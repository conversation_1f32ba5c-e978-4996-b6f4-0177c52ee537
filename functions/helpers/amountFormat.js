const { paiseToRupee, rupeeToPaise } = require("@/utils/money");

const formatAmountToPaise = (item) => {
  return {
    grossAmount: rupeeToPaise(item.grossAmount),
    totalDiscount: rupeeToPaise(item.totalDiscount),
    netAmount: rupeeToPaise(item.netAmount),
    charges: item.charges,
    totalChargeAmount: rupeeToPaise(item.totalChargeAmount),
    taxes: item.taxes,
    totalTaxAmount: rupeeToPaise(item.totalTaxAmount),
    totalAmount: rupeeToPaise(item.totalAmount),
    totalCess: rupeeToPaise(item.totalCess),
    totalFocAmount: rupeeToPaise(item.totalFocAmount),
    roundoff: rupeeToPaise(item.roundoff),
  };
};

const formatAmountToRupee = (item, format = true) => {
  return {
    grossAmount: format ? paiseToRupee(item.grossAmount) : item.grossAmount,
    totalDiscount: format
      ? paiseToRupee(item.totalDiscount)
      : item.totalDiscount,
    netAmount: format ? paiseToRupee(item.netAmount) : item.netAmount,
    totalChargeAmount: format
      ? paiseToRupee(item.totalChargeAmount)
      : item.totalChargeAmount,
    totalTaxAmount: format
      ? paiseToRupee(item.totalTaxAmount)
      : item.totalTaxAmount,
    totalAmount: format ? paiseToRupee(item.totalAmount) : item.totalAmount,
    totalFocAmount: format
      ? paiseToRupee(item.totalFocAmount)
      : item.totalFocAmount,
    totalCess: format ? paiseToRupee(item.totalCess) : item.totalCess,
    charges: item.charges,
    taxes: item.taxes,
    roundoff: format ? paiseToRupee(item.roundoff) : item.roundoff,
  };
};

module.exports = {
  formatAmountToPaise,
  formatAmountToRupee,
};
