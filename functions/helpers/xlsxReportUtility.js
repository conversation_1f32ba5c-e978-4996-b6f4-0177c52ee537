const ExcelJS = require("exceljs");

const borderStyle = Object.freeze({
  style: "thin",
  color: {
    argb: "FFCCCCCC",
  },
});
const _meta = Object.freeze({
  fill: {
    type: "pattern",
    pattern: "solid",
    fgColor: {
      argb: "FFD9D9D9",
    },
  },
  border: {
    top: borderStyle,
    bottom: borderStyle,
    left: borderStyle,
    right: borderStyle,
  },
});

// ui style alignment to excel alignment
const getAlignment = (align) => {
  const horizontal =
    align == "end" ? "right" : align == "center" ? "center" : "left";

  return {
    horizontal,
    vertical: "middle",
  };
};

const numberToExcelColumn = (n) => {
  let result = "";
  while (n > 0) {
    n--; // Excel is 1-based, computers are not
    result = String.fromCharCode(65 + (n % 26)) + result;
    n = Math.floor(n / 26);
  }
  return result;
};

/**
 * Adds meta information to the top of an Excel sheet.
 * @param {ExcelJS.Worksheet} sheet - The Excel worksheet to add meta information to.
 * @param {string} name - The name of the report to display at the top of the sheet.
 * @param {string} reportRange - The date range of the report to display at the top of the sheet.
 */
const addReportMeta = (sheet, result) => {
  const { name, payload, headers } = result;
  // Start placing meta rows from the top of the sheet
  let currentRow = 1;
  const mergeTo = numberToExcelColumn(headers.length);

  // Helper to add a centered, merged row with font and height settings
  const addRow = (text, font = {}) => {
    const rowIndex = currentRow++;
    const cellAddr = `A${rowIndex}`;

    // Merge across given columns (A to mergeTo)
    sheet.mergeCells(`${cellAddr}:${mergeTo}${rowIndex}`);

    // Set value, font, alignment, and height
    const cell = sheet.getCell(cellAddr);
    cell.value = text;
    cell.font = { size: 10, bold: true, ...font };
    cell.alignment = { horizontal: "center", vertical: "middle" };
    sheet.getRow(rowIndex).height = 20;
  };

  // Current timestamp for "Generated On" info
  const now = new Date();
  const f = { dateStyle: "medium" };
  const generatedDate = now.toLocaleString("en-IN", {
    ...f,
    hourCycle: "h24",
    timeStyle: "short",
  });
  const startDate = new Date(payload.filters.fromDate).toLocaleString(
    "en-IN",
    f,
  );
  const endDate = new Date(payload.filters.toDate).toLocaleString("en-IN", f);
  const reportRange = `${startDate} - ${endDate}`;

  // Add report name
  addRow(name);

  // Add generated and report date range info
  addRow(`Generated On: ${generatedDate} | Period: ${reportRange}`, {});

  return currentRow;
};

const formatHeader = (sheet, currentRow, headers) => {
  // Shift header row down (meta uses 2 rows)
  let headerRowIndex = currentRow + 1; // leave one blank line for spacing
  const headerRow = sheet.getRow(headerRowIndex);
  const labels = headers.map((h) => h.header);
  // Manually insert header labels at row 4
  headerRow.values = labels;

  headerRow.font = { size: 10, bold: true };
  headerRow.height = 25;
  headerRow.alignment = { vertical: "middle", indent: 1 };
  headerRow.eachCell((cell, colNumber) => {
    cell.fill = _meta.fill;
    cell.border = _meta.border;
    cell.alignment = getAlignment(headers[colNumber - 1]?.align);
  });
  return headerRowIndex;
};

/**
 * Writes a block of data to an Excel sheet.
 * @param {ExcelJS.Worksheet} sheet - The Excel worksheet to write to.
 * @param {Object[]} data - The data to write to the sheet.
 * @param {number} startRow - The starting row to write to.
 * @param {boolean} [expand=false] - If true, treats data as a single group and writes to the sheet using `sheet.addRows`. If false, writes each item in the data array as a separate row.
 */
const writeBlock = (sheet, headers, data, expand = false) => {
  if (!expand) {
    sheet.addRows(data);
    return;
  }

  const fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: {
      argb: "FFF7F7F7",
    },
  };

  data.forEach((d) => {
    const row = sheet.addRow(d);
    row.font = { size: 10, bold: true };
    row.height = 20;
    row.alignment = { vertical: "middle", indent: 1 };
    // Loop through columns based on headers count
    for (let col = 1; col <= headers.length; col++) {
      const cell = row.getCell(col);

      if (cell.value === undefined || cell.value === null) {
        cell.value = ""; // only for formatting in this row
      }

      cell.fill = fill;
      cell.border = _meta.border;
    }

    sheet.addRows(d.subItems || []);
  });
};

const writeTotalRow = (sheet, headers, totalRow) => {
  if (!totalRow) return;
  // Check if totalRow is an empty object
  if (!Array.isArray(totalRow) && Object.keys(totalRow).length === 0) {
    return;
  }

  // Check if totalRow is an empty array
  if (Array.isArray(totalRow) && totalRow.length === 0) {
    return;
  }

  // Handle totalRow as array (multiple total rows)
  if (Array.isArray(totalRow)) {
    totalRow.forEach((totalRowItem, index) => {
      // Create row data from the totalRow item
      const rowData = headers.map((h) => {
        const value = totalRowItem[h.key];
        if (value === "" || value === null || value === undefined) {
          return 0;
        }
        return value;
      });
      const row = sheet.addRow(rowData);

      row.font = { size: 10, bold: true };
      row.height = 20;
      row.alignment = { vertical: "middle", indent: 1 };

      if (index === 0) {
        row.getCell(1).value = "Total";
      }

      for (let col = 1; col <= headers.length; col++) {
        const cell = row.getCell(col);
        cell.fill = _meta.fill;
        cell.border = _meta.border;
        cell.alignment = getAlignment(headers[col - 1]?.align);
      }
    });
  } else {
    const row = sheet.addRow(totalRow);
    row.font = { size: 10, bold: true };
    row.height = 20;
    row.alignment = { vertical: "middle", indent: 1 };
    const cell = row.getCell(1);
    cell.value = "Total";
    for (let col = 1; col <= headers.length; col++) {
      const cell = row.getCell(col);
      if (cell.value === undefined || cell.value === null) {
        cell.value = ""; // only for formatting in this row
      }
      cell.fill = _meta.fill;
      cell.border = _meta.border;
    }
  }
};

/**
 * Create a XLSX report.
 *
 * @param {string} id - Type or name of the report.
 * @param {Array<Object>} headers - Column definitions [{ header, key }].
 * @param {Array<Object>} data - Data rows.
 * @param {Object|Array<Object>} [totalRow] - Optional total row.
 * @returns {Promise<Buffer>} XLSX buffer.
 */
async function createXlsxReport(result) {
  const { name, headers, data = [], totalRow = {}, expand } = result;

  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet(name);

  // Define columns only for key mapping
  sheet.columns = headers;

  // add reports meat
  let currentRow = addReportMeta(sheet, result);

  // add headers
  currentRow = formatHeader(sheet, currentRow, headers);

  // Add data rows
  writeBlock(sheet, headers, data, expand);

  writeTotalRow(sheet, headers, totalRow);

  // Return workbook buffer
  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
}

module.exports = { createXlsxReport };
