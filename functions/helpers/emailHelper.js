const nodemailer = require("nodemailer");
const fs = require("fs");
const path = require("path");
const { emailConfig } = require("@/helpers/config");

function buildEmailTemplate(data, templateName) {  

  const templatePath = path.join(
    __dirname,
    `../helpers/templates/${templateName}`
  );

  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template not found.`);
  }

  let html = fs.readFileSync(templatePath, "utf-8");

  return html
    .replace(
      "__PURCHASEREQUESTDATE__",
      data.po?.requestedTime
        ? new Date(data.po.requestedTime).toLocaleString() // @validate date
        : ""
    )
    .replace("__PURCHASEREQUESTID__", data.po?.poNumber || "");
}

function buildMailOptions({ pdf: buffer, subject, htmlTemplate, type, recipients }) {
  return {
    from: emailConfig.smtp.defaultFrom,
    to: recipients,
    subject,
    html: htmlTemplate,
    attachments: buffer
      ? [
          {
            filename: `${type}.pdf`,
            content: buffer,
          },
        ]
      : [],
  };
}

async function sendMail(mailOptions) {
  const transporter = nodemailer.createTransport(emailConfig.smtp);
  return transporter.sendMail(mailOptions);
}

module.exports = {
  buildEmailTemplate,
  buildMailOptions,
  sendMail
};
