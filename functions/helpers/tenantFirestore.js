const { COLLECTIONS } = require("@/defs/collectionDefs");
const { getFirestore } = require("firebase-admin/firestore");

const db = getFirestore();

/**
 * Returns tenant root document reference
 * Path: tenants/{tenantId}
 *
 * Usage:
 *   // Update tenant-level metadata
 *   await tenantDoc(tenantId).update({
 *     lastSeenAt: new Date()
 *   });
 *
 *   // Read tenant document
 *   const snap = await tenantDoc(tenantId).get();
 */
function tenantDoc(tenantId) {
  if (!tenantId || typeof tenantId !== "string") {
    throw new Error("tenantId is mandatory");
  }

  return db
    .collection(COLLECTIONS.TENANTS)
    .doc(tenantId);
}

/**
 * Returns tenant-scoped collection reference
 * Path: tenants/{tenantId}/{collectionPath}
 *
 * Usage:
 *   // Query tenant data
 *   const snap = await tenantCollection(tenantId, COLLECTIONS.INVENTORY)
 *     .where("businessDate", "==", businessDate)
 *     .orderBy("createdAt", "desc")
 *     .limit(50)
 *     .get();
 *
 *   // Add a document under tenant
 *   await tenantCollection(tenantId, "logs").add({
 *     type: "SYNC",
 *     createdAt: new Date()
 *   });
 *
 *   // Access a specific document under tenant collection
 *   const docSnap = await tenantCollection(tenantId, "orders")
 *     .doc(orderId)
 *     .get();
 *
 *   // Update a specific document
 *   await tenantCollection(tenantId, "orders")
 *     .doc(orderId)
 *     .update({ status: "COMPLETED" });
 */
function tenantCollection(tenantId, collectionPath) {
  return tenantDoc(tenantId).collection(collectionPath);
}

module.exports = {
  tenantDoc,
  tenantCollection
};
