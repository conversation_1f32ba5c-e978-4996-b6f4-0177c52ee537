"use strict";
// api/render.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResultType = void 0;
exports.renderJSON = renderJSON;
exports.renderCSV = renderCSV;
exports.renderXLSX = renderXLSX;
exports.renderPDF = renderPDF;
exports.render = render;
exports.redirect = redirect;
/** -----------------------------------
 * Result Types Definition
 * ----------------------------------- */
exports.ResultType = {
    JSON: 'json',
    EXCEL: 'xlsx',
    CSV: 'csv',
    PDF: 'pdf',
};
/** -----------------------------------
 * JSON Response
 * ----------------------------------- */
function renderJSON(res, status, data) {
    res.status(status).json(data);
}
/** -----------------------------------
 * CSV Response
 * ----------------------------------- */
function renderCSV(res, csvBuf, fileName) {
    fileName = getReportFileNameIfNot(fileName, 'csv');
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', "attachment; filename=\"".concat(fileName, "\""));
    res.status(200);
    if (typeof csvBuf === 'string' || csvBuf instanceof Buffer) {
        res.send(csvBuf);
    }
    else {
        csvBuf.pipe(res);
    }
}
/** -----------------------------------
 * XLSX Response
 * ----------------------------------- */
function renderXLSX(res, xlsxBuf, fileName) {
    fileName = getReportFileNameIfNot(fileName, 'xlsx');
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', "attachment; filename=\"".concat(fileName, "\""));
    res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    res.status(200);
    if (xlsxBuf instanceof Buffer) {
        res.send(xlsxBuf);
    }
    else {
        xlsxBuf.pipe(res);
    }
}
/** -----------------------------------
 * PDF Response (supports inline mode)
 * ----------------------------------- */
function renderPDF(res, pdfBuf, fileName, inline) {
    if (inline === void 0) { inline = false; }
    fileName = getReportFileNameIfNot(fileName, 'pdf');
    res.setHeader('Content-Type', 'application/pdf');
    var disposition = inline ? 'inline' : 'attachment';
    res.setHeader('Content-Disposition', "".concat(disposition, "; filename=\"").concat(fileName, "\""));
    res.status(200);
    if (pdfBuf instanceof Buffer) {
        res.send(pdfBuf);
    }
    else {
        pdfBuf.pipe(res);
    }
}
/** -----------------------------------
 * Generic Render Function
 * Handles JSON, CSV, XLSX, PDF
 * ----------------------------------- */
function render(res, type, data, fileName, status, inlinePDF) {
    if (status === void 0) { status = 200; }
    if (inlinePDF === void 0) { inlinePDF = false; }
    // fallback for invalid type
    if (!Object.values(exports.ResultType).includes(type)) {
        type = exports.ResultType.JSON;
    }
    switch (type) {
        case exports.ResultType.EXCEL:
            return renderXLSX(res, data, fileName);
        case exports.ResultType.CSV:
            return renderCSV(res, data, fileName);
        case exports.ResultType.PDF:
            return renderPDF(res, data, fileName, inlinePDF);
        default:
            return renderJSON(res, status, data);
    }
}
/** -----------------------------------
 * Redirect (303 - See Other)
 * ----------------------------------- */
function redirect(res, url) {
    res.redirect(303, url);
}
/** -----------------------------------
 * Helper: Default Filename Generator
 * ----------------------------------- */
function getReportFileNameIfNot(fileName, ext) {
    if (ext === void 0) { ext = 'csv'; }
    if (!fileName) {
        var today = new Date().toISOString().replace(/[-:.TZ]/g, '');
        fileName = "report".concat(today);
    }
    if (!fileName.endsWith(".".concat(ext))) {
        fileName = "".concat(fileName, ".").concat(ext);
    }
    return fileName;
}
