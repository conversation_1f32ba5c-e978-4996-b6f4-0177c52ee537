// api/render.ts

import { Response } from 'express';
import { Readable } from 'stream';

/** -----------------------------------
 * Result Types Definition
 * ----------------------------------- */
export const ResultType = {
  JSON: 'json',
  EXCEL: 'xlsx',
  CSV: 'csv',
  PDF: 'pdf',
} as const;

export type ResultTypeKey = (typeof ResultType)[keyof typeof ResultType];

/** -----------------------------------
 * JSON Response
 * ----------------------------------- */
export function renderJSON(res: Response, status: number, data: any) {
  res.status(status).json(data);
}

/** -----------------------------------
 * CSV Response
 * ----------------------------------- */
export function renderCSV(res: Response, csvBuf: Readable | Buffer | string, fileName?: string) {
  fileName = getReportFileNameIfNot(fileName, 'csv');

  res.setHeader('Content-Type', 'text/csv; charset=utf-8');
  res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
  res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
  res.status(200);

  if (typeof csvBuf === 'string' || csvBuf instanceof Buffer) {
    res.send(csvBuf);
  } else {
    csvBuf.pipe(res);
  }
}

/** -----------------------------------
 * XLSX Response
 * ----------------------------------- */
export function renderXLSX(res: Response, xlsxBuf: Readable | Buffer, fileName?: string) {
  fileName = getReportFileNameIfNot(fileName, 'xlsx');

  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
  res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
  res.status(200);

  if (xlsxBuf instanceof Buffer) {
    res.send(xlsxBuf);
  } else {
    xlsxBuf.pipe(res);
  }
}

/** -----------------------------------
 * PDF Response (supports inline mode)
 * ----------------------------------- */
export function renderPDF(
  res: Response,
  pdfBuf: Readable | Buffer,
  fileName?: string,
  inline: boolean = false
) {
  fileName = getReportFileNameIfNot(fileName, 'pdf');

  res.setHeader('Content-Type', 'application/pdf');
  const disposition = inline ? 'inline' : 'attachment';
  res.setHeader('Content-Disposition', `${disposition}; filename="${fileName}"`);
  res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
  res.status(200);

  if (pdfBuf instanceof Buffer) {
    res.send(pdfBuf);
  } else {
    pdfBuf.pipe(res);
  }
}

/** -----------------------------------
 * Generic Render Function
 * Handles JSON, CSV, XLSX, PDF
 * ----------------------------------- */
export function render(
  res: Response,
  type: ResultTypeKey,
  data: any,
  fileName?: string,
  status: number = 200,
  inlinePDF: boolean = false
) {
  // fallback for invalid type
  if (!Object.values(ResultType).includes(type)) {
    type = ResultType.JSON;
  }

  switch (type) {
    case ResultType.EXCEL:
      return renderXLSX(res, data, fileName);
    case ResultType.CSV:
      return renderCSV(res, data, fileName);
    case ResultType.PDF:
      return renderPDF(res, data, fileName, inlinePDF);
    default:
      return renderJSON(res, status, data);
  }
}

/** -----------------------------------
 * Redirect (303 - See Other)
 * ----------------------------------- */
export function redirect(res: Response, url: string) {
  res.redirect(303, url);
}

/** -----------------------------------
 * Helper: Default Filename Generator
 * ----------------------------------- */
function getReportFileNameIfNot(fileName?: string, ext: string = 'csv') {
  if (!fileName) {
    const today = new Date().toISOString().replace(/[-:.TZ]/g, '');
    fileName = `report${today}`;
  }

  if (!fileName.endsWith(`.${ext}`)) {
    fileName = `${fileName}.${ext}`;
  }

  return fileName;
}
