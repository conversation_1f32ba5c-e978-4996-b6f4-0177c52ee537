const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();

/**
 * QueryBuilder
 *
 * Firestore query helper that adds:
 *  - `.whereIf()`   → conditionally adds filter if value is valid
 *  - `.whereMust()` → adds required filter, throws if invalid
 */
class QueryBuilder {
  constructor(collectionPath) {
    this.query = db.collection(collectionPath);
  }

  /**
   * Adds a `where` clause only if the value is valid.
   */
  whereIf(field, op, value) {
    if (this.#isValid(value)) {
        return this.#where(field, op, value);
    }
    return this;
  }

  /**
   * Adds a mandatory `where` clause.
   * Throws if the value is invalid.
   */
  whereMust(field, op, value) {
    if (!this.#isValid(value)) {
      throw new Error(`Missing or invalid value for mandatory filter: "${field}"`);
    }
    return this.#where(field, op, value);
  }

  #where(field, op, value) {
    this.query = this.query.where(field, op, value);
    return this;
  }

  #isValid(val) {
    if (val === undefined || val === null) return false;
    if (Array.isArray(val)) return val.length > 0;
    if (typeof val === "string") return val.trim() !== "";
    return true;
  }
}

// Inherit Firestore Query prototype methods
Object.setPrototypeOf(QueryBuilder.prototype, db.collection("_tmp").__proto__);

module.exports = QueryBuilder;
