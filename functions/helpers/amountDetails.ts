// helpers/amountDetails.ts
/**
 * AmountDetails Module for Inventory/POS in India
 *
 * Stores amounts in **minor units (paise)** internally for accuracy.
 * Supports **item-level charges** (packing, freight) and **taxes** (CGST, SGST, IGST).
 * Automatically calculates **gross amount, net amounts, total discounts, total taxes, item total**.
 * Supports **unit price and quantity** for inventory calculations.
 * Provides **formatting with 3 decimals** for display.
 * Supports **merging multiple items** into one invoice.
 *
 * @example
 * ```ts
 * import { AmountDetails, AmountDetailsData } from './amountDetails';
 *
 * const item1: AmountDetailsData = {
 *   unitPrice: 50000,    // 500.00 per unit
 *   quantity: 2,          // 2 units
 *   itemDiscount: 5000,
 *   checkDiscount: 0,
 *   externalDiscount: 0,
 *   totalDiscount: 0,
 *   netAmount: 0,
 *   charges: [],
 *   itemCharge: 0,
 *   taxes: [],
 *   totalItemTax: 0,
 *   itemChargeTax: 0,
 *   totalTax: 0,
 *   itemTotal: 0,
 * };
 *
 * // Calculate and format a single item
 * const amt1 = new AmountDetails(item1).format();
 *
 * // Add charges and GST
 * const amt2 = new AmountDetails(item1)
 *   .addCharge('Packing', 2000, [
 *     { id: 1, name: 'CGST', amount: 180, percentage: 9 },
 *     { id: 2, name: 'SGST', amount: 180, percentage: 9 },
 *   ])
 *   .applyGST('intra', 18)
 *   .format();
 *
 * // Merge multiple items into one invoice
 * const item2 = { ...item1, quantity: 1, itemDiscount: 2000 };
 * const invoice = new AmountDetails(item1)
 *   .add(item2)
 *   .addCharge('Freight', 1000, [
 *     { id: 1, name: 'CGST', amount: 45, percentage: 9 },
 *     { id: 2, name: 'SGST', amount: 45, percentage: 9 },
 *   ])
 *   .applyGST('intra', 18)
 *   .format();
 * ```
 */

import cloneDeep from "lodash.clonedeep";
import Joi from "joi";
import Dinero from "dinero.js";

// ---------------------
// Interfaces
// ---------------------

/** Tax interface */
export interface Tax {
  /** Unique tax identifier */
  id: number;
  /** Tax name (CGST, SGST, IGST) */
  name: string;
  /** Tax amount in minor units (paise) */
  amount: number;
  /** Tax rate in percent */
  percentage: number;
  /** Formatted string for display */
  amountFmt?: string;
}

/** Charge interface (packing, freight) */
export interface Charge {
  /** Unique charge identifier */
  id: number;
  /** Charge name */
  name: string;
  /** Charge amount in minor units */
  amount: number;
  /** Taxes applied to this charge */
  taxes: Tax[];
  /** Formatted string for display */
  amountFmt?: string;
}

/** AmountDetailsData interface for a single item or invoice */
export interface AmountDetailsData {
  /** Total item amount before discounts, computed as unitPrice × quantity */
  grossAmount: number;
  /** Price per unit in minor units */
  unitPrice: number;
  /** Number of units */
  quantity: number;
  /** Discount applied at item level */
  itemDiscount: number;
  /** Discount applied at check level */
  checkDiscount: number;
  /** External discount (coupon, promotion) */
  externalDiscount: number;
  /** Sum of all discounts */
  totalDiscount: number;
  /** Net amount = grossAmount - totalDiscount */
  netAmount: number;

  /** Additional charges (packing, freight) */
  charges: Charge[];
  /** Sum of all charges */
  itemCharge: number;

  /** Item-level taxes */
  taxes: Tax[];
  /** Sum of item taxes */
  totalItemTax: number;
  /** Sum of charge taxes */
  itemChargeTax: number;
  /** totalItemTax + itemChargeTax */
  totalTax: number;

  /** Final total = netAmount + itemCharge + totalTax */
  itemTotal: number;
  /** Formatted amounts at same level */
  formats?: Record<string, string>;
}

// ---------------------
// Joi Schemas
// ---------------------
export const TaxSchema = Joi.object<Tax>({
  id: Joi.number().integer().required(),
  name: Joi.string().required(),
  amount: Joi.number().integer().required(),
  percentage: Joi.number().precision(2).required(),
  amountFmt: Joi.string().optional(),
});

export const ChargeSchema = Joi.object<Charge>({
  id: Joi.number().integer().required(),
  name: Joi.string().required(),
  amount: Joi.number().integer().required(),
  amountFmt: Joi.string().optional(),
  taxes: Joi.array().items(TaxSchema).default([]),
});

export const AmountDetailsSchema = Joi.object<AmountDetailsData>({
  unitPrice: Joi.number().integer().default(0),
  quantity: Joi.number().integer().default(1),
  grossAmount: Joi.number().integer().default(0),
  itemDiscount: Joi.number().integer().default(0),
  checkDiscount: Joi.number().integer().default(0),
  externalDiscount: Joi.number().integer().default(0),
  totalDiscount: Joi.number().integer().default(0),
  netAmount: Joi.number().integer().default(0),
  charges: Joi.array().items(ChargeSchema).default([]),
  itemCharge: Joi.number().integer().default(0),
  taxes: Joi.array().items(TaxSchema).default([]),
  totalItemTax: Joi.number().integer().default(0),
  itemChargeTax: Joi.number().integer().default(0),
  totalTax: Joi.number().integer().default(0),
  itemTotal: Joi.number().integer().default(0),
  formats: Joi.object().optional(),
});

// ---------------------
// Helpers
// ---------------------
const dineroFrom = (amount: number) => Dinero({ amount, precision: 3 });
const formatMoney = (value: number) => dineroFrom(value).toUnit().toFixed(3);

// ---------------------
// AmountDetails Class
// ---------------------
export class AmountDetails {
  private data: AmountDetailsData;

  /**
   * Create a new AmountDetails instance
   * @param initial - optional initial data
   */
  constructor(initial?: Partial<AmountDetailsData>) {
    this.data = cloneDeep({
      grossAmount: 0,
      unitPrice: 0,
      quantity: 1,
      itemDiscount: 0,
      checkDiscount: 0,
      externalDiscount: 0,
      totalDiscount: 0,
      netAmount: 0,
      charges: [],
      itemCharge: 0,
      taxes: [],
      totalItemTax: 0,
      itemChargeTax: 0,
      totalTax: 0,
      itemTotal: 0,
      ...initial,
    });
  }

  /**
   * Calculate totals for a single item
   * - grossAmount = unitPrice × quantity
   * - totalDiscount = itemDiscount + checkDiscount + externalDiscount
   * - netAmount = grossAmount - totalDiscount
   * - totalTax = totalItemTax + itemChargeTax
   * - itemTotal = netAmount + itemCharge + totalTax
   */
  private calculate(amt: AmountDetailsData): AmountDetailsData {
    const grossAmount = amt.unitPrice * amt.quantity;
    const totalDiscount =
      amt.itemDiscount + amt.checkDiscount + amt.externalDiscount;
    const netAmount = grossAmount - totalDiscount;
    const totalTax = amt.totalItemTax + amt.itemChargeTax;
    const itemTotal = netAmount + amt.itemCharge + totalTax;

    return {
      ...amt,
      grossAmount,
      totalDiscount,
      netAmount,
      totalTax,
      itemTotal,
    };
  }

  /**
   * Merge two arrays of taxes by name
   */
  private mergeTaxes(existingTaxes: Tax[], newTaxes: Tax[]): Tax[] {
    const merged = cloneDeep(existingTaxes);
    newTaxes.forEach((nt) => {
      const existing = merged.find((t) => t.name === nt.name);
      if (existing) existing.amount += nt.amount;
      else merged.push(cloneDeep(nt));
    });
    return merged;
  }

  /**
   * Add a charge to this item
   */
  public addCharge(name: string, amount: number, taxes: Tax[] = []): this {
    const existingCharge = this.data.charges.find((c) => c.name === name);
    if (existingCharge) {
      existingCharge.amount += amount;
      existingCharge.taxes = this.mergeTaxes(existingCharge.taxes, taxes);
    } else this.data.charges.push({ id: Date.now(), name, amount, taxes });
    this.data.itemCharge += amount;
    this.data.itemChargeTax += taxes.reduce((acc, t) => acc + t.amount, 0);
    return this;
  }

  /**
   * Apply GST for India
   */
  public applyGST(state: "intra" | "inter", rate: number): this {
    if (state === "intra") {
      const cgst = Math.floor((this.data.netAmount * rate) / 200);
      const sgst = cgst;
      this.data.taxes.push({
        id: 1,
        name: "CGST",
        amount: cgst,
        percentage: rate / 2,
      });
      this.data.taxes.push({
        id: 2,
        name: "SGST",
        amount: sgst,
        percentage: rate / 2,
      });
      this.data.totalItemTax += cgst + sgst;
    } else {
      const igst = Math.floor((this.data.netAmount * rate) / 100);
      this.data.taxes.push({
        id: 3,
        name: "IGST",
        amount: igst,
        percentage: rate,
      });
      this.data.totalItemTax += igst;
    }
    return this;
  }

  /**
   * Add another AmountDetails or raw data to this instance
   */
  public add(incoming: AmountDetailsData | AmountDetails): this {
    const incData =
      incoming instanceof AmountDetails ? incoming.getData() : incoming;
    const inc = this.calculate(cloneDeep(incData));
    const t = cloneDeep(this.data);

    let newCharges = [...t.charges];
    let newTaxes = cloneDeep(t.taxes);

    inc.charges.forEach((ch) => {
      const existingCharge = newCharges.find((c) => c.name === ch.name);
      if (existingCharge) {
        existingCharge.amount += ch.amount;
        existingCharge.taxes = this.mergeTaxes(existingCharge.taxes, ch.taxes);
      } else newCharges.push(cloneDeep(ch));
      newTaxes = this.mergeTaxes(newTaxes, ch.taxes);
    });

    newTaxes = this.mergeTaxes(newTaxes, inc.taxes);

    this.data = {
      unitPrice: t.unitPrice + inc.unitPrice,
      quantity: t.quantity + inc.quantity,
      grossAmount: t.grossAmount + inc.grossAmount,
      itemDiscount: t.itemDiscount + inc.itemDiscount,
      checkDiscount: t.checkDiscount + inc.checkDiscount,
      externalDiscount: t.externalDiscount + inc.externalDiscount,
      totalDiscount: t.totalDiscount + inc.totalDiscount,
      netAmount: t.netAmount + inc.netAmount,
      itemCharge: t.itemCharge + inc.itemCharge,
      charges: newCharges,
      taxes: newTaxes,
      totalItemTax: t.totalItemTax + inc.totalItemTax,
      itemChargeTax: t.itemChargeTax + inc.itemChargeTax,
      totalTax: t.totalTax + inc.totalTax,
      itemTotal: t.itemTotal + inc.itemTotal,
      formats: undefined,
    };

    return this;
  }

  /**
   * Format all amounts and taxes into strings with 3 decimals
   */
  public format(): Record<string, string> {
    const amt = this.data;
    const fmt: Record<string, string> = {
      grossAmountFmt: formatMoney(amt.grossAmount),
      itemDiscountFmt: formatMoney(amt.itemDiscount),
      checkDiscountFmt: formatMoney(amt.checkDiscount),
      externalDiscountFmt: formatMoney(amt.externalDiscount),
      totalDiscountFmt: formatMoney(amt.totalDiscount),
      netAmountFmt: formatMoney(amt.netAmount),
      itemChargeFmt: formatMoney(amt.itemCharge),
      totalItemTaxFmt: formatMoney(amt.totalItemTax),
      itemChargeTaxFmt: formatMoney(amt.itemChargeTax),
      totalTaxFmt: formatMoney(amt.totalTax),
      itemTotalFmt: formatMoney(amt.itemTotal),
    };

    amt.taxes = amt.taxes.map((t) => ({
      ...t,
      amountFmt: formatMoney(t.amount),
    }));
    amt.charges = amt.charges.map((c) => ({
      ...c,
      amountFmt: formatMoney(c.amount),
      taxes: c.taxes.map((t) => ({ ...t, amountFmt: formatMoney(t.amount) })),
    }));

    this.data.formats = fmt;
    return fmt;
  }

  /** Get a deep-cloned copy of raw data */
  public getData(): AmountDetailsData {
    return cloneDeep(this.data);
  }

  /** Merge an array of items into one AmountDetails instance */
  public static mergeAll(
    items: (AmountDetails | AmountDetailsData)[]
  ): AmountDetails {
    return items.reduce<AmountDetails>((acc, curr) => {
      return acc.add(curr);
    }, new AmountDetails());
  }
}
