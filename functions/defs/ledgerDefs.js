// defs/ledgerDefs.js

/**
 * LedgerTypes
 * -----------
 * Represents HOW stock moved.
 * Direction is determined by qty sign (+ / -).
 * Intent is determined by reasonCode.
 */
const LedgerTypes = Object.freeze({
  /**
   * GRN
   * ---
   * Goods Received Note.
   * Stock received from a supplier.
   * Always qty-positive.
   */
  GRN: "GRN",

  /**
   * TRANSFER_IN
   * -----------
   * Stock received from another inventory location.
   * Always qty-positive.
   */
  TRANSFER_IN: "TRANSFER-IN",

  /**
   * TRANSFER_OUT
   * ------------
   * Stock sent to another inventory location.
   * Always qty-negative.
   */
  TRANSFER_OUT: "TRANSFER-OUT",

  /**
   * CONSUMPTION
   * -----------
   * Stock consumed internally (sales).
   * Always qty-negative.
   */
  CONSUMPTION: "CONSUMPTION",

  /**
   * PREPARE_CONSUMPTION
   * -----------
   * Stock consumed internally (production, recipes).
   * Always qty-negative.
   */
  PREPARE_CONSUMPTION: "PREPARE_CONSUMPTION",
  /**
   * PREPARATION
   * -----------
   * Made Item Stock produced
   */
  PREPARATION: "PREPARATION",

  /**
   * SPOILAGE
   * --------
   * Stock written off due to damage / expiry.
   * Always qty-negative.
   */
  SPOILAGE: "SPOILAGE",

  /**
   * ADJUSTMENT
   * ----------
   * Generic stock correction.
   * Used for:
   * - physical stock overwrite
   * - manual correction
   * - system repair
   *
   * Direction depends on qty sign.
   */
  ADJUSTMENT: "ADJUSTMENT",

  /* ================================
   * Legacy / Unused (kept as-is)
   * ================================ */
  GRN_DELETE: "GRN-DELETE",
  GRN_EDIT_INCREASE: "GRN-EDIT-INCREASE",
  GRN_EDIT_DECREASE: "GRN-EDIT-DECREASE",
  GRN_EDIT_NOTE: "GRN-EDIT-NOTE",
  RETURN_VENDOR: "RETURN-TO-VENDOR",
  PRD: "PRODUCTION",
  ADJUSTMENT_CREDIT: "ADJUSTMENT-CREDIT",
  ADJUSTMENT_DEBIT: "ADJUSTMENT-DEBIT",
  CLOSING_CREDIT: "CLOSING-CREDIT",
  CLOSING_DEBIT: "CLOSING-DEBIT",
  MENU_RECIPE_CREDIT: "MENU-RECIPE-CREDIT",
  MENU_RECIPE_DEBIT: "MENU-RECIPE-DEBIT",
});

/**
 * StockTransactionType
 * --------------------
 * Direction of stock movement.
 * Mostly derived from qty sign.
 */
const StockTransactionType = Object.freeze({
  IN: "IN",
  OUT: "OUT",
});

/**
 * StockLedgerReasonCode
 * --------------------
 * WHY stock moved.
 * Values are UI-readable by design.
 */
const StockLedgerReasonCode = Object.freeze({
  PHYSICAL_STOCK_OVERRIDE: "Physical stock override",
  MANUAL_CORRECTION: "Manual stock correction",
  SYSTEM_REPAIR: "System repair / rebuild",
  GRN_EDIT: "GRN edit or reversal",
  GRN_DELETE: "GRN delete",
  RETURN_TO_VENDOR: "Return to vendor",
});

module.exports = {
  LedgerTypes,
  StockTransactionType,
  StockLedgerReasonCode,
};
