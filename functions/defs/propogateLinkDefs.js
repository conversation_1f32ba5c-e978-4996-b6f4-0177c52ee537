const PROPOGATE_LINKING_MODULES = {
    VENDOR: [
        "inventoryItems",
        "purchaseOrders",
        "GRNs",
        "contracts",
        "purchaseRequests"
    ],
    CATEGORY: [
        "inventoryItems"
    ],
    TAX: [
        "inventoryItems"
    ],
    HOUSE_UNIT: [
        "inventoryItems",
        "receipes",
        "closing", // id not present
        "spoilages", // id not present
        "stockLedgers", // id not present
        "stocks", // id not present
        "transfers" // id not present
    ],
    INVENTORY_ITEMS: [
        "receipes",
        "adjustInventory",
        "closing",
        "contracts",
        "modifiers",
        "purchaseOrders",
        "purchaseRequests",
        "spoilages",
        "stockLedgers",
        "stocks",
        "transfers"
    ],
    RECIPES: [],
    LOCATIONS:[
        "GRNs",
        "closing",
        "contracts",
        "purchaseOrders",
        "stocks",
        "spoilages",
        "purchaseRequests",
        "stockLedgers",
        "inventoryLocations"
    ],
    WORKAREAS:[
        "GRNs",
        "purchaseOrders",
        "stocks",
        "spoilages",
        "purchaseRequests",
        "stores",
        "stockLedgers",
    ]
};

module.exports = PROPOGATE_LINKING_MODULES;