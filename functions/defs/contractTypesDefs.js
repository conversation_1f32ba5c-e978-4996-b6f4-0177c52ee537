/**
 * Enum of all possible contract types
 */
const contractTypes = Object.freeze({
  FIXED: "fixed", // fixed contract price
  DISCOUNT: "discount", // discount on mrp
  ABSOLUTE: "absolute", // absolute price reduction on mrp
});

/**
 * Enum of all possible transfer statuses
 */
const contractStatus = Object.freeze({
  IDLE: "idle",
  ACTIVE: "active",
  EXPIRED: "expired",
  CLOSED: "closed",
});

module.exports = {
  contractTypes,
  contractStatus
};