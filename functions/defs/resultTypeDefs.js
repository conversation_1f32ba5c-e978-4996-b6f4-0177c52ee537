// defs/resultTypeDefs.js

/**
 * @typedef {("json"|"excel"|"csv"|"pdf")} ResultTypeKey
 * Represents the supported types of result/output across the application.
 *
 * - "json"  : Standard JSON object/array response
 * - "excel" : XLSX/Excel file export
 * - "csv"   : CSV file export
 * - "pdf"   : PDF file export
 */

/**
 * Generic result types across the application.
 * Use this constant wherever a function, API, or report can return
 * data in different formats.
 *
 * Example usage:
 * ```js
 * const { ResultTypes } = require("@/defs/resultTypeDefs");
 * const type = ResultType.EXCEL;
 * ```
 */
const ResultTypes = /** @type {Record<string, ResultTypeKey>} */ (Object.freeze({
  /** JSON object/array response */
  JSON: "json",

  /** Excel XLSX file export */
  EXCEL: "excel",

  /** CSV file export */
  CSV: "csv",

  /** PDF file export */
  PDF: "pdf"
}));

module.exports = { ResultTypes };
