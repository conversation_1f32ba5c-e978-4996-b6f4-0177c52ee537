NODE_ENV=production

# Firebase Admin (service account)
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=inventory-production-3b6ba
FIREBASE_KEY_ID=75252fc31623afb33a6411594b5f4371aea76323
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=107542762942004123639
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40inventory-production-3b6ba.iam.gserviceaccount.com
FIREBASE_UNIVERSE_DOMAIN=googleapis.com

AUTH_SERVER_URL="https://asia-south1-signhub-production.cloudfunctions.net/app"

# Firebase Client Config
FIREBASE_API_KEY=AIzaSyAubv68dItY6bwFo7cek-bERp44iu40FR0
FIREBASE_AUTH_DOMAIN=inventory-production-3b6ba.firebaseapp.com
FIREBASE_PROJECT_ID_CONFIG=inventory-production-3b6ba
FIREBASE_STORAGE_BUCKET=inventory-production-3b6ba.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:d709248814c4be0011342e
FIREBASE_MEASUREMENT_ID=G-FNEZ77XND6

JWT_SECRET=a56f888e807c51fe


BO_URI="https://rms-api.digitory.com/api/v1/inventory"
BO_APP_ID= "inventory"
BO_APP_CODE="ffd5ed2c-cebe-48de-9a44-76687394f22b"


#web application URI
WEB_APP_URI="https://inventory.digitory.com"
CLIENT_ID="inventory_prod"



