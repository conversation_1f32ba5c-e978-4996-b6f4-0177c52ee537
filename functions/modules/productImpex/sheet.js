// Utility functions for handling sheet-related operations:
// - validateSheetNames: ensures provided sheet names are valid
// - getHeaders: returns headers for a given sheet
// - expandSheetNames: returns expanded sheet names (aliases)
// - getData: returns mock data for a given sheet (extendable for real data)
const headers = require("./headers");

// Centralized sheet name definitions to avoid typos across the project
exports.SHEET_NAMES = {
  TAGS: "WorkArea Groups",
  LEDGERS: "Ledgers",
  TAXES: "Taxes",
  CHARGES: "Charges",
  VENDORS: "Vendors",
  CATEGORIES: "Categories",
  SUB_CATEGORIES: "Sub Categories",
  HOUSE_UNITS: "House Units",
  INVENTORY_ITEMS: "Inventory Items",
  INVENTORY_PACKAGES: "Inventory Packages",
  // INVENTORY_INGREDIENTS: "Inventory Ingredients",
  RECIPES: "Recipes",
  RECIPE_INGREDIENTS: "Recipe Ingredients",
};

const defaultSheets = [
  this.SHEET_NAMES.TAGS,
  this.SHEET_NAMES.LEDGERS,
  this.SHEET_NAMES.TAXES,
  this.SHEET_NAMES.CHARGES,
  this.SHEET_NAMES.VENDORS,
  this.SHEET_NAMES.CATEGORIES,
  this.SHEET_NAMES.HOUSE_UNITS,
  this.SHEET_NAMES.INVENTORY_ITEMS,
  this.SHEET_NAMES.RECIPES,
];

/**
 * Validates given sheet names (case-insensitive) and returns:
 * - formattedSheets: list of actual sheet names in proper casing
 * - invalidSheets: list of invalid sheet names
 * - message: error message if invalid sheets exist, otherwise null
 *
 * @param {string[]} sheets - Array of sheet names provided by the user
 * @returns {{ formattedSheets: string[], invalidSheets: string[], message: string|null }}
 */
exports.validateAndFormat = (sheets = []) => {
  if (sheets.length === 0) {
    return {
      isValid: true,
      formattedSheets: defaultSheets,
      invalidSheets: [],
      message: null,
    };
  }

  // Map lowercase → proper casing for easy comparison
  const validMap = defaultSheets.reduce((acc, name) => {
    acc[name.toLowerCase()] = name;
    return acc;
  }, {});

  const formattedSheets = [];
  const invalidSheets = [];

  sheets.forEach((sheet) => {
    const lower = sheet.toLowerCase();
    if (validMap[lower]) {
      formattedSheets.push(validMap[lower]);
    } else {
      invalidSheets.push(sheet);
    }
  });

  return {
    isValid: invalidSheets.length === 0,
    formattedSheets,
    invalidSheets,
    message:
      invalidSheets.length > 0
        ? `Invalid sheets: ${invalidSheets.join(
            ", "
          )}. Valid sheet names are: ${Object.values(this.SHEET_NAMES).join(
            ", "
          )}`
        : null,
  };
};

/**
 * Returns header column definitions for a given sheet name.
 *
 * @param {string} sheetName
 * @returns {string[]} - Array of column headers.
 * @throws {Error} If no headers are defined for the given sheet name.
 */
exports.getHeaders = (sheetName) => {
  switch (sheetName) {
    case this.SHEET_NAMES.TAGS:
      return headers.tagColumns;
    case this.SHEET_NAMES.LEDGERS:
      return headers.ledgerColumns;
    case this.SHEET_NAMES.TAXES:
      return headers.taxColumns;
    // case this.SHEET_NAMES.CHARGES:
    //   return headers.chargeColumns;
    case this.SHEET_NAMES.VENDORS:
      return headers.vendorColumns;
    case this.SHEET_NAMES.CATEGORIES:
      return headers.categoryColumns;
    case this.SHEET_NAMES.SUB_CATEGORIES:
      return headers.subCategoryColumns;
    case this.SHEET_NAMES.HOUSE_UNITS:
      return headers.houseUnitColumns;
    case this.SHEET_NAMES.INVENTORY_ITEMS:
      return headers.inventoryItemsColumns;
    case this.SHEET_NAMES.INVENTORY_PACKAGES:
      return headers.inventoryPackagesColumns;
    // case this.SHEET_NAMES.INVENTORY_INGREDIENTS:
    //   return headers.inventoryIngredientsColumns;
    case this.SHEET_NAMES.RECIPES:
      return headers.recipeColumns;
    case this.SHEET_NAMES.RECIPE_INGREDIENTS:
      return headers.recipeIngredientsColumns;
    case this.SHEET_NAMES.ROLES:
      return headers.roleColumns;
    default:
      throw new Error(`No headers defined for sheet: ${sheetName}`);
  }
};

/**
 * Expands a logical sheet name into one or more related worksheet names.
 * For example:
 * - "Inventory Items" → ["Inventory Items", "Inventory Packages", "Inventory Ingredients"]
 * - "Recipes" → ["Recipes", "Recipe Ingredients"]
 * - Default → returns the same name wrapped in an array.
 *
 * @param {string} sheetName
 * @returns {string[]}
 */
exports.expandSheetNames = (sheetName) => {
  switch (sheetName) {
    case this.SHEET_NAMES.CATEGORIES:
      return [this.SHEET_NAMES.CATEGORIES, this.SHEET_NAMES.SUB_CATEGORIES];
    case this.SHEET_NAMES.INVENTORY_ITEMS:
      return [
        this.SHEET_NAMES.INVENTORY_ITEMS,
        this.SHEET_NAMES.INVENTORY_PACKAGES,
        // this.SHEET_NAMES.INVENTORY_INGREDIENTS,
      ];
    case this.SHEET_NAMES.RECIPES:
      return [this.SHEET_NAMES.RECIPES, this.SHEET_NAMES.RECIPE_INGREDIENTS];
    default:
      return [sheetName];
  }
};
