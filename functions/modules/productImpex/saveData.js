const admin = require("firebase-admin");
const db = admin.firestore();
const { SHEET_NAMES } = require("./sheet");
const {
  getNextVendorId,
  // getNextInventoryItemId,
  // getNextRecipeId,
} = require("@/services/counterService");
const { COUNTER_TYPES } = require("@/defs/counterDefs");
const { DEFAULT_UNITS } = require("@/utils/defaultData");
const { rupeeToPaise, paiseToRupee } = require("@/utils/money");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { SERVICE_TYPES } = require("@/defs/serviceTypesDefs");

const vendorCollection = db.collection(COLLECTIONS.VENDORS);
const categoryCollection = db.collection(COLLECTIONS.CATEGORIES);
const tagCollection = db.collection(COLLECTIONS.TAGS);
const taxCollection = db.collection(COLLECTIONS.TAXES);
const houseUnitCollection = db.collection(COLLECTIONS.HOUSE_UNITS);
const inventoryItemCollection = db.collection(COLLECTIONS.INVENTORY_ITEMS);
const recipeCollection = db.collection(COLLECTIONS.RECEIPES);
const ledgerCollection = db.collection(COLLECTIONS.PRODUCT_LEDGERS);
const { getNextNumberRange } = require("@/repositories/counterRepo");
const { truncateNumber } = require("@/utils/money");

const vendorSchema = require("@/models/vendorSchema");
const categorySchema = require("@/models/categorySchema");
const houseUnitSchema = require("@/models/houseUnitSchema");
const inventoryItemSchema = require("@/models/inventoryItemSchema");
const tagSchema = require("@/models/tagSchema");
const ledgerSchema = require("@/models/productLedgerSchema");
const taxSchema = require("@/schema/taxSchema");
const { recipeSchema } = require("@/models/receipeSchema");
const {
  isDuplicateWithDefaultUnits,
} = require("@/controllers/houseUnitController");

// General parser for Excel data
const parseData = (value, defaultValue = "") => {
  return value !== undefined && value !== null
    ? String(value).trim()
    : defaultValue;
};

const parseBool = (value) => {
  return parseData(value).toUpperCase() === "TRUE";
};

const parseActiveStatus = (value) => {
  return parseData(value).toUpperCase() === "ACTIVE";
};

const parseNumber = (value) => {
  return Number(parseData(value)) || 0;
};

const formatName = (name) => name?.trim().replace(/\s+/g, "").toLowerCase();

// Vendors
const saveVendors = async (tenantId, data = {}, meta = {}) => {
  const rows = data[SHEET_NAMES.VENDORS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing vendors into a map (normalized name → ref)
  const vendorDocs = await vendorCollection
    .where("tenantId", "==", tenantId)
    .get();
  const vendorMap = vendorDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = doc.ref;
    return map;
  }, {});

  function formVendorData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      vendorId: parseData(row.vendorId),
      contactName: parseData(row.contactName),
      contactNo: parseData(row.contactNo),
      contactEmailId: parseData(row.contactEmailId),
      address: {
        country: parseData(row.country),
        address: parseData(row.address),
        state: parseData(row.state),
        city: parseData(row.city),
        pincode: parseData(row.pincode),
      },
      cinNo: parseData(row.cinNo),
      gstNo: parseData(row.gstNo),
      panNo: parseData(row.panNo),
      tinNo: parseData(row.tinNo),
      poTerms: parseData(row.poTerms),
      bankName: parseData(row.bankName),
      accountNumber: parseData(row.accountNumber),
      ifscCode: parseData(row.ifscCode),
      workOrderTerms: parseData(row.workorderTerms),
      paymentTerms: parseData(row.paymentTerms),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of rows.entries()) {
    const vendorData = formVendorData(row);

    if (!vendorData.vendorId) {
      vendorData.vendorId = await getNextVendorId(tenantId);
    }

    const docRef = vendorMap[vendorData.nameNormalized];

    // Validate vendorData using Joi
    const { error: validationError } = vendorSchema.validate(vendorData, {
      abortEarly: false,
    });

    if (validationError) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: vendorData.name || "Unknown Vendor",
            errors: validationError.details.map((d) => d.message),
          },
        ],
      });
      continue; // skip saving invalid row
    }

    // Proceed with saving valid row
    if (docRef) {
      batch.update(docRef, { id: docRef.id, ...vendorData });
    } else {
      const newDocRef = vendorCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...vendorData });
      vendorMap[vendorData.nameNormalized] = newDocRef;
    }

    opCount++;
    successCount++;

    if (opCount >= 500) {
      await commitBatch();
    }
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: rows.length,
    successCount,
    failureCount,
    sheetName: "Vendors",
  };

  if (errors.length) {
    console.error("Vendor import errors:", JSON.stringify(errors, null, 2));
  }

  return {
    logEntry,
  };
};

// Categories
const saveCategories = async (tenantId, data = {}) => {
  const categories = data[SHEET_NAMES.CATEGORIES] || [];
  const subCategories = data[SHEET_NAMES.SUB_CATEGORIES] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  const categoryDocs = await categoryCollection
    .where("tenantId", "==", tenantId)
    .get();

  const categoryMap = categoryDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = { ref: doc.ref, ...doc.data() };
    return map;
  }, {});

  const catMap = {};
  subCategories.forEach((res, index) => {
    const parent = parseData(res.category)?.toLowerCase();
    if (!parent) return;

    const subCatIns = { name: parseData(res.subCategory) };

    const catRef = categoryMap[formatName(res.category)];
    const subCatId = catRef?.subCategories?.find(
      (sub) => sub.name === subCatIns.name,
    )?.id;

    subCatIns.id = subCatId || admin.firestore().collection("dummy").doc().id;

    if (!catMap[parent]) {
      catMap[parent] = [];
    }
    catMap[parent].push(subCatIns);
  });

  function formCategoryData(row) {
    const key = parseData(row.name)?.toLowerCase();
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      isBarCategory: parseBool(row.isBarCategory),
      subCategories: catMap[key] || [],
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of categories.entries()) {
    const categoryData = formCategoryData(row);

    const { error: categoryValidationError } = categorySchema.validate(
      categoryData,
      {
        abortEarly: false,
      },
    );

    if (categoryValidationError) {
      failureCount++;
      // Check if subcategory errors exist
      const subCatErrors = categoryValidationError.details.filter((d) =>
        d.path.includes("subCategories"),
      );

      errors.push({
        rowNumber: i + 2,
        categoryName: categoryData.name || "Unknown Category",
        categoryErrors: categoryValidationError.details
          .filter((d) => !d.path.includes("subCategories"))
          .map((d) => d.message),
        subCategoryErrors: subCatErrors.map((d) => d.message),
      });
      continue;
    }

    const docRef = categoryMap[categoryData.nameNormalized];

    if (docRef) {
      batch.update(docRef.ref, { id: docRef.id, ...categoryData });
    } else {
      const newDocRef = categoryCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...categoryData });
      categoryMap[categoryData.nameNormalized] = { ref: newDocRef };
    }

    opCount++;
    successCount++;
    if (opCount >= 500) {
      await commitBatch();
    }
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: categories.length,
    successCount,
    failureCount,
    sheetName: "Categories",
  };

  if (errors.length) {
    console.error(
      "Category/Subcategory import errors:",
      JSON.stringify(errors, null, 2),
    );
  }

  return { logEntry };
};

// Tags
const saveTags = async (tenantId, data = {}) => {
  const tags = data[SHEET_NAMES.TAGS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing tags into a map (normalized name → ref)
  const tagDocs = await tagCollection.where("tenantId", "==", tenantId).get();

  const tagMap = tagDocs.docs.reduce((map, doc) => {
    const normalized = formatName(doc.get("name"));
    map[normalized] = doc.ref;
    return map;
  }, {});

  function formTagData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  async function commitBatch() {
    if (opCount === 0) return; // nothing to commit
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  const seenNames = new Set();

  for (const [i, row] of tags.entries()) {
    const tagData = formTagData(row);
    const normalizedName = formatName(tagData.name);
    const docRef = tagMap[normalizedName];
    const allErrors = [];

    // 1️⃣ Check if name is duplicated within import file
    if (seenNames.has(normalizedName)) {
      allErrors.push(
        `WorkArea Group '${tagData.name}' is duplicated in this import file.`,
      );
    } else {
      seenNames.add(normalizedName);
    }

    // 2️⃣ Validate structure via Joi
    const { error: validationError } = tagSchema.validate(tagData, {
      abortEarly: false,
    });
    if (validationError) {
      allErrors.push(...validationError.details.map((d) => d.message));
    }

    if (!docRef && tagMap[normalizedName]) {
      allErrors.push(
        `WorkArea Group '${tagData.name}' already exists and cannot be created again.`,
      );
    }

    if (allErrors.length > 0) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          { name: tagData.name || "Unknown WorkArea Group", errors: allErrors },
        ],
      });
      continue;
    }

    if (docRef) {
      // Update existing tag
      batch.update(docRef, { id: docRef.id, ...tagData });
    } else {
      // Create new tag
      const newDocRef = tagCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...tagData });
      tagMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) await commitBatch();
  }

  // Commit leftover ops
  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: tags.length,
    successCount,
    failureCount,
    sheetName: "Tag",
  };

  if (errors.length) {
    console.error(
      "WorkArea Group import errors:",
      JSON.stringify(errors, null, 2),
    );
  }

  return { logEntry };
};

// Ledgers
const saveLedgers = async (tenantId, data = {}) => {
  const ledgers = data[SHEET_NAMES.LEDGERS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing ledgers into a map (normalized name → ref)
  const ledgerDocs = await ledgerCollection
    .where("tenantId", "==", tenantId)
    .get();

  const ledgerMap = ledgerDocs.docs.reduce((map, doc) => {
    const normalized = formatName(doc.get("name"));
    map[normalized] = doc.ref;
    return map;
  }, {});

  function formLedgerData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  async function commitBatch() {
    if (opCount === 0) return; // nothing to commit
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  const seenNames = new Set();

  for (const [i, row] of ledgers.entries()) {
    const ledgerData = formLedgerData(row);
    const normalizedName = formatName(ledgerData.name);
    const docRef = ledgerMap[normalizedName];
    const allErrors = [];

    // 1️⃣ Check if name is duplicated within import file
    if (seenNames.has(normalizedName)) {
      allErrors.push(
        `Ledger '${ledgerData.name}' is duplicated in this import file.`,
      );
    } else {
      seenNames.add(normalizedName);
    }

    // 2️⃣ Validate structure via Joi
    const { error: validationError } = ledgerSchema.validate(ledgerData, {
      abortEarly: false,
    });
    if (validationError) {
      allErrors.push(...validationError.details.map((d) => d.message));
    }

    if (!docRef && ledgerMap[normalizedName]) {
      allErrors.push(
        `Ledger '${ledgerData.name}' already exists and cannot be created again.`,
      );
    }

    if (allErrors.length > 0) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          { name: ledgerData.name || "Unknown Ledger", errors: allErrors },
        ],
      });
      continue;
    }

    if (docRef) {
      // Update existing ledger
      batch.update(docRef, { id: docRef.id, ...ledgerData });
    } else {
      // Create new ledger
      const newDocRef = ledgerCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...ledgerData });
      ledgerMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) await commitBatch();
  }

  // Commit leftover ops
  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: ledgers.length,
    successCount,
    failureCount,
    sheetName: "Ledger",
  };

  if (errors.length) {
    console.error("Ledgers import errors:", JSON.stringify(errors, null, 2));
  }

  return { logEntry };
};

// Taxes
const saveTaxes = async (tenantId, data = {}) => {
  const taxes = data[SHEET_NAMES.TAXES] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // 🔹 Load existing taxes from DB into a map (normalized name → ref)
  const taxDocs = await taxCollection.where("tenantId", "==", tenantId).get();
  const taxMap = taxDocs.docs.reduce((map, doc) => {
    const normalized = formatName(doc.get("name")); // e.g., "gst"
    map[normalized] = doc.ref;
    return map;
  }, {});

  // 🔹 Track names encountered in current import to detect duplicates within same file
  const importedNames = new Set();

  function parseComponentsToArray(components, type = "amount") {
    if (!components) return [];
    return components.split(",").map((component) => {
      const [name, value] = component.trim().split("|");
      const result = { name: parseData(name) };
      if (type === "amount") result.valueAmt = parseNumber(value);
      else result.valuePercentage = parseNumber(value);
      return result;
    });
  }

  function formTaxData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      valueType: parseData(row.valueType || "amount"),
      valueAmt: parseNumber(row.valueAmt),
      taxLevel: parseData(row.taxLevel),
      valuePercentage: parseNumber(row.valuePercentage),
      nameNormalized: parseData(formatName(row.name)),
      components: parseComponentsToArray(row.components, row.valueType),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  const seenNames = new Set(); // Track duplicates within import file

  for (const [i, row] of taxes.entries()) {
    const taxData = formTaxData(row);
    const normalizedName = formatName(taxData.name);
    const docRef = taxMap[normalizedName];
    const allErrors = [];

    // 1️⃣ Check if name is duplicated within import file
    if (seenNames.has(normalizedName)) {
      allErrors.push(
        `Tax '${taxData.name}' is duplicated in this import file.`,
      );
    } else {
      seenNames.add(normalizedName);
    }

    // 2️⃣ Validate structure via Joi
    const { error: validationError } = taxSchema.validate(taxData, {
      abortEarly: false,
    });
    if (validationError) {
      allErrors.push(...validationError.details.map((d) => d.message));
    }

    // 3️⃣ Component validation
    const componentCount = taxData.components?.length || 0;
    if (componentCount === 1) {
      allErrors.push(
        "Components must be either empty or contain at least 2 items",
      );
    }

    if (componentCount >= 2) {
      const key =
        taxData.valueType === "percentage" ? "valuePercentage" : "valueAmt";

      const totalComponentValue = taxData.components.reduce(
        (sum, comp) => sum + (parseFloat(comp[key]) || 0),
        0,
      );
      if (totalComponentValue !== taxData[key]) {
        allErrors.push(
          `Sum of component values (${totalComponentValue}) must equal tax value (${taxData[key]})`,
        );
      }
      // const diff = Math.abs(
      //   totalComponentValue - parseFloat(taxData.value || 0)
      // );
      // if (diff > 0.001) {
      //   allErrors.push(
      //     `Sum of component values (${totalComponentValue}) must equal tax value (${taxData.value})`
      //   );
      // }
    }

    if (!docRef && taxMap[normalizedName]) {
      allErrors.push(
        `Tax '${taxData.name}' already exists and cannot be created again.`,
      );
    }

    if (allErrors.length > 0) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [{ name: taxData.name || "Unknown Tax", errors: allErrors }],
      });
      continue;
    }

    if (docRef) {
      // Update existing tax
      batch.update(docRef, { id: docRef.id, ...taxData });
    } else {
      // Create new tax
      const newDocRef = taxCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...taxData });
      taxMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) await commitBatch();
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: taxes.length,
    successCount,
    failureCount,
    sheetName: "Tax",
  };

  if (errors.length) {
    console.error("Tax import errors:", JSON.stringify(errors, null, 2));
  }

  return { logEntry };
};

// House Units
const saveHouseUnit = async (tenantId, data = {}) => {
  const houseUnits = data[SHEET_NAMES.HOUSE_UNITS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing house units into a map (normalized name → ref)
  const houseUnitDocs = await houseUnitCollection
    .where("tenantId", "==", tenantId)
    .select("nameNormalized")
    .get();

  const houseUnitMap = {};
  houseUnitDocs.forEach((doc) => {
    const nn = doc.get("nameNormalized");
    if (nn) houseUnitMap[nn] = doc.ref;
  });

  function formHouseUnitData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      symbol: parseData(row.symbol).toLowerCase(),
      quantity: parseNumber(row.quantity),
      toUnit: parseData(row.toUnit).toLowerCase(),
      activeStatus: parseActiveStatus(row.activeStatus),
      default: false,
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  async function commitBatch() {
    if (opCount === 0) return; // nothing to commit
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of houseUnits.entries()) {
    const houseUnitData = formHouseUnitData(row);

    // Validate vendorData using Joi
    const { error: validationError } = houseUnitSchema.validate(houseUnitData, {
      abortEarly: false,
    });

    if (validationError) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: houseUnitData.name || "Unknown Unit",
            errors: validationError.details.map((d) => d.message),
          },
        ],
      });
      continue; // skip saving invalid row
    }

    // 🔒 Reject default-unit conflicts
    if (isDuplicateWithDefaultUnits(houseUnitData.name, houseUnitData.symbol)) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: houseUnitData.name || "Unknown Unit",
            errors: [
              "House unit name or symbol already exists in default units!",
            ],
          },
        ],
      });
      continue;
    }

    const normalizedName = houseUnitData.nameNormalized;
    const docRef = houseUnitMap[normalizedName];

    if (docRef) {
      batch.update(docRef, { id: docRef.id, ...houseUnitData });
    } else {
      const newDocRef = houseUnitCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...houseUnitData });

      // 🔑 Add to vendorMap so duplicate rows in same import don't insert again
      houseUnitMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) {
      await commitBatch();
    }
  }
  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: houseUnits.length,
    successCount,
    failureCount,
    sheetName: "House Unit",
  };

  if (errors.length) {
    console.error("HouseUnit import errors:", JSON.stringify(errors, null, 2));
  }
  return {
    logEntry,
  };
};

// Inventory Items
const saveInventoryItems = async (tenantId, data = {}) => {
  const inventoryItems = data[SHEET_NAMES.INVENTORY_ITEMS] || [];
  const inventoryPackages = data[SHEET_NAMES.INVENTORY_PACKAGES] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  const [
    inventoryItemDocs,
    categoryDocs,
    houseUnitDocs,
    vendorDocs,
    tagDocs,
    taxDocs,
    ledgerDocs,
    recipeDocs,
  ] = await Promise.all([
    inventoryItemCollection.where("tenantId", "==", tenantId).get(),
    categoryCollection.where("tenantId", "==", tenantId).get(),
    houseUnitCollection.where("tenantId", "==", tenantId).get(),
    vendorCollection.where("tenantId", "==", tenantId).get(),
    tagCollection.where("tenantId", "==", tenantId).get(),
    taxCollection.where("tenantId", "==", tenantId).get(),
    ledgerCollection.where("tenantId", "==", tenantId).get(),
    recipeCollection.where("tenantId", "==", tenantId).get(),
  ]);

  const inventoryItemMap = inventoryItemDocs.docs.reduce((m, d) => {
    m[formatName(d.get("itemName"))] = { ref: d.ref, ...d.data() };
    return m;
  }, {});

  const categoryMap = categoryDocs.docs.reduce((m, d) => {
    m[formatName(d.get("name"))] = d.data();
    return m;
  }, {});

  const houseUnitDocsData = [
    ...houseUnitDocs.docs.map((d) => d.data()),
    ...DEFAULT_UNITS,
  ];

  const houseUnitMap = houseUnitDocsData.reduce((m, u) => {
    m[parseData(u.symbol)] = u;
    return m;
  }, {});

  const vendorMap = vendorDocs.docs.reduce((m, d) => {
    const v = d.data();
    m[formatName(v.name)] = v;
    return m;
  }, {});

  const tagMap = tagDocs.docs.reduce((m, d) => {
    const t = d.data();
    m[formatName(t.name)] = t;
    return m;
  }, {});

  const ledgerMap = ledgerDocs.docs.reduce((m, d) => {
    m[formatName(d.get("name"))] = d.data();
    return m;
  }, {});

  const taxMap = taxDocs.docs.reduce((m, d) => {
    const t = d.data();
    m[formatName(t.name)] = { id: d.id, ...t };
    return m;
  }, {});

  const recipeMap = recipeDocs.docs.reduce((m, d) => {
    const t = d.data();
    m[formatName(t.name)] = { id: d.id, ...t };
    return m;
  }, {});

  const pushRowError = (rowIndex, itemName, msg) => {
    errors.push({
      rowNumber: rowIndex + 2,
      fields: [{ name: itemName, errors: [msg] }],
    });
  };

  const findCategory = (c) => categoryMap[formatName(c)];
  const findLedger = (l) => {
    const ledger = ledgerMap[formatName(l)];
    return { id: ledger.id, name: ledger.name };
  };
  const findUnit = (s) => houseUnitMap[parseData(s)];

  const findRecipe = (r) => recipeMap[formatName(r)];

  const getChain = (symbol) => {
    const m = {};
    houseUnitDocsData.forEach((u) => (m[u.symbol] = u));
    const list = [];
    const visited = new Set();
    let cur = symbol;
    while (cur && !visited.has(cur)) {
      visited.add(cur);
      const u = m[cur];
      if (!u) break;
      list.push(u);
      cur = u.toUnit;
    }
    return list;
  };

  const parsePurchaseUnit = (unit, rowIndex, itemName) => {
    const u = findUnit(unit);
    if (!u) {
      pushRowError(rowIndex, itemName, `Unit not found: ${unit}`);
      return { _error: true };
    }
    return {
      id: u.id,
      name: u.name,
      symbol: u.symbol,
      quantity: u.quantity,
      toUnit: u.toUnit,
    };
  };

  const parseUnit = (from, to, rowIndex, itemName) => {
    const fromSym = parseData(from);
    const toSym = parseData(to);
    const target = findUnit(toSym);
    if (!target) {
      pushRowError(rowIndex, itemName, `Unit not found: ${toSym}`);
      return { _error: true };
    }
    if (fromSym === toSym) {
      return {
        id: target.id,
        name: target.name,
        symbol: target.symbol,
        quantity: 1,
        toUnit: target.symbol,
      };
    }
    const chain = getChain(fromSym);
    const conv = chain.find((u) => u.toUnit === toSym);
    if (!conv) {
      pushRowError(
        rowIndex,
        itemName,
        `Unit conversion not found: ${fromSym} → ${toSym}`,
      );
      return { _error: true };
    }
    return {
      id: target.id,
      name: target.name,
      symbol: target.symbol,
      quantity: conv.quantity,
      toUnit: toSym,
    };
  };

  const parseVendors = (txt, rowIndex, itemName) => {
    const raw = parseData(txt);
    if (!raw) return [];
    const arr = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);
    const list = [];
    const miss = [];
    arr.forEach((n) => {
      const v = vendorMap[formatName(n)];
      if (v) list.push({ id: v.id, name: v.name });
      else miss.push(n);
    });
    if (miss.length) {
      pushRowError(
        rowIndex,
        itemName,
        `Vendor(s) not found: ${miss.join(", ")}`,
      );
      return { _error: true };
    }
    return list;
  };

  const parseTags = (txt, rowIndex, itemName) => {
    const raw = parseData(txt);
    if (!raw) return [];
    const arr = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);
    const list = [];
    const miss = [];
    arr.forEach((t) => {
      const tg = tagMap[formatName(t)];
      if (tg) list.push({ id: tg.id, name: tg.name });
      else miss.push(t);
    });
    if (miss.length) {
      pushRowError(
        rowIndex,
        itemName,
        `WorkArea Group(s) not found: ${miss.join(", ")}`,
      );
      return { _error: true };
    }
    return list;
  };

  const parseTaxes = (txt, rowIndex, itemName) => {
    const raw = parseData(txt);
    if (!raw) return [];
    const arr = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);
    const list = [];
    const miss = [];
    arr.forEach((n) => {
      const t = taxMap[formatName(n)];
      if (t)
        list.push({
          id: t.id,
          name: t.name,
          taxLevel: t.taxLevel,
          valueAmt: t.valueAmt,
          valuePercentage: t.valuePercentage,
          valueType: t.valueType,
          components: t.components,
        });
      else miss.push(n);
    });
    if (miss.length) {
      pushRowError(rowIndex, itemName, `Tax(s) not found: ${miss.join(", ")}`);
      return { _error: true };
    }
    return list;
  };

  const getRelated = (purchaseUnit) => {
    if (!purchaseUnit || !purchaseUnit.symbol) return [];
    const syms = [
      purchaseUnit.symbol?.toLowerCase(),
      purchaseUnit.toUnit?.toLowerCase(),
    ].filter(Boolean);
    return houseUnitDocsData
      .filter((u) => {
        const m = syms.includes(u.toUnit?.toLowerCase());
        const a = u.activeStatus === true;
        const d = u.symbol?.toLowerCase() !== purchaseUnit.symbol.toLowerCase();
        return m && a && d;
      })
      .sort((a, b) =>
        (a.nameNormalized || "").localeCompare(b.nameNormalized || ""),
      );
  };

  const buildPackage = (row, purchaseUnit, rowIndex, itemName) => {
    const allowed = getRelated(purchaseUnit);
    const nm = parseData(row.name)?.trim()?.toLowerCase();
    const match = allowed.find((u) => u.name.trim().toLowerCase() === nm);
    if (!match) {
      pushRowError(rowIndex, itemName, `Invalid package: ${row.name}`);
      return { _error: true };
    }
    return {
      name: match.name,
      quantity: match.quantity,
      toUnit: match.toUnit,
      packageCode: parseData(row.packageCode),
      unitCost: parseNumber(row.unitCost) ? rupeeToPaise(row.unitCost) : 0,
      emptyWeight: parseNumber(row.emptyWeight) || 0,
      fullWeight: parseNumber(row.fullWeight) || 0,
    };
  };

  const packageMap = {};
  inventoryPackages.forEach((p) => {
    const parent = parseData(p.itemName)?.toLowerCase();
    if (!parent) return;
    if (!packageMap[parent]) packageMap[parent] = [];
    packageMap[parent].push(p);
  });

  const formInventoryData = (row, rowIndex) => {
    const category = findCategory(row.category);
    if (!category) {
      pushRowError(rowIndex, row.itemName, `Category not found`);
      return { _mappingError: true };
    }

    const subCategory = (category.subCategories || []).find(
      (s) => formatName(s.name) === formatName(row.subCategory),
    );
    if (!subCategory) {
      pushRowError(rowIndex, row.itemName, `Subcategory not found`);
      return { _mappingError: true };
    }

    let ledger = null;

    if (row.ledger && String(row.ledger).trim()) {
      const found = findLedger(row.ledger);
      if (!found) {
        pushRowError(rowIndex, row.itemName, `Ledger not found`);
        return { _mappingError: true };
      }
      ledger = found;
    }

    const purchaseUnit = parsePurchaseUnit(
      row.purchaseUnit.toLowerCase(),
      rowIndex,
      row.itemName,
    );
    if (purchaseUnit._error) return { _mappingError: true };

    const countingUnit = parseUnit(
      row.purchaseUnit.toLowerCase(),
      row.countingUnit.toLowerCase(),
      rowIndex,
      row.itemName,
    );
    if (countingUnit._error) return { _mappingError: true };

    const recipeUnit = parseUnit(
      row.countingUnit.toLowerCase(),
      row.recipeUnit.toLowerCase(),
      rowIndex,
      row.itemName,
    );
    if (recipeUnit._error) return { _mappingError: true };

    const tags = parseTags(row.tags, rowIndex, row.itemName);
    if (tags._error) return { _mappingError: true };

    const vendors = parseVendors(row.vendors, rowIndex, row.itemName);
    if (vendors._error) return { _mappingError: true };

    const taxes = parseTaxes(row.taxes, rowIndex, row.itemName);
    if (taxes._error) return { _mappingError: true };

    const parsedCost = parseNumber(row.unitCost) || 0;
    let taxTotal = 0;
    taxes.forEach((t) => {
      if (t.valueType === "amount") taxTotal += Number(t.valueAmt) || 0;
      else taxTotal += ((Number(t.valuePercentage) || 0) * parsedCost) / 100;
    });

    const pkgRows = packageMap[parseData(row.itemName).toLowerCase()] || [];
    const builtPkgs = pkgRows.map((p) =>
      buildPackage(p, purchaseUnit, rowIndex, row.itemName),
    );

    if (builtPkgs.some((p) => p._error)) return { _mappingError: true };

    const recipeVal =
      parseData(row.itemType) === "made"
        ? (() => {
            const r = findRecipe(row.recipe);
            if (!r) {
              pushRowError(
                rowIndex,
                row.itemName,
                `Recipe not found: ${row.recipe}`,
              );
              return { _error: true };
            }
            return r;
          })()
        : null;

    if (recipeVal && recipeVal._error) return { _mappingError: true };

    return {
      tenantId,
      itemName: parseData(row.itemName),
      nameNormalized: formatName(row.itemName),
      itemType: parseData(row.itemType),
      itemCode: parseData(row.itemCode),
      category: { id: category.id, name: category.name },
      subCategory,
      tags,
      taxes,
      purchaseUnit,
      countingUnit,
      recipeUnit,
      parLevel: parseNumber(row.parLevel),
      trackExpiry: parseBool(row.trackExpiry),
      stockable: parseBool(row.stockable),
      allVendors: parseBool(row.allVendors),
      vendors,
      unitCost: parseNumber(row.unitCost) ? rupeeToPaise(row.unitCost) : 0,
      activeStatus: parseActiveStatus(row.activeStatus),
      hsnCode: parseData(row.hsnCode),
      ledger,
      defaultPackage: parseBool(
        row.defaultPackage == null ? true : row.defaultPackage,
      ),
      recipe: recipeVal,
      unitTax: rupeeToPaise(taxTotal),
      unitCostIncludingTax: rupeeToPaise(parsedCost + taxTotal),
      packages: builtPkgs,
      showPackage: builtPkgs.length > 0,
    };
  };

  const batchLimit = 500;
  let batch = db.batch();
  let ops = 0;
  const promises = [];

  const itemsMissingCode = inventoryItems.filter((i) => !i.itemCode).length;
  let itemRange = null;
  if (itemsMissingCode > 0) {
    itemRange = await getNextNumberRange(
      tenantId,
      COUNTER_TYPES.INVENTORY_ITEM.key,
      COUNTER_TYPES.INVENTORY_ITEM.prefix,
      itemsMissingCode,
    );
  }
  let nextItemNumber = itemRange ? itemRange.start : null;

  let pkgRows = [];
  inventoryItems.forEach((row) => {
    const name = parseData(row.itemName)?.toLowerCase();
    if (name && packageMap[name]) {
      pkgRows.push(...packageMap[name]);
    }
  });

  const pkgsMissingCode = pkgRows.filter((p) => !p.packageCode).length;
  let pkgRange = null;
  if (pkgsMissingCode > 0) {
    pkgRange = await getNextNumberRange(
      tenantId,
      COUNTER_TYPES.PACKAGE.key,
      COUNTER_TYPES.PACKAGE.prefix,
      pkgsMissingCode,
    );
  }
  let nextPkgNumber = pkgRange ? pkgRange.start : null;

  const assignPkgCodes = (itemData) => {
    const existingItem = inventoryItemMap[itemData.nameNormalized];
    const existingPackages = existingItem?.packages || [];

    itemData.packages = itemData.packages.map((pkg) => {
      let code = pkg.packageCode;

      if (!code) {
        const seq = String(nextPkgNumber).padStart(4, "0");
        code = `${pkgRange.prefix}${seq}`;
        nextPkgNumber++;
      }

      // Try to find existing package id
      const match = existingPackages.find(
        (p) =>
          (p.packageCode && p.packageCode === code) ||
          formatName(p.name) === formatName(pkg.name),
      );

      return {
        ...pkg,
        id: match?.id ? match.id : inventoryItemCollection.doc().id,
        packageCode: code,
      };
    });

    return itemData;
  };

  for (const [i, row] of inventoryItems.entries()) {
    let itemData = formInventoryData(row, i);

    if (itemData._mappingError) {
      failureCount++;
      continue;
    }

    if (!itemData.itemCode) {
      const seq = String(nextItemNumber).padStart(4, "0");
      itemData.itemCode = `${itemRange.prefix}${seq}`;
      nextItemNumber++;
    }

    itemData = assignPkgCodes(itemData);

    const pkgNameSet = new Set();
    const pkgCodeSet = new Set();
    const pkgNameDup = [];
    const pkgCodeDup = [];

    for (const pkg of itemData.packages) {
      const nm = pkg.name?.trim().toLowerCase();
      const cd = pkg.packageCode?.trim();

      if (pkgNameSet.has(nm)) pkgNameDup.push(pkg.name);
      else pkgNameSet.add(nm);

      if (pkgCodeSet.has(cd)) pkgCodeDup.push(cd);
      else pkgCodeSet.add(cd);
    }

    if (pkgNameDup.length || pkgCodeDup.length) {
      const errs = [];
      if (pkgNameDup.length)
        errs.push(`Duplicate package names: ${pkgNameDup.join(", ")}`);
      if (pkgCodeDup.length)
        errs.push(`Duplicate package codes: ${pkgCodeDup.join(", ")}`);

      pushRowError(i, itemData.itemName, errs.join(" | "));
      failureCount++;
      continue;
    }

    const { error } = inventoryItemSchema.validate(itemData, {
      abortEarly: false,
    });

    if (error) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: itemData.itemName,
            errors: error.details.map((d) => d.message),
          },
        ],
      });
      continue;
    }

    const existing = inventoryItemMap[itemData.nameNormalized];

    if (existing) {
      batch.update(existing.ref, { id: existing.ref.id, ...itemData });
    } else {
      const newRef = inventoryItemCollection.doc();
      batch.set(newRef, { id: newRef.id, ...itemData });
      inventoryItemMap[itemData.nameNormalized] = { ref: newRef };
    }

    ops++;
    if (ops >= batchLimit) {
      promises.push(batch.commit());
      batch = db.batch();
      ops = 0;
    }

    successCount++;
  }

  if (ops) promises.push(batch.commit());
  await Promise.all(promises);

  return {
    logEntry: {
      errors,
      totalRecords: inventoryItems.length,
      successCount,
      failureCount,
      sheetName: "Inventory Items",
    },
  };
};

// Recipes
const saveRecipe = async (tenantId, data = {}) => {
  const recipes = data[SHEET_NAMES.RECIPES] || [];
  const recipesIngredients = data[SHEET_NAMES.RECIPE_INGREDIENTS] || [];

  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // ------------------------------------------------------------
  // STEP 0: Build recipe name set & dependency graph
  // ------------------------------------------------------------
  const recipeByName = new Map();
  recipes.forEach((r, i) =>
    recipeByName.set(formatName(r.name), { row: r, index: i }),
  );

  const recipeDeps = new Map();
  recipeByName.forEach((_, name) => recipeDeps.set(name, new Set()));

  recipesIngredients.forEach((row) => {
    const parent = formatName(row.recipeName);
    const ing = formatName(row.itemName);
    if (recipeByName.has(ing)) recipeDeps.get(parent)?.add(ing);
  });

  // ------------------------------------------------------------
  // STEP 1: Topological layering (Kahn)
  // ------------------------------------------------------------
  const inDegree = new Map();
  recipeDeps.forEach((deps, name) => inDegree.set(name, deps.size));

  const layers = [];
  let queue = [];

  inDegree.forEach((deg, name) => {
    if (deg === 0) queue.push(name);
  });

  while (queue.length) {
    layers.push(queue);
    const next = [];
    for (const r of queue) {
      recipeDeps.forEach((deps, name) => {
        if (deps.has(r)) {
          const d = inDegree.get(name) - 1;
          inDegree.set(name, d);
          if (d === 0) next.push(name);
        }
      });
    }
    queue = next;
  }

  const unresolved = [...inDegree.entries()].filter(([, d]) => d > 0);
  if (unresolved.length) {
    throw new Error(
      `Circular recipe dependency detected: ${unresolved
        .map(([n]) => n)
        .join(", ")}`,
    );
  }

  // ------------------------------------------------------------
  // STEP 2: Fetch masters
  // ------------------------------------------------------------
  let [recipeDocs, tagDocs, inventoryItemDocs, houseUnitDocs] =
    await Promise.all([
      recipeCollection.where("tenantId", "==", tenantId).get(),
      tagCollection.where("tenantId", "==", tenantId).get(),
      inventoryItemCollection.where("tenantId", "==", tenantId).get(),
      houseUnitCollection.where("tenantId", "==", tenantId).get(),
    ]);

  let recipeMap = new Map();
  recipeDocs.docs.forEach((d) =>
    recipeMap.set(formatName(d.get("name")), { ref: d.ref, ...d.data() }),
  );

  const tagMap = new Map();
  tagDocs.docs.forEach((d) => tagMap.set(formatName(d.get("name")), d.data()));

  const inventoryItemMap = new Map();
  inventoryItemDocs.docs.forEach((d) => {
    const item = d.data();
    inventoryItemMap.set(formatName(item.itemName), {
      ref: d.ref,
      ...item,
      unitCost: paiseToRupee(item.unitCost) || 0,
    });
  });

  const houseUnitDocsData = [
    ...houseUnitDocs.docs.map((d) => d.data()),
    ...DEFAULT_UNITS,
  ];
  const houseUnitMap = new Map(
    houseUnitDocsData.map((u) => [parseData(u.symbol), u]),
  );

  const findUnit = (s) => {
    const {
      tenantId,
      default: def,
      ...rest
    } = houseUnitMap.get(parseData(s)) || {};
    return rest;
  };

  // ★ ADDED — recipe code range
  const recipesMissingCode = recipes.filter((r) => !r.recipeCode).length;
  let recipeRange = null;
  if (recipesMissingCode > 0) {
    recipeRange = await getNextNumberRange(
      tenantId,
      COUNTER_TYPES.RECIPE.key,
      COUNTER_TYPES.RECIPE.prefix,
      recipesMissingCode,
    );
  }
  let nextRecipeNumber = recipeRange ? recipeRange.start : null;

  // ------------------------------------------------------------
  // Helpers (unchanged logic)
  // ------------------------------------------------------------
  const pushRowError = (rowIndex, name, msg) => {
    errors.push({ rowNumber: rowIndex + 2, fields: [{ name, errors: [msg] }] });
  };

  const parseTags = (txt) => {
    const raw = parseData(txt);
    if (!raw) return [];
    return raw
      .split(",")
      .map((t) => t.trim())
      .map((t) => tagMap.get(formatName(t)))
      .filter(Boolean)
      .map((t) => ({ id: t.id, name: t.name }));
  };

  const parseServiceTypes = (txt) => {
    const raw = parseData(txt);
    if (!raw) return [];
    return raw
      .split(",")
      .map((t) => t.trim())
      .map((t) =>
        SERVICE_TYPES.find((st) => st.name.toLowerCase() === t.toLowerCase()),
      )
      .filter(Boolean)
      .map((t) => ({ id: t.id, name: t.name }));
  };

  function calculateRecipeAndInventoryItemsCost(item) {
    let cost = item.unitCost;
    switch (item.itemType) {
      case "made":
      case "bought": {
        const purchaseUnit = item.purchaseUnit;
        const recipeUnit = item.recipeUnit;
        if (
          purchaseUnit?.symbol !== recipeUnit?.symbol &&
          purchaseUnit?.toUnit === recipeUnit?.symbol
        ) {
          if (item.defaultPackage) cost = cost / purchaseUnit?.quantity;
          else {
            const pkg = item.packages?.[0];
            if (pkg) {
              if (recipeUnit?.symbol === pkg?.toUnit) {
                cost = paiseToRupee(pkg?.unitCost) / pkg?.quantity;
              } else
                cost =
                  paiseToRupee(pkg?.unitCost) /
                  (purchaseUnit?.quantity * pkg?.quantity);
            } else cost = cost / purchaseUnit?.quantity;
          }
        }
        break;
      }
      case "recipe":
      case "subRecipe":
        cost = cost / item.recipeQuantity;
        break;
    }
    return truncateNumber(cost, 3);
  }

  const buildIngredientFromInventoryItem = ({ ingredientItem, row }) => {
    const quantity = parseNumber(row.quantity);
    const consumptionQuantity =
      row.quantity > 0 && row.yield > 0
        ? truncateNumber(row.quantity / row.yield, 3)
        : 0;
    const rate = calculateRecipeAndInventoryItemsCost(ingredientItem);
    const totalCost = truncateNumber(rate * consumptionQuantity, 3);
    return {
      itemId: ingredientItem.id,
      itemName: ingredientItem.itemName,
      itemCode: ingredientItem.itemCode,
      quantity,
      consumptionQuantity,
      yield: parseNumber(row.yield),
      itemType: ingredientItem.itemType,
      stockable: ingredientItem.stockable,
      recipeUnit: ingredientItem.recipeUnit,
      purchaseUnit: ingredientItem.purchaseUnit,
      countingUnit: ingredientItem.countingUnit,
      unitCost: rupeeToPaise(ingredientItem.unitCost),
      totalCost: rupeeToPaise(totalCost),
      isSubRecipe: false,
      packages: ingredientItem.packages.map((p) => ({
        ...p,
        unitCost: paiseToRupee(p.unitCost),
      })),
      defaultPackage: ingredientItem.defaultPackage,
      serviceType: parseServiceTypes(row.serviceTypes),
    };
  };

  const buildIngredientFromSubRecipe = ({ subRecipe, row }) => {
    const quantity = parseNumber(row.quantity);
    const consumptionQuantity =
      parseNumber(row.consumptionQuantity) || quantity;
    const rate = calculateRecipeAndInventoryItemsCost({
      unitCost: paiseToRupee(subRecipe.cost),
      itemType: subRecipe.recipeType,
      recipeQuantity: subRecipe.quantity,
    });
    const totalCost = truncateNumber(rate * consumptionQuantity, 3);
    return {
      itemId: subRecipe.id,
      itemName: subRecipe.name,
      itemCode: subRecipe.recipeCode,
      quantity,
      recipeQuantity: subRecipe.quantity,
      consumptionQuantity,
      yield: parseNumber(row.yield),
      itemType: subRecipe.recipeType,
      recipeUnit: subRecipe.recipeUnit,
      unitCost: rupeeToPaise(paiseToRupee(subRecipe.cost)),
      totalCost: rupeeToPaise(totalCost),
      isSubRecipe: true,
      serviceType: parseServiceTypes(row.serviceTypes),
    };
  };

  // ------------------------------------------------------------
  // STEP 3: Group ingredients by parent recipe
  // ------------------------------------------------------------
  const ingredientsByParent = new Map();
  recipesIngredients.forEach((row, i) => {
    const parent = formatName(row.recipeName);
    if (!ingredientsByParent.has(parent)) ingredientsByParent.set(parent, []);
    ingredientsByParent.get(parent).push({ row, index: i });
  });

  // ------------------------------------------------------------
  // STEP 4: Process layer by layer
  // ------------------------------------------------------------
  const batchLimit = 500;
  for (const layer of layers) {
    const ingredientMap = new Map();

    for (const name of layer) {
      const rows = ingredientsByParent.get(name) || [];
      for (const { row, index } of rows) {
        const code = parseData(row.ingredientItemCode || row.itemCode || "");
        const nameKey = formatName(row.itemName);

        // Try by code first
        let sub = null;
        let inv = null;

        if (code) {
          sub = [...recipeMap.values()].find(
            (r) => formatName(r.recipeCode) === formatName(code),
          );
          if (!sub) {
            inv = [...inventoryItemMap.values()].find(
              (i) => formatName(i.itemCode) === formatName(code),
            );
          }
        }

        // Fallback to name
        if (!sub && !inv) {
          sub = recipeMap.get(nameKey);
          inv = inventoryItemMap.get(nameKey);
        }

        let ingredient;
        if (sub) {
          ingredient = buildIngredientFromSubRecipe({ subRecipe: sub, row });
        } else if (inv) {
          ingredient = buildIngredientFromInventoryItem({
            ingredientItem: inv,
            row,
          });
        } else {
          pushRowError(
            index,
            row.itemName,
            "Ingredient item / sub-recipe not found",
          );
          continue;
        }

        if (!ingredientMap.has(name)) ingredientMap.set(name, []);
        ingredientMap.get(name).push(ingredient);
      }
    }

    let batch = db.batch();
    let ops = 0;
    const commits = [];

    for (const name of layer) {
      const { row, index: recipeRowIndex } = recipeByName.get(name);
      const ingredients = ingredientMap.get(name) || [];
      if (!ingredients.length) {
        pushRowError(0, name, "No ingredients found");
        failureCount++;
        continue;
      }

      const totalCost = ingredients.reduce((a, i) => a + (i.totalCost || 0), 0);

      let recipeCode = parseData(row.recipeCode);
      if (!recipeCode) {
        const seq = String(nextRecipeNumber).padStart(4, "0");
        recipeCode = `${recipeRange.prefix}${seq}`;
        nextRecipeNumber++;
      }

      const recipeData = {
        tenantId,
        name: parseData(row.name),
        nameNormalized: formatName(row.name),
        recipeType: parseData(row.recipeType),
        recipeUnit: findUnit(row.recipeUnit),
        tags: parseTags(row.tags),
        recipeCode,
        quantity: parseNumber(row.quantity),
        cost: totalCost,
        ingredients,
        activeStatus: parseActiveStatus(row.activeStatus),
      };

      const { error } = recipeSchema.validate(recipeData, {
        abortEarly: false,
        allowUnknown: true,
      });
      if (error) {
        error.details.forEach((d) => {
          pushRowError(recipeRowIndex, d.path.join("."), d.message);
        });
        failureCount++;
        continue;
      }

      const existing = recipeMap.get(name);

      if (existing?.ref) {
        batch.update(existing.ref, { ...recipeData });
      } else {
        const ref = recipeCollection.doc();
        batch.set(ref, { id: ref.id, ...recipeData });
        recipeMap.set(name, { id: ref.id, ref, ...recipeData });
      }

      ops++;
      if (ops >= batchLimit) {
        commits.push(batch.commit());
        batch = db.batch();
        ops = 0;
      }

      successCount++;
    }

    if (ops) commits.push(batch.commit());
    await Promise.all(commits);
  }

  return {
    logEntry: {
      errors,
      totalRecords: recipes.length,
      successCount,
      failureCount,
      sheetName: "Recipes",
    },
  };
};

// example validation function
// @Todo: need to write schema specific validations
function validate(data) {
  const errors = [];

  // add more validation rules as needed
  return errors;
}

exports.saveData = async (sheetName, tenantId, data) => {
  switch (sheetName) {
    case SHEET_NAMES.TAGS:
      return await saveTags(tenantId, data);
    case SHEET_NAMES.LEDGERS:
      return await saveLedgers(tenantId, data);
    case SHEET_NAMES.TAXES:
      return await saveTaxes(tenantId, data);
    case SHEET_NAMES.VENDORS:
      return await saveVendors(tenantId, data);
    case SHEET_NAMES.CATEGORIES:
      return await saveCategories(tenantId, data);
    case SHEET_NAMES.HOUSE_UNITS:
      return await saveHouseUnit(tenantId, data);
    case SHEET_NAMES.INVENTORY_ITEMS:
      return await saveInventoryItems(tenantId, data);
    case SHEET_NAMES.RECIPES:
      return await saveRecipe(tenantId, data);
    default:
      throw new Error("Unsupported sheet name");
  }
};
