const admin = require("firebase-admin");
const db = admin.firestore();
const { SHEET_NAMES } = require("./sheet");
const { paiseToRupee } = require("@/utils/money");
const { COLLECTIONS } = require("@/defs/collectionDefs");

// General formatter for Excel data
const formatData = (value, defaultValue = "") => {
  return value !== undefined && value !== null ? value : defaultValue;
};

const formatBool = (value) => {
  return value === true ? "TRUE" : "FALSE";
};

const formatNumber = (value) => {
  return value || 0;
};

const formatActiveStatus = (value) => {
  return value === true ? "ACTIVE" : "IN-ACTIVE";
};

// Vendors
/**
 * Fetch all vendors from the database and format them into an array of objects
 * suitable for export to Excel.
 *
 * @param {string} tenantId The tenant ID to fetch vendors for.
 * @returns {object} An object where the key is the sheet name and the value is an
 *                  array of vendor objects.
 */
const getVendors = async (tenantId) => {
  const vendorCollection = db.collection(COLLECTIONS.VENDORS);
  const vendorSnap = await vendorCollection
    .where("tenantId", "==", tenantId)
    .get();

  const vendors = vendorSnap.docs.map((doc) => {
    const data = doc.data();
    const address = data.address || {};
    return {
      name: formatData(data.name),
      vendorId: formatData(data.vendorId),
      contactName: formatData(data.contactName),
      contactNo: formatData(data.contactNo),
      contactEmailId: formatData(data.contactEmailId),
      address: formatData(address.address),
      country: formatData(address.country),
      state: formatData(address.state),
      city: formatData(address.city),
      pincode: formatData(address.pincode),
      cinNo: formatData(data.cinNo),
      gstNo: formatData(data.gstNo),
      panNo: formatData(data.panNo),
      tinNo: formatData(data.tinNo),
      poTerms: formatData(data.poTerms),
      bankName: formatData(data.bankName),
      accountNumber: formatData(data.accountNumber),
      ifscCode: formatData(data.ifscCode),
      paymentTerms: formatData(data.paymentTerms),
      workOrderTerms: formatData(data.workOrderTerms),
      activeStatus: formatActiveStatus(data.activeStatus),
    };
  });
  return { [SHEET_NAMES.VENDORS]: vendors };
};

// Tags
const getTags = async (tenantId) => {
  const tagCollection = db.collection(COLLECTIONS.TAGS);
  const tagSnap = await tagCollection.where("tenantId", "==", tenantId).get();

  const tags = tagSnap.docs.map((doc) => {
    const data = doc.data();
    return {
      name: formatData(data.name),
      activeStatus: formatActiveStatus(data.activeStatus),
    };
  });
  return { [SHEET_NAMES.TAGS]: tags };
};

// Ledgers
const getLedgers = async (tenantId) => {
  const ledgerCollection = db.collection(COLLECTIONS.PRODUCT_LEDGERS);
  const ledgerSnap = await ledgerCollection
    .where("tenantId", "==", tenantId)
    .get();

  const ledgers = ledgerSnap.docs.map((doc) => {
    const data = doc.data();
    return {
      name: formatData(data.name),
      activeStatus: formatActiveStatus(data.activeStatus),
    };
  });
  return { [SHEET_NAMES.LEDGERS]: ledgers };
};

// Taxes
const getTaxes = async (tenantId) => {
  const taxCollection = db.collection(COLLECTIONS.TAXES);
  const taxSnap = await taxCollection.where("tenantId", "==", tenantId).get();

  const formatComponents = (conversions = [], type = "amount") =>
    conversions.length
      ? conversions
          .map(
            ({ name, valueAmt, valuePercentage }) =>
              `${name}|${type == "amount" ? valueAmt : valuePercentage}`,
          )
          .join(", ")
      : "";

  const taxes = taxSnap.docs.map((doc) => {
    const data = doc.data();
    return {
      name: formatData(data.name),
      valueType: formatData(data.valueType || "amount"),
      valueAmt: formatNumber(data.valueAmt),
      valuePercentage: formatNumber(data.valuePercentage),
      taxLevel: formatData(data.taxLevel),
      components: formatComponents(data.components, data.valueType),
      activeStatus: formatActiveStatus(data.activeStatus),
    };
  });
  return { [SHEET_NAMES.TAXES]: taxes };
};

// Categories
const getCategories = async (tenantId) => {
  const categoryCollection = db.collection(COLLECTIONS.CATEGORIES);
  const categorySnap = await categoryCollection
    .where("tenantId", "==", tenantId)
    .get();

  const categories = [];
  const subCategories = [];

  categorySnap.docs.forEach((doc) => {
    const data = doc.data();
    categories.push({
      name: formatData(data.name),
      isBarCategory: formatBool(data.isBarCategory),
      activeStatus: formatActiveStatus(data.activeStatus),
    });
    data.subCategories.forEach((sub) => {
      subCategories.push({
        category: formatData(data.name),
        subCategory: formatData(sub.name),
      });
    });
  });

  return {
    [SHEET_NAMES.CATEGORIES]: categories,
    [SHEET_NAMES.SUB_CATEGORIES]: subCategories,
  };
};

// House units
const getHouseUnits = async (tenantId) => {
  const houseUnitCollection = db.collection(COLLECTIONS.HOUSE_UNITS);
  const houseUnitSnap = await houseUnitCollection
    .where("tenantId", "==", tenantId)
    .get();

  // build houseUnits
  const houseUnits = houseUnitSnap.docs.map((doc) => {
    const data = doc.data();
    return {
      name: formatData(data.name),
      symbol: formatData(data.symbol),
      quantity: formatNumber(data.quantity),
      toUnit: formatData(data.toUnit),
      activeStatus: formatActiveStatus(data.activeStatus),
    };
  });

  return { [SHEET_NAMES.HOUSE_UNITS]: houseUnits };
};

// Inventory Items
const getInventoryItems = async (tenantId) => {
  const inventoryCollection = db.collection(COLLECTIONS.INVENTORY_ITEMS);
  // const tagCollection = db.collection(COLLECTIONS.TAGS);

  const inventorySnap = await inventoryCollection
    .where("tenantId", "==", tenantId)
    .get();

  // const tagSnap = await tagCollection.where("tenantId", "==", tenantId).get();

  // const tagMap = {};
  // tagSnap.docs.forEach((doc) => {
  //   const data = doc.data();
  //   if (data.activeStatus) tagMap[doc.id] = data.name;
  // });

  const formatVendorNames = (vendors = []) =>
    vendors.length ? vendors.map(({ name }) => name).join(", ") : "";

  const formatTagNames = (tags = []) => {
    if (!tags.length) return "";
    return tags.map(({ name }) => name).join(", ");
    // return tags
    //   .map((tagId) => tagMap[tagId] || "")
    //   .filter(Boolean)
    //   .join(", ");
  };

  const formatTaxesNames = (taxes = []) =>
    taxes.length ? taxes.map(({ name }) => name).join(", ") : "";

  let items = [];
  let packages = [];
  // let ingredients = [];

  inventorySnap.docs.forEach((doc) => {
    const data = doc.data();
    items.push({
      itemName: formatData(data.itemName),
      itemCode: formatData(data.itemCode),
      itemType: formatData(data.itemType),
      category: formatData(data.category?.name),
      subCategory: formatData(data.subCategory?.name),
      tags: formatTagNames(data.tags),
      taxes: formatTaxesNames(data.taxes),
      vendors: formatVendorNames(data.vendors),
      allVendors: formatBool(data.allVendors),
      purchaseUnit: formatData(data.purchaseUnit?.symbol),
      countingUnit: formatData(data.countingUnit?.symbol),
      recipeUnit: formatData(data.recipeUnit?.symbol),
      parLevel: formatNumber(data.parLevel),
      trackExpiry: formatBool(data.trackExpiry),
      stockable: formatBool(data.stockable),
      // prepareQuantity: formatNumber(data.prepareQuantity),
      unitCost: formatNumber(data.unitCost) ? paiseToRupee(data.unitCost) : 0,
      hsnCode: formatData(data.hsnCode),
      ledger: formatData(data.ledger?.name),
      activeStatus: formatActiveStatus(data.activeStatus),
      defaultPackage: formatBool(data.defaultPackage),
      recipe: data.recipe ? formatData(data.recipe?.name) : "",
    });

    // Packages
    if (data.packages && data.packages.length > 0) {
      data.packages.forEach((pkg) => {
        packages.push({
          itemCode: formatData(data.itemCode),
          itemName: formatData(data.itemName),
          name: formatData(pkg.name),
          packageCode: formatData(pkg.packageCode),
          // quantity: formatNumber(pkg.quantity),
          unitCost: formatNumber(pkg.unitCost) ? paiseToRupee(pkg.unitCost) : 0,
        });
      });
    }

    // Ingredients
    // if (
    //   data.itemType === "made" &&
    //   data.ingredients &&
    //   data.ingredients.length
    // ) {
    //   data.ingredients.forEach((ing) => {
    //     ingredients.push({
    //       itemName: formatData(data.itemName),
    //       ingItemCode: formatData(ing.itemCode),
    //       ingItemName: formatData(ing.itemName),
    //       quantity: formatNumber(ing.quantity),
    //     });
    //   });
    // }
  });

  return {
    [SHEET_NAMES.INVENTORY_ITEMS]: items,
    [SHEET_NAMES.INVENTORY_PACKAGES]: packages,
    // [SHEET_NAMES.INVENTORY_INGREDIENTS]: ingredients,
  };
};

// Recipes
const getRecipes = async (tenantId) => {
  const recipeCollection = db.collection(COLLECTIONS.RECEIPES);
  const recipeSnap = await recipeCollection
    .where("tenantId", "==", tenantId)
    .get();
  let recipes = [];
  let ingredients = [];

  const formatTagNames = (tags = []) =>
    tags.length ? tags.map(({ name }) => name).join(", ") : "";

  recipeSnap.docs.forEach((doc) => {
    const data = doc.data();
    recipes.push({
      name: formatData(data.name),
      recipeCode: formatData(data.recipeCode),
      recipeType: formatData(data.recipeType, "recipe"),
      quantity: formatNumber(data.quantity),
      recipeUnit: formatData(data.recipeUnit?.symbol),
      cost: formatNumber(data.cost) ? paiseToRupee(data.cost) : 0,
      tags: formatTagNames(data.tags),
      activeStatus: formatActiveStatus(data.activeStatus),
    });

    // check ingredient
    if (data.ingredients && data.ingredients.length) {
      data.ingredients.forEach((ing) => {
        const formatServiceTypes = (serviceTypes = []) =>
          serviceTypes.length
            ? serviceTypes.map(({ name }) => name).join(", ")
            : "";

        ingredients.push({
          recipeName: formatData(data.name),
          itemCode: formatData(ing.itemCode),
          itemName: formatData(ing.itemName),
          quantity: formatNumber(ing.quantity),
          recipeUnit: formatData(ing.recipeUnit?.symbol),
          yield: formatNumber(ing.yield),
          serviceTypes: formatServiceTypes(ing.serviceType),
          total: formatNumber(ing.totalCost) ? paiseToRupee(ing.totalCost) : 0,
        });
      });
    }
  });
  return {
    [SHEET_NAMES.RECIPES]: recipes,
    [SHEET_NAMES.RECIPE_INGREDIENTS]: ingredients,
  };
};

/**
 * Returns data rows for a given sheet name.
 * Implementation depends on how you source the data (DB, file, API).
 *
 * @param {string} sheetName
 * @returns {any[]} - Data rows.
 */
exports.getData = async (sheetName, tenantId) => {
  switch (sheetName) {
    case SHEET_NAMES.TAGS:
      return getTags(tenantId);
    case SHEET_NAMES.LEDGERS:
      return getLedgers(tenantId);
    case SHEET_NAMES.TAXES:
      return getTaxes(tenantId);
    case SHEET_NAMES.VENDORS:
      return getVendors(tenantId);
    case SHEET_NAMES.CATEGORIES:
      return getCategories(tenantId);
    case SHEET_NAMES.HOUSE_UNITS:
      return getHouseUnits(tenantId);
    case SHEET_NAMES.INVENTORY_ITEMS:
      return getInventoryItems(tenantId);
    case SHEET_NAMES.RECIPES:
      return getRecipes(tenantId);
    default:
      return {};
  }
};
