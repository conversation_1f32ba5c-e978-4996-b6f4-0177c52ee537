// utils/validation.js

const handleValidation = (data, schema) => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  if (error) {
    throw Error(error.details[0].message);
  }
  return value;
};

const trimName = (name) => name.trim().replace(/\s+/g, " ");
const formatName = (name) => name.trim().replace(/\s+/g, "").toLowerCase();

const nameValidation = async (
  rawName,
  db,
  id = null,
  firebaseKey = null,
  value = null
) => {
  const normalizedName = formatName(rawName);

  let query = db.where("nameNormalized", "==", normalizedName);

  if (firebaseKey && value) {
    query = query.where(firebaseKey, "==", value);
  }
  const existingSnap = await query.limit(1).get();

  if (!existingSnap.empty && existingSnap.docs[0].id !== id) {
    return { valid: false, error: "Name Already Exists" };
  }
  return { valid: true, normalizedName };
};

function validateEmail(emailId) {
  if (!emailId) {
    const error = new Error(
      "Unable to send email: The vendor does not have a registered contact email address."
    );
    error.statusCode = 400;
    throw error;
  }

  const emailPattern = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;

  const emails = Array.isArray(emailId) ? emailId : [emailId];

  emails.forEach((email) => {
    if (!emailPattern.test(email)) {
      const error = new Error(`Invalid email`);
      error.statusCode = 400;
      throw error;
    }
  });

  return emails;
}

module.exports = {
  handleValidation,
  trimName,
  formatName,
  nameValidation,
  validateEmail,
};
