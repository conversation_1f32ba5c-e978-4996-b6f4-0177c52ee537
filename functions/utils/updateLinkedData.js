const admin = require("firebase-admin");
const firestore = admin.firestore();
const PROPOGATE_LINKING_MODULES = require("../defs/propogateLinkDefs");

/**
 * Recursively walks through any object/array/flat to update matching names.
 */
function deepUpdateNames(obj, id, newName) {
  if (Array.isArray(obj)) {
    return obj.map((item) => deepUpdateNames(item, id, newName));
  } else if (typeof obj === "object" && obj !== null) {
    const updated = {};
    let changed = false;

    for (const [key, value] of Object.entries(obj)) {
      // 🧩 Case 3: flat fields like vendorId → vendorName
      if (key.endsWith("Id") && value === id) {
        const baseKey = key.slice(0, -2); // removes "Id"
        updated[key] = value;
        updated[`${baseKey}Name`] = newName; // updates corresponding name field
        changed = true;
      }

      // 🧩 Case 1 & 2: nested object or array
      else if (typeof value === "object" && value !== null) {
        if (value.id === id) {
          updated[key] = { ...value, name: newName };
          changed = true;
        } else {
          const newVal = deepUpdateNames(value, id, newName);
          updated[key] = newVal;
          if (JSON.stringify(newVal) !== JSON.stringify(value)) changed = true;
        }
      } else {
        updated[key] = value;
      }
    }

    return changed ? updated : obj;
  } else {
    return obj;
  }
}

function deepUpdateItemNames(obj, itemId, newItemName) {
  let changedKeys = new Set();

  function recursiveUpdate(target, parentKey) {
    if (Array.isArray(target)) {
      const result = target.map((item) => recursiveUpdate(item, parentKey));
      if (JSON.stringify(result) !== JSON.stringify(target)) {
        if (parentKey) changedKeys.add(parentKey);
      }
      return result;
    } else if (typeof target === "object" && target !== null) {
      const updated = {};
      for (const [key, value] of Object.entries(target)) {
        if (key === "itemId" && value === itemId) {
          updated[key] = value;
          updated["itemName"] = newItemName;
          if (parentKey) changedKeys.add(parentKey);
        } else if (typeof value === "object" && value !== null) {
          if (value.itemId === itemId) {
            updated[key] = { ...value, itemName: newItemName };
            if (parentKey) changedKeys.add(parentKey);
          } else {
            updated[key] = recursiveUpdate(value, key);
          }
        } else {
          updated[key] = value;
        }
      }
      return updated;
    } else {
      return target;
    }
  }

  const updatedObj = recursiveUpdate(obj, null);
  return { updatedObj, changedKeys };
}

/**
 * Updates the category name in all linked modules
 * @param {string} categoryId - ID of the category being updated
 * @param {string} newName - New category name
 * @param {Array<string>} modules - Modules to update (e.g., ['inventoryItems'])
 */

async function propagateVendorName(vendorId, newName) {
  const modules = PROPOGATE_LINKING_MODULES.VENDOR;

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      let updateNeeded = false;
      const updatePayload = {};

      // CASE 1: array
      if (Array.isArray(data.vendors)) {
        const updatedVendors = data.vendors.map((vendor) =>
          vendor.id === vendorId ? { ...vendor, name: newName } : vendor
        );

        if (JSON.stringify(updatedVendors) !== JSON.stringify(data.vendors)) {
          updateNeeded = true;
          updatePayload.vendors = updatedVendors;
        }
      }

      // CASE 2: single object
      else if (data.vendor && data.vendor.id === vendorId) {
        updateNeeded = true;
        updatePayload.vendor = { ...data.vendor, name: newName };
      }

      // CASE 3: fields (flat structure)
      else if (data.vendorId === vendorId) {
        updateNeeded = true;
        updatePayload.vendorName = newName;
      }

      // CASE 4: nested object
      else if (Array.isArray(data.items)) {
        const updatedItems = data.items.map((item) => {
          if (item.vendor && item.vendor.id === vendorId) {
            return {
              ...item,
              vendor: { ...item.vendor, name: newName },
            };
          }
          return item;
        });

        if (JSON.stringify(updatedItems) !== JSON.stringify(data.items)) {
          updateNeeded = true;
          updatePayload.items = updatedItems;
        }
      }

      if (updateNeeded) {
        batch.update(doc.ref, updatePayload);
      }
    });

    await batch.commit();
  }
}

async function propagateCategoryName(categoryId, newName) {

  const modules = PROPOGATE_LINKING_MODULES.CATEGORY;

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      let updateNeeded = false;
      const updatePayload = {};

      // CASE 1: array
      if (Array.isArray(data.categories)) {
        const updatedCategories = data.categories.map((category) =>
          category.id === categoryId ? { ...category, name: newName } : tax
        );

        if (JSON.stringify(updatedCategories) !== JSON.stringify(data.categories)) {
          updateNeeded = true;
          updatePayload.categories = updatedCategories;
        }
      }

      // CASE 2: single object
      else if (data.category && data.category.id === categoryId) {
        updateNeeded = true;
        updatePayload.category = { ...data.category, name: newName };
      }

      // CASE 3: fields (flat structure)
      else if (data.categoryId === categoryId) {
        updateNeeded = true;
        updatePayload.categoryName = newName;
      }

      if (updateNeeded) {
        batch.update(doc.ref, updatePayload);
      }
    });

    await batch.commit();
  }
}

async function propagateTaxName(taxId, newName) {
  const modules = PROPOGATE_LINKING_MODULES.TAX;

  console.log("Propagating tax name change:", taxId, newName);

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      let updateNeeded = false;
      const updatePayload = {};

      // CASE 1: array
      if (Array.isArray(data.taxes)) {
        const updatedTaxes = data.taxes.map((tax) =>
          tax.id === taxId ? { ...tax, name: newName } : tax
        );

        if (JSON.stringify(updatedTaxes) !== JSON.stringify(data.taxes)) {
          updateNeeded = true;
          updatePayload.taxes = updatedTaxes;
        }
      }

      if (updateNeeded) {
        batch.update(doc.ref, updatePayload);
      }
    });

    await batch.commit();
  }
}

async function propagateHouseUnitName(unitId, newName) {
  const modules = PROPOGATE_LINKING_MODULES.HOUSE_UNIT;

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      const updatedData = deepUpdateNames(data, unitId, newName);

      // Compare — only update if changed
      if (JSON.stringify(updatedData) !== JSON.stringify(data)) {
        batch.update(doc.ref, updatedData);
      }
    });

    await batch.commit();
  }
}

async function propagateInventoryItemName(itemId, newName) {
  const modules = PROPOGATE_LINKING_MODULES.INVENTORY_ITEMS;

  console.log(itemId, newName)

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();
    let batchHasChanges = false;

    snapshot.forEach((doc) => {
      const data = doc.data();
      const { updatedObj, changedKeys } = deepUpdateItemNames(data, itemId, newName);

      if (changedKeys.size > 0) {
        const updatePayload = {};
        for (const key of changedKeys) {
          updatePayload[key] = updatedObj[key];
        }

        batch.update(doc.ref, updatePayload);
        batchHasChanges = true;
        console.log(`✅ Updated ${module}/${doc.id} (fields: ${[...changedKeys].join(", ")})`);
      }
    });

    if (batchHasChanges) {
      await batch.commit();
      console.log(`💾 Committed batch for ${module}`);
    } else {
      console.log(`⚠️ No changes needed for ${module}`);
    }
  }
}

async function propagateLocationName(locationId, newName){
  const modules = PROPOGATE_LINKING_MODULES.LOCATIONS;

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      let updateNeeded = false;
      const updatePayload = {};

      // CASE 1: array
      if (Array.isArray(data.location)) {
        const updatedLocation = data.location.map((location) =>
          location.id === locationId ? { ...location, name: newName } : location
        );

        if (JSON.stringify(updatedLocation) !== JSON.stringify(data.location)) {
          updateNeeded = true;
          updatePayload.taxes = updatedLocation;
        }
      }

      // CASE 2: single object
      else if (data.location && data.location.id === locationId) {
        updateNeeded = true;
        updatePayload.location = { ...data.location, name: newName };
      }

      // CASE 3: fields (flat structure)
      else if (data.locationId === locationId) {
        updateNeeded = true;
        updatePayload.locationName = newName;
      }

      if (updateNeeded) {
        batch.update(doc.ref, updatePayload);
      }
    });

    await batch.commit();
  }
}

async function propagateWorkareaName(workareaId, newName){
  const modules = PROPOGATE_LINKING_MODULES.WORKAREAS;

  for (const module of modules) {
    const snapshot = await firestore.collection(module).get();
    if (snapshot.empty) continue;

    const batch = firestore.batch();

    snapshot.forEach((doc) => {
      const data = doc.data();
      let updateNeeded = false;
      const updatePayload = {};

      // CASE 1: array
      if (Array.isArray(data.inventoryLocation)) {
        const updatedinventoryLocations = data.inventoryLocation.map((workarea) =>
          workarea.id === workareaId ? { ...workarea, name: newName } : workarea
        );

        if (JSON.stringify(updatedinventoryLocations) !== JSON.stringify(data.inventoryLocation)) {
          updateNeeded = true;
          updatePayload.inventoryLocation = updatedinventoryLocations;
        }
      }

      // CASE 2: single object
      else if (data.inventoryLocation && data.inventoryLocation.id === workareaId) {
        updateNeeded = true;
        updatePayload.inventoryLocation = { ...data.inventoryLocation, name: newName };
      }

      // CASE 3: fields (flat structure)
      else if (data.inventoryLocationId === workareaId) {
        updateNeeded = true;
        updatePayload.inventoryLocationName = newName;
      }

      if (updateNeeded) {
        batch.update(doc.ref, updatePayload);
      }
    });

    await batch.commit();
  }
}


module.exports = {
  propagateCategoryName,
  propagateVendorName,
  propagateTaxName,
  propagateHouseUnitName,
  propagateInventoryItemName,
  propagateLocationName,
  propagateWorkareaName
};
