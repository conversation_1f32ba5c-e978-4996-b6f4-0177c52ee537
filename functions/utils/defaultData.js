// Default units data
const defaultUnits = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    symbol: "kg",
    tenantId: null,
    quantity: 1000,
    toUnit: "g",
    default: true,
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    symbol: "l",
    tenantId: null,
    quantity: 1000,
    toUnit: "ml",
    default: true,
  },
  {
    id: "3",
    name: "Numbers",
    symbol: "nos",
    tenantId: null,
    quantity: null,
    toUnit: null,
    default: true,
  },
  {
    id: "4",
    name: "Gram",
    symbol: "g",
    tenantId: null,
    quantity: null,
    toUnit: null,
    default: true,
  },
  {
    id: "5",
    name: "<PERSON><PERSON><PERSON>",
    symbol: "ml",
    tenantId: null,
    quantity: null,
    toUnit: null,
    default: true,
  },
];

// Export the default units as a constant
const DEFAULT_UNITS = Object.freeze(defaultUnits);

module.exports = {
  DEFAULT_UNITS,
};
