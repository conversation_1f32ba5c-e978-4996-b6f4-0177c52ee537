const UNIT_CONVERSION = {
  // weight in grams
  mg: { base: "g", factor: 0.001 },
  g: { base: "g", factor: 1 },
  gm: { base: "g", factor: 1 },
  kg: { base: "g", factor: 1000 },

  // volume in milliliters
  ml: { base: "ml", factor: 1 },
  l: { base: "ml", factor: 1000 },
  ltr: { base: "ml", factor: 1000 },
  litre: { base: "ml", factor: 1000 },

  // count-based units
  nos: { base: "count", factor: 1 },
  pcs: { base: "count", factor: 1 },
  piece: { base: "count", factor: 1 },
};

const getConvertedQuantity = (quantity, fromUnit, toUnit) => {
  const from = UNIT_CONVERSION[fromUnit.toLowerCase()];
  const to = UNIT_CONVERSION[toUnit.toLowerCase()];

  if (!from || !to) {
    throw new Error(
      `Unsupported unit conversion from '${fromUnit}' to '${toUnit}'`
    );
  }

  if (from.base !== to.base) {
    throw new Error(
      `Incompatible units: cannot convert '${fromUnit}' to '${toUnit}'`
    );
  }

  const baseQty = quantity * from.factor;
  return baseQty / to.factor;
};

module.exports = {
  getConvertedQuantity,
  rupeeToPaise,
  paiseToRupee,
};
