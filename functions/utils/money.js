const rupeeToPaise = (amount) => {
  return truncateNumber(amount) * 100;
};

const paiseToRupee = (amount) => {
  return truncateNumber(amount / 100);
};

const calculateRecipeCost = (ingredient, quantityInRecipeUnit) => {
  const { unitCost, recipeUnit } = ingredient;
  // unitCost is cost for 1 purchaseUnit (ex: 1 kg)
  // recipeUnit.conversion = how many recipeUnits in 1 purchaseUnit
  const constPerRecipeUnit = unitCost / (recipeUnit.conversion || 1);
  return quantityInRecipeUnit * constPerRecipeUnit;
};

const truncateNumber = (num, precision = 2) => {
  if (typeof num === "string") {
    num = parseFloat(num);
  }
  if (isNaN(num)) {
    num = 0;
  }

  const factor = 10 ** precision;
  return Math.trunc(num * factor) / factor;
};

module.exports = {
  rupeeToPaise,
  paiseToRupee,
  calculateRecipeCost,
  truncateNumber,
};
