// utils/storage.js
const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');
const ATTACHMENT_TYPES = require('@/defs/attachmentTypesDefs');

const storage = admin.storage().bucket();

/**
 * Sets CORS configuration for Firebase Storage.
 *
 * If in development mode, allows requests from http://localhost:5174
 * and sets the response header to include Content-Type and x-goog-meta-*
 * and sets the max age to 1 hour.
 */
async function setCors() {
    // Allowed origins
    const origins = [process.env.WEB_APP_URI];
    // If not production, allow localhost
    if (process.env.NODE_ENV !== 'production') {
        origins.push('http://localhost:5174'); // add other dev ports if needed
    }

    const corsConfiguration = [
        {
            origin: origins, // allowed origins
            method: ['GET', 'PUT', 'POST', 'HEAD'],    // allowed HTTP methods
            responseHeader: ['Content-Type', 'x-goog-meta-*'], // allowed response headers
            maxAgeSeconds: 3600, // preflight cache
        },
    ];

    await storage.setCorsConfiguration(corsConfiguration);
    console.log('CORS configuration updated!');
}

setCors().catch(console.error);

/**
 * Generates signed write URLs for uploading multiple files to Firestore Storage
 * @param {{ tenantId: string, type: string, docId: string, files: Array<{ name: string, contentType: string }>} options
 * @returns {Promise<Array<{ filePath: string, fileUrl: string }>>} A promise with an array of signed URLs
 * @throws {Error} if attachment type is invalid or files is an empty array
 */
async function generateUploadUrls({ tenantId, type, docId = 'default', files = [] }) {
    if (!Object.values(ATTACHMENT_TYPES).includes(type)) {
        throw new Error(`Invalid attachment type: ${type}`);
    }
    if (!Array.isArray(files) || files.length === 0) {
        throw new Error('files must be a non-empty array');
    }

    const expiresMs = Date.now() + 15 * 60 * 1000; // 15 minutes

    return Promise.all(
        files.map(async (file) => {
            const filePath = `${tenantId}/${type}/${docId}/${uuidv4()}_${file.name}`;
            const fileRef = storage.file(filePath);

            const [fileUrl] = await fileRef.getSignedUrl({
                version: 'v4',
                action: 'write',
                expires: expiresMs,
                ...(file.contentType ? { contentType: file.contentType } : {}),
            });

            return { 
                fileName: file.name, // store the original file name
                filePath, // store the full file path
                fileUrl // write signed url, no need save
             };
        })
    );
}


/**
 * Generates signed read URLs for downloading files from Firestore Storage
 * @param {Array<string>} files An array of file paths in Firestore Storage
 * @returns {Promise<Array<{ filePath: string, fileUrl: string }>>} A promise with an array of signed URLs
 * @throws {Error} if filePaths is an empty array
 */
async function generateReadUrls(files = []) {
    if (!Array.isArray(files) || files.length === 0) return [];

    const expiresMs = Date.now() + 15 * 60 * 1000; // 15 minutes

    return Promise.all(
        files.map(async (file) => {
            const fileRef = storage.file(file.filePath);
            const [fileUrl] = await fileRef.getSignedUrl({
                version: 'v4',
                action: 'read',
                expires: expiresMs,
            });
            return { 
                ...file, 
                fileUrl
             };
        })
    );
}


/**
 * Returns a publicly accessible URL for the given file path in Firestore Storage.
 * Note that this method makes the file publicly readable, so use with caution.
 * @param {string} filePath - Full file path in Firestore Storage (tenant/type/docId/uuid_name.ext)
 * @returns {Promise<string>} A promise with the publicly accessible URL
 */
async function getPublicUrl(filePath) {
    const fileRef = storage.file(filePath);
    await fileRef.makePublic();
    return `https://storage.googleapis.com/${storage.name}/${encodeURIComponent(filePath)}`;
}


/**
 * Downloads a file from Firestore Storage and returns its contents as a Buffer.
 * @param {string} filePath - Full file path in Firestore Storage (tenant/type/docId/uuid_name.ext)
 * @returns {Promise<Buffer>} A promise with the file contents as a Buffer
 */
async function getFile(filePath) {
    const fileRef = storage.file(filePath);
    const [content] = await fileRef.download();
    return content; // Buffer
}


/**
 * Deletes multiple files from Firestore Storage in parallel.
 * @param {Array<string>} fileNames An array of file paths in Firestore Storage
 * @returns {Promise<void>} A promise that resolves when all files have been deleted
 * @throws {Error} if fileNames is not an array or is empty
 */
async function deleteFiles(fileNames) {
    if (!Array.isArray(fileNames) || !fileNames.length) return;

    await Promise.all(
        fileNames.map(async (name) => {
            const fileRef = storage.file(name);
            try {
                await fileRef.delete();
            } catch (err) {
                console.error(`Failed to delete ${name}:`, err.message);
            }
        })
    );
}

module.exports = {
    generateUploadUrls,
    generateReadUrls,
    getPublicUrl,
    getFile,
    deleteFiles,
};
