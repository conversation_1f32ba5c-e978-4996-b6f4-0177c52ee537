const Joi = require("joi");
const { transferStatus } = require("@/defs/transferStatusDefs");

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  }),
);

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
  time: timestampSchema.required(),
});

const locationSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
  locationName: Joi.string().required(),
  locationId: Joi.string().required(),
});

const dispatchItemsSchema = Joi.object({
  itemId: Joi.string().required(),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
    toUnit: Joi.string().optional(),
    emptyWeight: Joi.number().default(0).optional(),
    fullWeight: Joi.number().default(0).optional(),
  })
    .allow(null)
    .optional(),
  dispatchedQuantity: Joi.number()
    .min(0)
    .required()
    .messages({ "number.min": "Dispatched Quantity Cannot be Less than Zero" }),
  receivedQuantity: Joi.number()
    .min(0)
    .optional()
    .messages({ "number.min": "Received Quantity Cannot be Less than Zero" }),
  shortageQuantity: Joi.number()
    .min(0)
    .optional()
    .messages({ "number.min": "Shortage Quantity Cannot be Less than Zero" }),
  reason: Joi.string().allow("").optional(),
  totalValue: Joi.number().optional(),
  wac: Joi.number().optional(),
  ledgerId: Joi.string().optional(),
  dispatchDate: Joi.any()
    .optional()
    .allow(null)
    .description("Date of the Dispatch"),
  receiveDate: Joi.any()
    .optional()
    .allow(null)
    .description("Date of the Receive"),
});

const historySchema = Joi.object({
  dispatchNo: Joi.string().optional(),
  status: Joi.string()
    .valid(
      transferStatus.PENDING,
      transferStatus.PARTIAL,
      transferStatus.COMPLETED,
    )
    .optional(),
  items: Joi.array()
    .items(dispatchItemsSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Item is Required" }),
  dispatchedBy: userSchema.optional(),
  receivedBy: userSchema.optional(),
});

const itemSchema = Joi.object({
  ordinal: Joi.number().integer().required(),
  itemId: Joi.string().required(),
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  categoryId: Joi.string().required(),
  subcategoryId: Joi.string().required(),
  categoryName: Joi.string().required(),
  subcategoryName: Joi.string().required(),
  fifoCost: Joi.number().optional(),
  unitCost: Joi.number().optional(),
  totalValueDispatch: Joi.number().optional(),
  totalValueReceive: Joi.number().optional(),
  stockable: Joi.boolean().default(false),
  purchaseUOM: Joi.string().required(),
  countingUOM: Joi.string().allow(null, "").optional(),
  itemType: Joi.string().allow(null, "").optional(),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
    toUnit: Joi.string().optional(),
    emptyWeight: Joi.number().default(0).optional(),
    fullWeight: Joi.number().default(0).optional(),
  })
    .allow(null)
    .optional(),
  requestedQuantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Requested Quantity Must be Greater than Zero" }),
  dispatchedQuantity: Joi.number()
    .min(0)
    .optional()
    .messages({ "number.min": "Dispatched Quantity Cannot be Less than Zero" }),
  receivedQuantity: Joi.number()
    .min(0)
    .optional()
    .messages({ "number.min": "Received Quantity Cannot be Less than Zero" }),
  shortageQuantity: Joi.number()
    .min(0)
    .optional()
    .messages({ "number.min": "Shortage Quantity Cannot be Less than Zero" }),
});

const transferSchema = Joi.object({
  id: Joi.string(),
  issuer: locationSchema.required(),
  requester: locationSchema.required(),
  transferNumber: Joi.string().optional(),
  tenantId: Joi.string().required(),
  items: Joi.array()
    .items(itemSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Item is Required" }),
  dispatchStatus: Joi.string()
    .valid(
      transferStatus.PENDING,
      transferStatus.PARTIAL,
      transferStatus.COMPLETED,
    )
    .optional(),
  receiveStatus: Joi.string()
    .valid(
      transferStatus.PENDING,
      transferStatus.PARTIAL,
      transferStatus.COMPLETED,
    )
    .optional(),
  requestedBy: userSchema.required(),
  timeLine: Joi.array().items(historySchema).optional(),
  stockableItems: Joi.boolean().optional().default(false),
  transferDate: Joi.any()
    .optional()
    .allow(null)
    .description("Date of the Transfer"),
  grnNumber: Joi.array().items(Joi.string()).optional(),
  remarks: Joi.string().allow(null, "").optional(),
});

module.exports = transferSchema;
