const Joi = require("joi");
const { LedgerTypes, StockTransactionType } = require("@/defs/ledgerDefs");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

/**
 * StockLedger
 * ------------
 * Transaction log of all inventory movements.
 * Firestore docId acts as the ledgerId.
 */
const stockLedgerSchema = Joi.object({
  tenantId: Joi.string().required(),

  locationId: Joi.string().required(),
  locationName: Joi.string().required(),

  inventoryLocationId: Joi.string().required(),
  inventoryLocationName: Joi.string().required(),

  itemId: Joi.string().required(),
  itemCode: Joi.string().required(),
  itemName: Joi.string().required(),
  // @add package id, name
  // @add categoryId, categoryName, subcategoryId, name

  // 🔹 Ledger type & transaction direction
  ledgerType: Joi.string()
    .valid(...Object.values(LedgerTypes))
    .required(),

  transactionType: Joi.string()
    .valid(...Object.values(StockTransactionType))
    .required(),

  // 🔹 Movement quantities
  qty: Joi.number().min(0).required(),
  remainingQty: Joi.number().min(0).required(),

  // 🔹 Always in counting UOM
  countingUOM: Joi.string().required(),

  // 🔹 Costing
  // @todo amt details
  unitCost: Joi.number().precision(4).required(),
  totalCost: Joi.number().precision(2).required(),

  // 🔹 Expiry (if applicable)
  expiryDate: Joi.date().optional(),

  // 🔹 Business references (PO, PR, Vendor…)
  grnMeta: Joi.object({
    id: Joi.string().required(),
    grnNumber: Joi.string().required(),
    poId: Joi.string().optional(),
    vendorId: Joi.string().optional(),
    vendorName: Joi.string().optional(),
    // @todo: 
    // grnDate, invoiceDate
  }).optional(),

  // 🔹 User reference (who initiated)
  // @todo flatten to query easily
  userRef: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).optional(),

  // 🔹 Ledger reference (link back to the IN ledger doc)
  ledgerRefId: Joi.string().required(),

  createdAt: Joi.date().default(() => FD.now()),
  updatedAt: Joi.date().default(() => FD.now()),
});

module.exports = { stockLedgerSchema };
