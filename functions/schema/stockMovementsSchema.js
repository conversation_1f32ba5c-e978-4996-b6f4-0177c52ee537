const Joi = require("joi");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

/**
 * MovementBlock
 * --------------
 * Represents a quantity + value movement for a given day.
 *
 * IMPORTANT INVARIANTS:
 * - qty and totalValue MUST have the same sign
 * - qty > 0  => stock increase
 * - qty < 0  => stock decrease
 * - totalValue represents COST value (not selling price)
 */
const MovementBlockSchema = Joi.object({
  qty: Joi.number()
    .precision(3)
    .required(),

  uom: Joi.string()
    .trim()
    .required(),

  // Cost value corresponding to qty (signed)
  totalValue: Joi.number()
    .precision(2)
    .required()
});

/**
 * StockMovementSchema
 * -------------------
 * Daily stock movement projection for ONE inventory item,
 * scoped to a single inventory location and business date.
 *
 * This is a DERIVED / PROJECTED document.
 * Source of truth remains the ledger.
 *
 * Date uniqueness is enforced by Firestore path:
 *   stockMovements/{YYYY-MM-DD}/items/{inventoryItemId}
 */
const StockMovementSchema = Joi.object({
  /* ============================
   * Context / Path Identifiers
   * ============================
   */
  tenantId: Joi.string().required(),

  locationId: Joi.string().required(),
  locationName: Joi.string().required(),

  inventoryLocationId: Joi.string().required(),
  inventoryLocationName: Joi.string().required(),

  /* ============================
   * Item Identity
   * ============================
   */
  inventoryItemId: Joi.string().required(),
  inventoryItemCode: Joi.string().required(),
  inventoryItemName: Joi.string().required(),

  /* ============================
   * Business Date
   * ============================
   * ISO date string (YYYY-MM-DD)
   * Also enforced by document path.
   */
  businessDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required(),

  /* ============================
   * Movement Buckets
   * ============================
   * These represent SAME-DAY aggregated movements.
   * All fields are OPTIONAL because not every movement
   * happens every day.
   */

  /**
   * Opening stock
   * -------------
   * Derived automatically:
   * opening(today) = finalClosing(yesterday)
   *
   * Never manually edited.
   */
  opening: MovementBlockSchema.optional(),

  /**
   * Purchase (GRN)
   * --------------
   * Total quantity & value received from suppliers for the day.
   */
  purchase: MovementBlockSchema.optional(),

  /**
   * Transfer In
   * -----------
   * Stock received from another inventory location.
   */
  transferIn: MovementBlockSchema.optional(),

  /**
   * Transfer Out
   * ------------
   * Stock sent to another inventory location.
   */
  transferOut: MovementBlockSchema.optional(),

  /**
   * Consumption
   * -----------
   * Stock consumed internally (sales, production, recipes).
   * Always qty-negative.
   */
  consumption: MovementBlockSchema.optional(),

  /**
   * Adjustment
   * ----------
   * Manual or system adjustments NOT related to physical closing.
   * Examples:
   * - data correction
   * - migration fix
   * - admin override
   *
   * Physical overwrite adjustments DO NOT go here.
   */
  adjustment: MovementBlockSchema.optional(),

  /**
   * Spoilage
   * --------
   * Stock written off due to damage / expiry.
   * Always qty-negative.
   */
  spoilage: MovementBlockSchema.optional(),

  /* ============================
   * Closing & Physical Count
   * ============================
   */

  /**
   * System Closing (Expected)
   * -------------------------
   * System-calculated stock BEFORE physical overwrite.
   *
   * Formula:
   * opening
   * + purchase
   * + transferIn
   * - transferOut
   * - consumption
   * - spoilage
   * + adjustment
   */
  systemClosing: MovementBlockSchema.optional(),

  /**
   * Physical Closing
   * ----------------
   * User-entered physical stock count.
   *
   * IMPORTANT:
   * - This is an OBSERVATION, not a movement.
   * - May be missing if no count was done.
   * - qty = 0 is VALID and different from "not entered".
   */
  physicalClosing: MovementBlockSchema.optional(),

  /**
   * Closing Adjustment
   * ------------------
   * Materialized effect of a physical stock overwrite.
   *
   * Derived from ledger entries where:
   *   ledgerType = ADJUSTMENT
   *   reasonCode = PHYSICAL_STOCK_OVERRIDE
   *
   * May be zero.
   * Exists ONLY after user confirms overwrite.
   */
  closingAdjustment: MovementBlockSchema.optional(),

  /**
   * Final Closing (Current Stock)
   * -----------------------------
   * Stock carried forward to next day.
   *
   * Formula:
   * finalClosing = systemClosing + closingAdjustment
   */
  finalClosing: MovementBlockSchema.optional(),

  /* ============================
   * System Metadata
   * ============================
   */
  createdAt: Joi.date()
    .default(() => FD.now()),

  updatedAt: Joi.date()
    .default(() => FD.now())
})
  // Disallow unknown fields to keep projection clean
  .unknown(false);

module.exports = {
  StockMovementSchema
};
