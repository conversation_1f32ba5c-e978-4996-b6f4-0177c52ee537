// schemas/consumptionDataSchema.js
const Joi = require("joi");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const invItemSchema = Joi.object({
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  itemType: Joi.string().required(),
  itemId: Joi.string().required(),
  recipeUom: Joi.string().required(),
  cost: Joi.number().min(0).required(),
  requiredQty: Joi.number().positive().required(),
});

const consumptionDataSchema = Joi.object({
  tenantId: Joi.string().required(),
  saleReferenceId: Joi.number().integer().positive().required(),
  accountId: Joi.number().integer().positive().required(),
  storeId: Joi.number().integer().positive().required(),
  ticketNo: Joi.string().required(),
  tableNo: Joi.string().required(),
  floorNo: Joi.number().integer().positive().required(),
  floorName: Joi.string().allow('').optional(),
  
  // Type field to distinguish menu items from modifiers
  type: Joi.string().valid('menu', 'modifier').optional(),
  
  // POS Menu Item details
  posMenuItemName: Joi.string().allow('').required(),
  posItemCode: Joi.string().allow('').required(),
  menuItemId: Joi.number().integer().optional(),
  
  // Modifier-specific fields (only present when type is 'modifier')
  modifierName: Joi.string().allow('').optional(),
  modifierId: Joi.number().integer().positive().optional(),
  modifierGroup: Joi.string().valid('mandatory', 'optional').optional(),
  modifierGroupId: Joi.number().integer().positive().optional(),
  
  // Serving size details
  servingSizeId: Joi.number().integer().positive().required(),
  servingSizeName: Joi.string().allow('').required(),
  
  // Sale details
  soldQuantity: Joi.number().positive().required(),
  posItemTotalAmount: Joi.number().min(0).required(),
  
  // Work area and location (optional for error cases)
  workArea: Joi.string().allow(null, '').optional(),
  createdAt: Joi.date().default(() => FD.now()),
  workAreaId: Joi.string().allow(null, '').optional(),
  location: Joi.string().allow(null, '').optional(),
  locationId: Joi.string().allow(null, '').optional(),
  
  // Inventory item details (optional, present only when linked)
  invItemName: Joi.string().allow(null, '').optional(),
  invItemCode: Joi.string().allow(null, '').optional(),
  invItems: Joi.array().items(invItemSchema).optional(),
  
  // Status fields (for error tracking)
  status: Joi.string().allow('').optional(),
  statusSummary: Joi.string().allow('').optional(),
});

module.exports = consumptionDataSchema;