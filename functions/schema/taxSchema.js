const Joi = require("joi");

// Main tax schema
const taxSchema = Joi.object({
  tenantId: Joi.string().required(),
  name: Joi.string().trim().required(), // e.g., "GST"
  nameNormalized: Joi.string().max(100).optional(),
  valueType: Joi.string().valid("amount", "percentage").default("amount"), // 1 - amount, 2 - percentage
  valueAmt: Joi.number().min(0).default(0).when("valueType", {
    is: "amount",
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  valuePercentage: Joi.number().min(0).default(0).when("valueType", {
    is: "percentage",
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  taxLevel: Joi.string().valid("item", "grn").default("item"),
  activeStatus: Joi.boolean().default(true), // true = active, false = inactive
  components: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().trim().required(),
        valueAmt: Joi.number().min(0).default(0),
        valuePercentage: Joi.number().min(0).default(0),
      })
    )
    .optional(), // optional sub-taxes
});

module.exports = taxSchema;
