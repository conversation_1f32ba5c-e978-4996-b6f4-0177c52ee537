const Joi = require("joi");

/**
 * Joi schema for validating Counter documents.
 *
 * Fields:
 * - tenantId   : string (required)
 * - key        : string (required)
 * - prefix     : string (required)
 * - nextNumber : integer >= 1 (required)
 */
const counterSchema = Joi.object({
  tenantId: Joi.string().required(),
  key: Joi.string().required(),
  prefix: Joi.string().required(),
  nextNumber: Joi.number().integer().min(1).required(),
});

module.exports = counterSchema;
