const Joi = require("joi");

const chargeSchema = Joi.object({
  tenantId: Joi.string().optional(),
  name: Joi.string().trim().required(),
  nameNormalized: Joi.string().max(100).optional(),
  type: Joi.string().default("charge"),
  valueType: Joi.string().valid("amount", "percentage").default("amount"),
  valueAmt: Joi.number().min(0).when("valueType", {
    is: "amount",
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  valuePercentage: Joi.number().min(0).when("valueType", {
    is: "percentage",
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
  activeStatus: Joi.boolean().default(true),
});

module.exports = chargeSchema;
