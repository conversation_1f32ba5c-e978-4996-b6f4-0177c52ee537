const {
  aggregatePurchaseRequests,
  createPR,
  getPRById,
  updatePR,
  approvePR,
  rejectPR,
  convertPO,
  completePR,
  closePR
} = require("@/services/purchaseService");

const { renderPDF } = require("@/helpers/render");

const {
  generatePrint,
  generateHeader,
  generateDetails,
  generateDynamicTable,
  generateItemsTable
} = require("@/helpers/printHelper");

const { validateIdentityFilters } = require("@/helpers/reportHelper");

const { preparePRData, getLocationById } = require("@/services/printService");

exports.createPurchaseRequest = async (req, res) => {
  const payload = {
    ...req.body,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName
    }
  };
  try {
    const purchaseRequest = await createPR(payload);
    res.status(201).json(purchaseRequest);
  } catch (error) {
    console.error("Error creating purchase request:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.approvePurchaseRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { userName, userId } = req.identity;

    const approvedBy = {
      id: userId,
      name: userName
    };

    if (!approvedBy.id || !approvedBy.name) {
      throw Error("Missing approvedBy");
    }

    const result = await approvePR(id, approvedBy);

    if (!result) {
      throw Error("Purchase request not found");
    }
    res.status(200).json({ message: "Purchase request approved" });
  } catch (error) {
    console.error("Error approving purchase request:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.rejectPurchaseRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { rejectedReason } = req.body;
    const { userName, userId } = req.identity;
    const rejectedBy = {
      id: userId,
      name: userName
    };

    if (!rejectedBy || !rejectedBy.id || !rejectedBy.name) {
      return res
        .status(400)
        .json({ message: "Missing rejectedBy (name & id)" });
    }

    const result = await rejectPR(id, rejectedBy, rejectedReason);

    if (!result) {
      throw Error("Purchase request not found");
    }

    res.status(200).json({ message: "Purchase request rejected" });
  } catch (error) {
    console.error("Error in rejectPurchaseRequest:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.closePurchaseRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const { userName, userId } = req.identity;
    const closedBy = {
      id: userId,
      name: userName
    };

    if (!closedBy || !closedBy.id || !closedBy.name) {
      return res.status(400).json({ message: "Missing closedBy (name & id)" });
    }

    const result = await closePR(id, closedBy, reason);

    if (!result) {
      throw Error("Purchase request not found");
    }

    res.status(200).json({ message: "Purchase request Closed" });
  } catch (error) {
    console.error("Error in closePurchaseRequest:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getPurchaseRequests = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        message: "tenantId is required"
      });
    }

    const filters = {
      status: req.body.status || null,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null
    };

    const _filters = validateIdentityFilters(req.identity, filters);

    const result = await aggregatePurchaseRequests(tenantId, _filters);
    res.status(200).json(result);
  } catch (error) {
    console.error("Error:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getPurchaseRequestById = async (req, res) => {
  try {
    const { id } = req.params;
    const purchaseRequest = await getPRById(id);

    if (!purchaseRequest) {
      return res.status(404).json({ message: "purchase request not found" });
    }

    res.status(200).json(purchaseRequest);
  } catch (error) {
    console.error("Error fetching purchase request:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updatePurchaseRequest = async (req, res) => {
  const payload = {
    ...req.body,
    updatedBy: {
      id: req.identity.userId,
      name: req.identity.userName
    }
  };
  try {
    await updatePR(req.params.id, payload);

    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating purchase request:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.convertToPurchaseOrder = async (req, res) => {
  const payload = {
    id: req.params.id,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName
    }
  };
  try {
    const pr = await getPRById(payload.id, false);
    if (!pr) {
      throw Error("Purchase request not found");
    }

    await convertPO(pr);

    await completePR(payload.id, {
      ...pr,
      updatedBy: payload.requestedBy
    });

    res.status(201).json({
      message: "Purchase Orders created"
    });
  } catch (error) {
    console.error("Error converting to purchase order:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a purchase request to a PDF file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Purchase Request ID
 * @returns {Promise<void>}
 */
exports.exportToPDF = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const prData = await preparePRData(id, tenantId);
    if (!prData) throw new Error("No purchase request found");

    const { pr, firstCol, secondCol, itemsTable } = prData;
    const locationData = await getLocationById(tenantId, pr.location.id);
    const content = [
      generateHeader("Purchase Request", pr.tenantName, locationData?.name),
      generateDetails(firstCol, secondCol),
      generateDynamicTable(itemsTable)
      // generateItemsTable(pr.items),
    ];
    const pdf = await generatePrint(content);
    renderPDF(res, pdf, `${pr.prNumber}.pdf`);
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a purchase request to a XLSX file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - Purchase Request ID
 * @returns {Promise<void>}
 */
exports.exportToXLSX = async (req, res) => {
  try {
    // @todo implement
    throw new Error("Not implemented");
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};
