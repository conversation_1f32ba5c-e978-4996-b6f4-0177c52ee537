// src/controllers/masterController.js
const masterService = require("@/services/masterService");

/**
 * Fetches all master data from the database.
 * @returns {Promise<Object>} A promise that resolves to an object containing the master data.
 * @throws {Error} An error that occurred while fetching the master data.
 */
async function getMasterData(req, res) {
  try {
    const { tenantId } = req.params;
    const data = await masterService.getAllMasters(tenantId);
    return res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching master data:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch master data",
      error: error.message,
    });
  }
}

async function getMasterInventoryItemData(req, res) {
  try {
    const { tenantId } = req.params;
    const data = await masterService.getInventoryMasters(tenantId);
    return res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching master inventory data:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch master inventory data",
      error: error.message,
    });
  }
}

module.exports = { getMasterData, getMasterInventoryItemData };
