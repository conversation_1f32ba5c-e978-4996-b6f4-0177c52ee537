const admin = require("firebase-admin");
const schema = require("@/models/userSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.USERS);
const { handleValidation, trimName } = require("@/utils/validation");
const axios = require("axios");

exports.insertUser = async (req, res) => {
  try {
    const validatedData = handleValidation({ ...req.body }, schema);
    if (!validatedData) return;

    const { tenantId, tenantName, emailId, name } = validatedData;
    const duplicateEmailId = await db
      .where("tenantId", "==", tenantId)
      .where("emailId", "==", emailId)
      .limit(1)
      .get();

    if (!duplicateEmailId.empty) {
      return res.status(409).json({ message: "Email ID Already Exists" });
    }

    const cleanName = trimName(name);
    const newDocRef = db.doc();

    const user = {
      ...validatedData,
      id: newDocRef.id,
      name: cleanName,
      activeStatus: validatedData.activeStatus ?? true,
      digitorySsoId: "",
    };

    await newDocRef.set(user);

    await axios.post(`${process.env.AUTH_SERVER_URL}/sso/invite-user`, {
      email: validatedData.emailId,
      clientId: process.env.CLIENT_ID,
      userId: newDocRef.id,
      tenantId,
      tenantName,
    });

    res.status(201).json(user);
  } catch (error) {
    console.error("Insert User Failed:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getUserById = async (req, res) => {
  try {
    const user = await db.doc(req.params.id).get();
    if (!user.exists)
      return res.status(404).json({ message: "User Not Found" });
    return res.status(200).json(user.data());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getUsers = async (req, res) => {
  const { tenantId } = req.params;

  if (!tenantId) {
    return res.status(400).json({
      status: "error",
      message: "tenantId is required",
    });
  }
  try {
    const snapshot = await db.where("tenantId", "==", tenantId).get();
    const response = snapshot.docs.map((doc) => doc.data());
    res.status(200).send(response);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateUserDigitorySsoId = async (req, res) => {
  const { digitorySsoId } = req.body;
  if (!digitorySsoId) {
    return res.status(400).json({ message: "digitorySsoId is required" });
  }
  try {
    const userDoc = db.doc(req.params.id);
    await userDoc.update({ digitorySsoId, verified: true });
    res.status(200).json("Digitory SSO ID Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateUser = async (req, res) => {
  try {
    const validatedData = handleValidation({ ...req.body }, schema);
    if (!validatedData) return;

    const { tenantId, emailId, name } = validatedData;

    const duplicateEmail = await db
      .where("tenantId", "==", tenantId)
      .where("emailId", "==", emailId)
      .limit(1)
      .get();

    if (!duplicateEmail.empty && duplicateEmail.docs[0].id !== req.params.id) {
      return res.status(409).json({ message: "Email ID Already Exists" });
    }

    const cleanName = trimName(name);

    const userRef = db.doc(req.params.id);
    const updatedUser = {
      ...validatedData,
      name: cleanName,
    };

    await userRef.update(updatedUser);
    res
      .status(200)
      .json({ message: "Updated Successfully", user: updatedUser });
  } catch (error) {
    console.error("Update User Failed:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateUserActiveStatus = async (req, res) => {
  try {
    const userDoc = db.doc(req.params.id);
    const user = await userDoc.get();
    const activeStatus = !user.data().activeStatus;
    await userDoc.update({ activeStatus });
    res.status(200).json("Status Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
