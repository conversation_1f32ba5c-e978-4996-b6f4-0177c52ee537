const admin = require("firebase-admin");
const schema = require("@/models/modifierSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.MODIFIERS);
const { handleValidation } = require("@/utils/validation");
const axios = require("axios");

exports.getModifiers = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const data = await db.where("tenantId", "==", tenantId).get();
    const modifiers = data.docs.map((doc) => doc.data());
    return res.status(200).json(modifiers);
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.getModifierById = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .get();

    if (snapshot.empty) {
      return res.status(404).json({ message: "Modifier not found." });
    }
    const result = [];
    snapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() });
    });
    return res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error fetching modifier by ID:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.updateModifier = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.body?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();
  const updatedData = req.body;

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const validatedData = handleValidation(updatedData, schema);
    if (!validatedData) return;

    const querySnapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .limit(1)
      .get();

    if (querySnapshot.empty) {
      return res.status(404).json({ message: "Modifier not found." });
    }

    const doc = querySnapshot.docs[0];
    const tenant = doc.data();

    if (tenant?.tenantId !== tenantId) {
      return res
        .status(403)
        .json({ message: "Unauthorized access to update this modifier." });
    }

    await doc.ref.update({ ...validatedData, linkingStatus: true });
    return res.status(200).json({ message: "Modifier updated successfully." });
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.insertModifiers = async (req, res) => {
  try {
    const input = req.body;
    const modifiers = Array.isArray(input) ? input : [input];

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    const accSnapshot = await accountDb
      .where("posId", "==", modifiers[0].accountId)
      .get();
    const account = accSnapshot.docs[0].data();

    if (!account) {
      return res.status(404).json({ message: "Account not found." });
    }

    const preparedModifiers = modifiers.map((modifier) => ({
      posId: modifier.posId,
      itemName: modifier.itemName,
      servingLevels: modifier.servingLevels ?? [],
      account: {
        id: account.id,
        name: account.name,
        posId: account.posId,
      },
      tenantId: account.tenantId,
    }));

    const validModifiers = await this.insertModifierLogic(
      preparedModifiers,
      res,
    );

    res.status(201).json({
      message: "Modifiers inserted/updated successfully",
      count: validModifiers.length,
    });
  } catch (error) {
    console.error("Error inserting modifiers:", error);
    res.status(500).send(error.message || "Internal Server Error");
  }
};

exports.insertModifierLogic = async (modifiers, res) => {
  const batch = admin.firestore().batch();
  const validModifiers = [];

  for (const mod of modifiers) {
    const validatedData = handleValidation(mod, schema);
    if (!validatedData) {
      console.log("insertModifierLogic validation failed:", mod);
      return;
    }

    const modifierSnap = await db
      .where("posId", "==", validatedData.posId)
      .where("account.id", "==", validatedData.account.id)
      .limit(1)
      .get();

    if (!modifierSnap.empty) {
      const existingDoc = modifierSnap.docs[0];
      const existingData = existingDoc.data();

      const existingServingLevels = existingData.servingLevels || [];
      const incomingServingLevels = validatedData.servingLevels || [];

      // Map existing servingLevels by servingSizeId
      const existingMap = new Map(
        existingServingLevels.map((s) => [s.servingSizeId, s]),
      );

      // Build final list ONLY from incoming
      const finalServingLevels = incomingServingLevels.map((incoming) => {
        // If exists → keep existing values
        if (existingMap.has(incoming.servingSizeId)) {
          return existingMap.get(incoming.servingSizeId);
        }

        // If new → add incoming
        return incoming;
      });

      const docRef = db.doc(existingDoc.id);

      batch.update(docRef, {
        servingLevels: finalServingLevels,
      });

      validModifiers.push({
        ...existingData,
        id: existingDoc.id,
        name: validatedData.name,
        servingLevels: finalServingLevels,
      });
    } else {
      const docRef = db.doc();
      const newItem = { ...validatedData, id: docRef.id };
      batch.set(docRef, newItem);
      validModifiers.push(newItem);
      console.log("New menuItem created:", validatedData.name);
    }
  }

  await batch.commit();
  return validModifiers;
};

exports.syncModifierFromBO = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  const url = `${process.env.BO_URI}/tenants/${tenantId}/modifiers`;

  try {
    // 1️⃣ Fetch from BO
    const result = await axios.get(url, {
      headers: {
        "App-Id": process.env.BO_APP_ID,
        "App-Code": process.env.BO_APP_CODE,
        "Content-Type": "application/json",
      },
    });

    const modifiers = Array.isArray(result.data) ? result.data : [result.data];
    if (!modifiers.length) {
      return res.status(200).json({ message: "No modifiers found" });
    }

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    // 2️⃣ Collect unique accountIds from modifiers
    const accountPosIds = [
      ...new Set(modifiers.map((m) => m.accountId).filter(Boolean)),
    ];

    // 3️⃣ Fetch matching accounts
    const accountSnapshots = await Promise.all(
      accountPosIds.map((posId) =>
        accountDb.where("posId", "==", posId).limit(1).get(),
      ),
    );

    // 4️⃣ Build account lookup map
    const accountMap = new Map();
    accountSnapshots.forEach((snapshot) => {
      if (!snapshot.empty) {
        const acc = snapshot.docs[0].data();
        accountMap.set(acc.posId, acc);
      }
    });

    // 5️⃣ Prepare modifiers with correct account
    const preparedModifiers = modifiers
      .map((modifier) => {
        const account = accountMap.get(modifier.accountId);
        if (!account) return null; // ❌ skip if account not found

        return {
          posId: modifier.posId,
          itemName: modifier.itemName,
          servingLevels: modifier.servingLevels ?? [],
          account: {
            id: account.id,
            name: account.name,
            posId: account.posId,
          },
          tenantId: account.tenantId,
        };
      })
      .filter(Boolean);

    if (!preparedModifiers.length) {
      return res
        .status(404)
        .json({ message: "No valid accounts found for modifiers" });
    }

    // 6️⃣ Insert / update logic
    const validModifiers = await this.insertModifierLogic(
      preparedModifiers,
      res,
    );

    res.status(201).json({
      message: "Modifiers synced successfully",
      data: validModifiers,
    });
  } catch (error) {
    console.error(error);
    res.status(500).send(error.message || "Internal Server Error");
  }
};
