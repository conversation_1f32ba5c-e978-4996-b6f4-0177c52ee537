const admin = require("firebase-admin");
const schema = require("@/models/categorySchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.CATEGORIES);
const {
  handleValidation,
  trimName,
  formatName,
  nameValidation,
} = require("@/utils/validation");
const categoryService = require("@/services/categoryService");
const { propagateCategoryName } = require("@/utils/updateLinkedData");

exports.insertCategory = async (req, res) => {
  try {
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;
    // Check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    // Check for duplicate subCategory names
    const names = validatedData.subCategories.map((s) => formatName(s.name));

    const hasDuplicates = new Set(names).size !== names.length;
    if (hasDuplicates) {
      return res
        .status(400)
        .json({ message: "Duplicate subCategory names are not allowed." });
    }

    const cleanName = trimName(validatedData.name);

    // Generate IDs
    const newDocRef = db.doc();
    const subCategoriesWithIds = validatedData.subCategories.map((sub) => ({
      id: admin.firestore().collection("dummy").doc().id,
      name: sub.name,
    }));

    const category = {
      id: newDocRef.id,
      tenantId: validatedData.tenantId,
      name: cleanName,
      nameNormalized: normalizedName,
      subCategories: subCategoriesWithIds,
      activeStatus: validatedData.activeStatus,
    };

    await newDocRef.set(category);
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.createSubCategory = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.body?.tenantId?.trim();
  const newSubCategories = req.body?.subCategories;

  if (!id || !tenantId) {
    return res
      .status(400)
      .json({ message: "Both id and tenantId are required." });
  }

  if (!Array.isArray(newSubCategories) || newSubCategories.length === 0) {
    return res
      .status(400)
      .json({ message: "subCategories must be a non-empty array." });
  }

  try {
    const data = await db
      .where("tenantId", "==", tenantId)
      .where("__name__", "==", id)
      .get();

    if (data.empty) {
      return res.status(404).json({ message: "Category not found." });
    }

    const docRef = data.docs[0].ref;
    const categoryData = data.docs[0].data();

    const existingNames =
      categoryData.subCategories?.map((s) => s.name.trim().toLowerCase()) || [];
    const incomingNames = newSubCategories.map((s) =>
      s.name.trim().toLowerCase()
    );

    // Check for duplicates in request
    const hasDuplicates = new Set(incomingNames).size !== incomingNames.length;
    if (hasDuplicates) {
      return res
        .status(400)
        .json({ message: "Duplicate subCategory names are not allowed." });
    }

    // Check for existing sub-category names
    const existingSubCategory = incomingNames.filter((name) =>
      existingNames.includes(name)
    );
    if (existingSubCategory.length > 0) {
      return res
        .status(409)
        .json({ message: "subCategory name already exists" });
    }

    // Add new IDs and append to existing subcategories
    const newSubCatsWithIds = newSubCategories.map((sub) => ({
      id: admin.firestore().collection("dummy").doc().id,
      name: sub.name.trim(),
    }));

    const updatedSubCategories = [
      ...(categoryData.subCategories || []),
      ...newSubCatsWithIds,
    ];
    await docRef.update({ subCategories: updatedSubCategories });
    return res
      .status(200)
      .json({ message: "subCategories added successfully." });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

exports.getCategories = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const data = await db
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();
    const categories = data.docs.map((doc) => doc.data());
    return res.status(200).json(categories);
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

exports.getCategory = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  if (!id || !tenantId) {
    return res
      .status(400)
      .json({ message: "Both id and tenantId are required." });
  }

  try {
    const data = await db
      .where("tenantId", "==", tenantId)
      .where("__name__", "==", id)
      .get();

    if (data.empty) {
      return res.status(404).json({ message: "Category not found." });
    }

    const category = data.docs[0].data();
    return res.status(200).json(category);
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

exports.updateCategory = async (req, res) => {
  try {
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;
    // Check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      req.params.id,
      "tenantId",
      validatedData.tenantId
    );

    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    // Check for duplicate subCategory names
    if (Array.isArray(validatedData.subCategories)) {
      const names = validatedData.subCategories.map((s) => formatName(s.name));
      const hasDuplicates = new Set(names).size !== names.length;
      if (hasDuplicates) {
        return res
          .status(400)
          .json({ message: "Duplicate subCategory names are not allowed." });
      }
      // Assign Firestore IDs where missing
      validatedData.subCategories = validatedData.subCategories.map((sub) => ({
        id: sub.id || admin.firestore().collection("dummy").doc().id,
        name: sub.name.trim(),
      }));
    }

    const categoryRef = db.doc(req.params.id);
    await categoryRef.update({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });

    await propagateCategoryName(req.params.id, cleanName);

    return res.status(200).json({ message: "Updated successfully." });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

exports.deleteCategory = async (req, res) => {
  try {
    const category = db.doc(req.params.id);
    await category.delete();
    res.status(200).json("deleted successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Controller for activating a category
exports.activateCategory = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get category ID from URL
    const result = await categoryService.activateCategory(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Category activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., category not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a category
exports.deactivateCategory = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get category ID from URL
    const result = await categoryService.deactivateCategory(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Category deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};
