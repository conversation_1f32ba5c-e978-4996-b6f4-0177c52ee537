const {
  createContract,
  getContracts,
  getContractById,
  updateContract,
  closeContractById,
  updateContractAttachments,
  deleteContractAttachment
} = require("@/services/contractService");

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

exports.insertContract = async (req, res) => {
  const payload = {
    ...req.body,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
    },
  };
  try {
    const contract = await createContract(payload);    
    res.status(201).json(contract); 
  } catch (error) {
    console.error("Error creating contract:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getContracts = async (req, res) => {
  try {
    const contracts = await getContracts(req.params.tenantId);
    res.status(200).json(contracts);
  } catch (error) {
    console.error("Error fetching contracts:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getContractById = async (req, res) => {
  try {
    const contract = await getContractById(req.params.id);
    res.status(200).json(contract);
  } catch (error) {
    console.error("Error fetching contract:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateContract = async (req, res) => {
  const payload = {
    ...req.body,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
    },
  };
  try {
    await updateContract(req.params.id, payload);
    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating contract:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.closeContract = async (req, res) => {  
  try {
    const { userName, userId } = req.identity;
    const data = {
      closingDetails: {
        id: userId,
        name: userName,
        time: FD.now(),
      },
    };
    await closeContractById(data, req.params.id);
    res.status(200).json("updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateContractAttachments = async (req, res) => {
  try {
    const { id } = req.params;
    const { attachments } = req.body;

    const result = await updateContractAttachments(id, attachments);
    res.status(200).json(result);
  } catch (err) {
    console.error("Error updating attachments:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.deleteContractAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const { filePath } = req.body;    

    const result = await deleteContractAttachment(id, filePath);
    res.status(200).json(result);
  } catch (err) {
    console.error("Error deleting attachment:", err);
    res.status(500).json({ message: err.message });
  }
};

