const axios = require("axios");
const admin = require("firebase-admin");
const jwt = require("jsonwebtoken");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { COLLECTIONS } = require("@/defs/collectionDefs");
/**
 * @description
 * Silent login API. This API is used to
 * - Validate the token
 * - Fetch the user
 * - Fetch the tenants associated with the user
 * - Return the user details and a JWT token
 * @param {Object} req - Request object
 * @param {string} req.body.token - The token to be validated
 * @returns {Object} - An object with the user details and a JWT token
 * @throws {Error} - If the token is invalid
 */
exports.silentLogin = async (req, res) => {
  const { oAuthCode } = req.body;

  try {
    const { data } = await axios.post(
      `${process.env.AUTH_SERVER_URL}/sso/token-exchange`,
      { oAuthToken: oAuthCode }
    );

    const { userEmail, userId } = data;

    const usersSnapshot = await admin
      .firestore()
      .collection(COLLECTIONS.USERS)
      .where("digitorySsoId", "==", userId)
      .get();

    if (usersSnapshot.empty) {
      return res
        .status(403)
        .json({ message: "User does not have access to the app" });
    }

    const tenantPromises = usersSnapshot.docs.map(async (doc) => {
      const user = doc.data();
      if (user.emailId !== userEmail) return null;

      const isAdmin = user.isAdmin;

      let privileges = [];

      if (isAdmin) {
        const rolesSnap = await admin
          .firestore()
          .collection(COLLECTIONS.ROLE)
          .get();

        privileges = rolesSnap.docs.flatMap((d) => d.data().privileges || []);
        privileges = [...new Set(privileges)];
      } else {
        const roleSnap = await admin
          .firestore()
          .collection(COLLECTIONS.ROLE)
          .where("name", "==", user.roleName)
          .where("tenantId", "==", user.tenantId)
          .limit(1)
          .get();

        if (roleSnap.empty) return null;
        privileges = roleSnap.docs[0].data().privileges || [];
      }

      let workAreaMap = {};

      if (!isAdmin && !user.allLocations && (user.locationIds || []).length) {
        const workAreaRef = admin
          .firestore()
          .collection(COLLECTIONS.WORK_AREAS);

        const chunks = [];
        for (let i = 0; i < user.locationIds.length; i += 10) {
          chunks.push(user.locationIds.slice(i, i + 10));
        }

        const snaps = await Promise.all(
          chunks.map((chunk) =>
            workAreaRef
              .where("tenantId", "==", user.tenantId)
              .where("locationId", "in", chunk)
              .get()
          )
        );

        const docs = snaps.flatMap((s) => s.docs);

        for (const waDoc of docs) {
          const wa = waDoc.data();
          const waId = waDoc.id;
          const locId = wa.locationId;

          if (!workAreaMap[locId]) workAreaMap[locId] = [];

          if (
            user.allInventoryLocations ||
            (user.inventoryLocationIds || []).includes(waId)
          ) {
            workAreaMap[locId].push(waId);
          }
        }
      }

      return {
        id: user.tenantId,
        name: user.tenantName,
        roleName: user.roleName,
        roleId: user.roleId,
        userName: user.name || userEmail.split("@")[0],
        allLocations: user.allLocations ?? true,
        allInventoryLocations: user.allInventoryLocations ?? true,
        locationIds: user.locationIds || [],
        inventoryLocationIds: user.inventoryLocationIds || [],
        workAreaMap,
        isAdmin,
        userId: user.id,
        userDetails: {
          isAdmin,
          allLocations: user.allLocations ?? true,
          locationIds: user.locationIds || [],
          allInventoryLocations: user.allInventoryLocations ?? true,
          inventoryLocationIds: user.inventoryLocationIds || [],
          tenantId: user.tenantId,
        },
        privileges,
      };
    });

    const tenants = (await Promise.all(tenantPromises)).filter(Boolean);

    if (!tenants.length) {
      return res
        .status(403)
        .json({ message: "No valid tenants found for this user" });
    }

    const claims = {
      userId,
      email: userEmail,
      tenants: tenants.map(({ privileges, ...rest }) => ({ ...rest })),
      claimsIssuedAt: FD.now(),
    };

    const jwtToken = jwt.sign(claims, process.env.JWT_SECRET, {
      expiresIn: "6h",
    });

    res.status(200).json({
      userId,
      tenants,
      email: userEmail,
      token: jwtToken,
      expiresIn: 3600 * 6,
    });
  } catch (error) {
    console.error(error);
    res.status(401).json({ message: "Invalid token" });
  }
};
