// controllers/grnController.js
const { ResultTypes } = require("@/defs/resultTypeDefs");
const { LedgerTypes, StockLedgerReasonCode } = require("@/defs/ledgerDefs");

const {
  listGrnSummaries,
  getGrnByID,
  fetchGrnItemPricesByPeriod,
  updateGrnAttachments,
  deleteGrnAttachment,
  fetchLastGrnItemPrice,
  updateGRN,
} = require("@/services/grnService");
const {
  deleteGRN,
  returnVendor,
} = require("@/services/stockTransactionService");

const { renderPDF } = require("@/helpers/render");

const {
  generatePrint,
  generateHeader,
  generateDetails,
  generateItemsTable,
  generateDynamicTable,
  generateApprovalSignatures,
} = require("@/helpers/printHelper");

const { prepareGRNData, getLocationById } = require("@/services/printService");

const { validateIdentityFilters } = require("@/helpers/reportHelper");

/**
 * Retrieves a GRN document by its ID.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} id - Unique identifier of the GRN document to retrieve.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
exports.getGRNByID = async function (req, res) {
  try {
    const { tenantId, id } = req.params;
    const grn = await getGrnByID(id, tenantId);
    if (!grn) return res.status(404).json({ message: "GRN not found" });
    return res.status(200).json(grn);
  } catch (err) {
    console.error("Error fetching GRN by ID:", err);
    return res.status(500).json({ message: err.message });
  }
};

/**
 * Retrieves a GRN document by its number.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} grnNumber - Unique GRN number of the GRN document to retrieve.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
exports.getGRNByNumber = async function (req, res) {
  try {
    const { tenantId, grnNumber } = req.params;
    const grn = await grnService.getGrnByNumber(grnNumber, tenantId);
    if (!grn) return res.status(404).json({ message: "GRN not found" });
    return res.status(200).json(grn);
  } catch (err) {
    console.error("Error fetching GRN by Number:", err);
    return res.status(500).json({ message: err.message });
  }
};

/**
 * Lists GRN summaries for the given tenantId, with optional filters for locationId, startDate, and endDate.
 * - Returns a list of GRN summaries in the given resultType (either "json" or "excel").
 * - If resultType is "excel", sets the Content-Disposition and Content-Type headers to return the GRN summaries as an Excel file.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {string} [locationId] - Location ID to filter GRNs by.
 * @param {string} [startDate] - Start date to filter GRNs by.
 * @param {string} [endDate] - End date to filter GRNs by.
 * @param {string} [ResultType="json"] - Type of result to return. Either "json" or "excel".
 * @returns {Promise<object>} - List of GRN summaries or Excel file containing the GRN summaries.
 */
exports.getGRNs = async function (req, res) {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        message: "tenantId is required",
      });
    }
    const { ResultType: resultTypeParam = "json" } = req.query;

    const filters = {
      tenantId: tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
      attachments: req.body.attachments || null,
      grnStatus: req.body.grnStatus || null,
      vendors: req.body.vendors || null,
    };

    const _filters = validateIdentityFilters(req.identity, filters);

    const resultType = resultTypeParam.toLowerCase();
    const data = await listGrnSummaries(_filters, resultType);

    switch (resultType) {
      case ResultTypes.EXCEL.toLowerCase():
        res.setHeader("Content-Disposition", "attachment; filename=grns.xlsx");
        res.setHeader(
          "Content-Type",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        );
        return res.send(data);
      default:
        return res.status(200).json(data);
    }
  } catch (err) {
    console.error("Error listing GRN summaries:", err);
    return res.status(500).json({ message: err.message });
  }
};

exports.getGrnItemPrices = async function (req, res) {
  try {
    const { tenantId } = req.params;
    const { itemId, inventoryLocationId, pkgId = null } = req.body;

    if (!itemId || !inventoryLocationId) {
      return res.status(400).json({
        message: "Missing required fields: itemId and inventoryLocationId",
      });
    }

    const prices = await fetchGrnItemPricesByPeriod({
      tenantId,
      itemId,
      inventoryLocationId,
      pkgId,
    });

    res.status(200).json(prices);
  } catch (err) {
    console.error("Error fetching last GRN item price:", err);
    return res.status(500).json({ message: err.message });
  }
};

exports.getLastGrnItemPrice = async function (req, res) {
  try {
    const { tenantId } = req.params;
    const { itemId, inventoryLocationId, pkgId = null } = req.body;

    if (!itemId || !inventoryLocationId) {
      return res.status(400).json({
        message: "Missing required fields: itemId and inventoryLocationId",
      });
    }

    const price = await fetchLastGrnItemPrice({
      tenantId,
      itemId,
      inventoryLocationId,
      pkgId,
    });

    res.status(200).json(price);
  } catch (err) {
    console.error("Error fetching last GRN item price:", err);
    return res.status(500).json({ message: err.message });
  }
};

exports.updateGrnAttachments = async (req, res) => {
  try {
    const { id } = req.params;
    const { attachments } = req.body;

    const result = await updateGrnAttachments(id, attachments);
    res.status(200).json(result);
  } catch (err) {
    console.error("Error updating attachments:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.deleteGrnAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const { filePath } = req.body;

    const result = await deleteGrnAttachment(id, filePath);
    res.status(200).json(result);
  } catch (err) {
    console.error("Error deleting attachment:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.deleteGRNByID = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    const { reason } = req.body;
    const { userName, userId } = req.identity;
    const result = await deleteGRN({
      grnId: id,
      tenantId,
      reason,
      removedById: userId,
      removedByName: userName,
      ledgerType: LedgerTypes.ADJUSTMENT,
      ledgerReasonCode: StockLedgerReasonCode.GRN_DELETE,
    });
    res.status(200).json(result);
  } catch (err) {
    console.error("Error deleting GRN:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.returnToVendor = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    const payload = req.body;
    const { userName, userId } = req.identity;
    const result = await returnVendor({
      grnId: id,
      tenantId,
      payload,
      removedById: userId,
      removedByName: userName,
      ledgerType: LedgerTypes.RETURN_VENDOR,
    });
    res.status(200).json(result);
  } catch (err) {
    console.error("Error returning GRN:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.updateGRN = async (req, res) => {
  try {
    const { id } = req.params;
    const grn = req.body;
    const { userName, userId } = req.identity;
    const result = await updateGRN({
      id,
      grn,
      editedById: userId,
      editedByName: userName,
    });
    res.status(200).json(result);
  } catch (err) {
    console.error("Error updating GRN:", err);
    res.status(500).json({ message: err.message });
  }
};

/**
 * Exports a grn to a PDF file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - GRN ID
 * @returns {Promise<void>}
 */
exports.exportToPDF = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const prepared = await prepareGRNData(id, tenantId);
    if (!prepared) throw new Error("No GRN found");

    const { grn, vendorObj, grnObj, itemsTable } = prepared;

    const locationId = grn?.location ? grn.location.id : grn?.locationId;
    const locationData = await getLocationById(tenantId, locationId);

    const content = [
      generateHeader("Goods Received Note", grn.tenantName, locationData?.name),
      generateDetails(vendorObj, grnObj),
      generateDynamicTable(itemsTable),
      generateApprovalSignatures({ name: grn.createdBy?.name || "Unknown", time: grn.createdAt }),
    ];

    const pdf = await generatePrint(content);
    renderPDF(res, pdf, `${grn.grnNumber}.pdf`);
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Exports a grn to a XLSX file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {String} req.params.tenantId - Tenant ID
 * @param {String} req.params.id - GRN ID
 * @returns {Promise<void>}
 */
exports.exportToXLSX = async (req, res) => {
  try {
    // @todo implement
    throw new Error("Not implemented");
  } catch (error) {
    console.error("Error in exportPdf:", error);
    res.status(500).json({ message: error.message });
  }
};
