const {
  createTenant,
  updateTenant,
  updateTenantSettings,
  updateTenantStatus,
  getTenant,
  getTenants,
  freezeMonth,
} = require("@/services/tenantService");

// ================== Create ==================
exports.insertTenants = async (req, res) => {
  try {
    const result = await createTenant(req.body);
    res.status(201).json(result);
  } catch (error) {
    console.error("Error creating tenant:", error);
    res.status(500).json({ message: error.message });
  }
};

// ================== Update ==================
exports.updateTenant = async (req, res) => {
  try {
    await updateTenant(req.params.id, req.body);
    res.status(200).json("updated successfully");
  } catch (error) {
    console.error("Error updating tenant:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.freezeMonth = async (req, res) => {
  try {
    await freezeMonth(req.params.tenantId);
    res.status(200).json({ message: "Month updated successfully" });
  } catch (error) {
    console.error("Error freezing month:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateTenantSettings = async (req, res) => {
  try {
    await updateTenantSettings(req.params.tenantId, req.body.settings);
    res.status(200).json({ message: "Settings updated successfully" });
  } catch (error) {
    console.error("Error updating tenant settings:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateStatusActive = async (req, res) => {
  try {
    await updateTenantStatus(req.params.id, true);
    res.status(200).json("status updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateStatusInactive = async (req, res) => {
  try {
    await updateTenantStatus(req.params.id, false);
    res.status(200).json("status updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// ================== Get ==================
exports.getTenant = async (req, res) => {
  try {
    const tenant = await getTenant(req.params.tenantId);
    if (!tenant) {
      return res.status(400).json({ message: "Tenant not found" });
    }
    res.status(200).send(tenant);
  } catch (error) {
    console.error("Error fetching tenant:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getTenants = async (req, res) => {
  try {
    const tenants = await getTenants();
    res.status(200).send(tenants);
  } catch (error) {
    console.error("Error fetching tenants:", error);
    res.status(500).json({ message: error.message });
  }
};

// ================== Internal Utility ==================
// exports.isApprovalNeeded = async (req, res) => {
//   try {
//     const result = await isApprovalNeeded(req.params.id);
//     res.status(200).json({ approvalNeeded: result });
//   } catch (error) {
//     console.error("Error checking approval status:", error);
//     res.status(500).json({ message: error.message });
//   }
// };
