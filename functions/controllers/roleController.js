const admin = require("firebase-admin");
const { groupedPrivileges, getPrivilegeDetails } = require("@/utils/privileges"); // adjust path as needed
const roleSchema = require("@/models/roleSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");

const db = admin.firestore().collection(COLLECTIONS.ROLE);

const roleService = require("@/services/roleService");

exports.getPrivileges = (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Privileges fetched successfully",
    payload: groupedPrivileges,
  });
};

exports.createRole = async (req, res) => {
  const { error, value } = roleSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ status: "error", message: error.message });
  }

  const { tenantId, name, privileges } = req.body;

  if (!tenantId) {
    return res.status(400).json({
      status: "error",
      message: "tenantId is required",
    });
  }

  try {
    const existingRole = await db
      .where("tenantId", "==", tenantId)
      .where("name", "==", name)
      .limit(1)
      .get();

    if (!existingRole.empty) {
      return res.status(400).json({
        status: "error",
        message: `Role '${name}' already exists`,
      });
    }

    const newRoleRef = db.doc();
    const newRole = {
      id: newRoleRef.id,
      ...value,
    };

    await newRoleRef.set(newRole);

    res.status(201).json({
      status: "success",
      message: "Role created successfully",
      payload: newRole,
    });
  } catch (err) {
    res.status(500).json({ status: "error", message: err.message });
  }
};

exports.getRoles = async (req, res) => {
  const { tenantId } = req.params;

  if (!tenantId) {
    return res.status(400).json({
      status: "error",
      message: "tenantId is required",
    });
  }

  try {
    const snapshot = await db.where("tenantId", "==", tenantId).get();
    const roles = snapshot.docs.map((doc) => {
      const data = doc.data();
      
      // If role has privileges as an array of codes, map them to details
      if (Array.isArray(data.privileges)) {
        data.privileges = getPrivilegeDetails(data.privileges);
      }

      return data;
    });

    res.json({
      status: "success",
      message: "Roles fetched successfully",
      payload: roles,
    });
  } catch (err) {
    res.status(500).json({ status: "error", message: err.message });
  }
};

exports.getRoleById = async (req, res) => {
  try {
    const roleRef = db.doc(req.params.id);
    const doc = await roleRef.get();

    if (!doc.exists) {
      return res.status(404).json({
        status: "error",
        message: "Role not found",
      });
    }

    res.json({
      status: "success",
      message: "Role fetched successfully",
      payload: doc.data(),
    });
  } catch (err) {
    res.status(500).json({
      status: "error",
      message: err.message,
    });
  }
};

exports.updateRole = async (req, res) => {
  const { error, value } = roleSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ status: "error", message: error.message });
  }

  try {
    const roleRef = db.doc(req.params.id);
    const doc = await roleRef.get();
    if (!doc.exists) {
      return res
        .status(404)
        .json({ status: "error", message: "Role not found" });
    }

    const { tenantId, name } = value;

    const existingRole = await db
      .where("tenantId", "==", tenantId)
      .where("name", "==", name)
      .limit(1)
      .get();

    if (!existingRole.empty && existingRole.docs[0].id !== req.params.id) {
      return res.status(400).json({
        status: "error",
        message: `Role '${name}' already exists`,
      });
    }

    await roleRef.update(value);

    res.json({
      status: "success",
      message: "Role updated successfully",
      payload: { id: req.params.id, ...value },
    });
  } catch (err) {
    res.status(500).json({ status: "error", message: err.message });
  }
};

exports.deleteRole = async (req, res) => {
  try {
    const roleRef = db.doc(req.params.id);
    const doc = await roleRef.get();
    if (!doc.exists) {
      return res
        .status(404)
        .json({ status: "error", message: "Role not found" });
    }

    await roleRef.delete();
    res.json({
      status: "success",
      message: "Role deleted successfully",
      payload: doc.data(),
    });
  } catch (err) {
    res.status(500).json({ status: "error", message: err.message });
  }
};

exports.updateRoleActiveStatus = async (req, res) => {
  try {
    const roleDoc = db.doc(req.params.id);
    const role = await roleDoc.get();
    const activeStatus = !role.data().activeStatus;
    await roleDoc.update({ activeStatus });
    res.status(200).json({status:"success", message:"Status Updated Successfully"});
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Controller for activating a role
exports.activateRole = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get role ID from URL
    const result = await roleService.activateRole(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Role activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., role not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a role
exports.deactivateRole = async (req, res) => {
  try {
      const { tenantId, id } = req.params;
  
      const unitSnapshot = await db
        .where("tenantId", "==", tenantId)
        .where("id", "==", id)
        .limit(1)
        .get();
  
      if (unitSnapshot.empty) {
        return res.status(404).json({ message: "Role not found" });
      }
  
      const unitData = unitSnapshot.docs[0].data();
      const name = unitData.name;
  
      if (!name) {
        return res.status(400).json({ message: "Role name missing" });
      }
      await roleService.deactivateRole(tenantId, name);
  
      res.status(200).json({
        message: `Role deactivated successfully`,
      });
    } catch (err) {
      res.status(400).json({ message: err.message });
    }
};


