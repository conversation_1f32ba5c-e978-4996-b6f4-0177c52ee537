const {
  getSummary,
  searchByNumber,
} = require("@/services/actionCenterService.js");

/**
 * Controller: Fetches dashboard summary for a tenant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 */
exports.getHome = async (req, res) => {
  try {
    // Extract tenantId from URL parameters
    const tenantId = req.params.tenantId;

    if (!tenantId) {
      return res.status(400).json({ message: "tenantId is required" });
    }

    // Fetch summary from service
    const result = await getSummary(tenantId);

    // TODO: Fetch par level from inventory repository if needed
    result.par_level = 0;

    return res.status(200).json(result);
  } catch (err) {
    console.error("Error fetching summary:", err);
    return res.status(500).json({ message: err.message || "Server error" });
  }
};

/**
 * Controller: Search for a document by its number for a tenant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 */
exports.searchByNumber = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { number } = req.query;

    // Validate input
    if (!tenantId) {
      return res.status(400).json({ message: "tenantId is required" });
    }
    if (!number) {
      return res.status(400).json({ message: "number is required" });
    }

    // Call service to search by number
    const result = await searchByNumber(tenantId, number);

    return res.status(200).json(result);
  } catch (err) {
    console.error("Error in searchByNumber:", err);
    return res.status(500).json({ message: err.message || "Server error" });
  }
};
