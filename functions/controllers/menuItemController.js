const admin = require("firebase-admin");
const schema = require("@/models/menuItemSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.MENU_ITEMS);
const { handleValidation } = require("@/utils/validation");
const axios = require("axios");

exports.getMenuItems = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const data = await db.where("tenantId", "==", tenantId).get();
    const menuItems = data.docs.map((doc) => doc.data());
    return res.status(200).json(menuItems);
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.getMenuItemById = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .get();

    if (snapshot.empty) {
      return res.status(404).json({ message: "Menu Item not found." });
    }
    const result = [];
    snapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() });
    });
    return res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error fetching menu item by ID:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.updateMenuItem = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.body?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();
  const updatedData = req.body;

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const validatedData = handleValidation(updatedData, schema);
    if (!validatedData) return;
    const querySnapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .limit(1)
      .get();

    if (querySnapshot.empty) {
      return res.status(404).json({ message: "Menu Item not found." });
    }

    const doc = querySnapshot.docs[0];
    const tenant = doc.data();

    if (tenant?.tenantId !== tenantId) {
      return res
        .status(403)
        .json({ message: "Unauthorized access to update this menu item." });
    }

    await doc.ref.update({ ...validatedData, linkingStatus: true });
    return res.status(200).json({ message: "Menu Item updated successfully." });
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.insertMenuItems = async (req, res) => {
  try {
    const input = req.body;
    const menuItems = Array.isArray(input) ? input : [input];

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    const accSnapshot = await accountDb
      .where("posId", "==", menuItems[0].accountId)
      .get();
    const account = accSnapshot.docs[0].data();

    if (!account) {
      return res.status(404).json({ message: "Account not found." });
    }

    const preparedMenuItems = menuItems.map((menuItem) => ({
      posId: menuItem.posId,
      itemName: menuItem.itemName,
      itemCode: menuItem.itemCode,
      servingLevels: menuItem.servingLevels ?? [],
      account: {
        id: account.id,
        name: account.name,
        posId: account.posId,
      },
      tenantId: account.tenantId,
    }));

    const validMenuItems = await this.insertMenuItemLogic(
      preparedMenuItems,
      res,
    );

    res.status(201).json({
      message: "MenuItems created/updated successfully",
      count: validMenuItems.length,
    });
  } catch (error) {
    res.status(500).send(error.message || "Internal Server Error");
  }
};

exports.insertMenuItemLogic = async (menuItems, res) => {
  const batch = admin.firestore().batch();
  const validMenuItems = [];

  for (const item of menuItems) {
    const validatedData = handleValidation(item, schema);
    if (!validatedData) {
      console.log("Invalid data, skipping.", item);
      continue;
    }

    const snapshot = await db
      .where("posId", "==", validatedData.posId)
      .where("account.id", "==", validatedData.account.id)
      .limit(1)
      .get();

    if (!snapshot.empty) {
      const existingDoc = snapshot.docs[0];
      const existingData = existingDoc.data();

      const existingServingLevels = existingData.servingLevels || [];
      const incomingServingLevels = validatedData.servingLevels || [];

      // Map existing by servingSizeId
      const existingMap = new Map(
        existingServingLevels.map((s) => [s.servingSizeId, s]),
      );

      // Build final list strictly from incoming
      const finalServingLevels = incomingServingLevels.map((incoming) => {
        // If exists → keep existing
        if (existingMap.has(incoming.servingSizeId)) {
          return existingMap.get(incoming.servingSizeId);
        }

        // If new → add incoming
        return incoming;
      });

      const docRef = db.doc(existingDoc.id);

      batch.update(docRef, {
        servingLevels: finalServingLevels,
      });
      //put exisitig data  // overide name, map new serving level if changes
      validMenuItems.push({
        ...existingData,
        id: existingDoc.id,
        name: validatedData.name,
        servingLevels: finalServingLevels,
      });
    } else {
      const docRef = db.doc();
      const newItem = { ...validatedData, id: docRef.id };
      batch.set(docRef, newItem);
      validMenuItems.push(newItem);
      console.log("New menuItem created:", validatedData.name);
    }
  }

  await batch.commit();
  return validMenuItems;
};

exports.getMenuItemsByModifiers = async (req, res) => {
  try {
    const { accountId, modifiers } = req.body;

    if (!accountId || !modifiers) {
      return res
        .status(400)
        .json({ message: "accountId and modifiers are required" });
    }

    const result = await axios.put(
      `${process.env.BO_URI}/accounts/${accountId}/menu-items/by-modifiers`,
      { modifiers },
      {
        headers: {
          "App-Id": process.env.BO_APP_ID,
          "App-Code": process.env.BO_APP_CODE,
          "Content-Type": "application/json",
        },
      },
    );

    // ✅ Send only serializable parts
    return res.status(200).json({
      success: true,
      status: result.status,
      data: result.data,
    });
  } catch (error) {
    console.error(
      "Error fetching menu items by modifiers:",
      error.response?.data || error.message,
    );

    return res.status(500).json({
      success: false,
      message: "Failed to fetch menu items",
      error: error.response?.data || error.message,
    });
  }
};

exports.syncMenuItemFromBO = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  const url = `${process.env.BO_URI}/tenants/${tenantId}/menu-items`;

  try {
    // 1️⃣ Fetch from BO
    const result = await axios.get(url, {
      headers: {
        "App-Id": process.env.BO_APP_ID,
        "App-Code": process.env.BO_APP_CODE,
        "Content-Type": "application/json",
      },
    });

    const menuItems = Array.isArray(result.data) ? result.data : [result.data];
    if (!menuItems.length) {
      return res.status(200).json({ message: "No menu items found" });
    }

    // 2️⃣ Collect unique accountIds from menuItems
    const accountPosIds = [
      ...new Set(menuItems.map((i) => i.accountId).filter(Boolean)),
    ];

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    // 3️⃣ Fetch matching accounts
    const accountSnapshots = await Promise.all(
      accountPosIds.map((posId) =>
        accountDb.where("posId", "==", posId).limit(1).get(),
      ),
    );

    // 4️⃣ Build account lookup map
    const accountMap = new Map();
    accountSnapshots.forEach((snapshot) => {
      if (!snapshot.empty) {
        const acc = snapshot.docs[0].data();
        accountMap.set(acc.posId, acc);
      }
    });

    // 5️⃣ Prepare menu items with correct account
    const preparedMenuItems = menuItems
      .map((menuItem) => {
        const account = accountMap.get(menuItem.accountId);
        if (!account) return null; // ❌ skip if account not found

        return {
          posId: menuItem.posId,
          itemName: menuItem.itemName,
          itemCode: menuItem.itemCode,
          servingLevels: menuItem.servingLevels ?? [],
          account: {
            id: account.id,
            name: account.name,
            posId: account.posId,
          },
          tenantId: account.tenantId,
        };
      })
      .filter(Boolean);

    if (!preparedMenuItems.length) {
      return res
        .status(404)
        .json({ message: "No valid accounts found for menu items" });
    }

    // 6️⃣ Insert / update logic
    const validMenuItems = await this.insertMenuItemLogic(
      preparedMenuItems,
      res,
    );

    res.status(201).json({
      message: "MenuItems synced successfully",
      data: validMenuItems,
    });
  } catch (error) {
    console.error(error);
    res.status(500).send(error.message || "Internal Server Error");
  }
};
