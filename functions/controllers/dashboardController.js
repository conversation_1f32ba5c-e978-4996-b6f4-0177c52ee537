// controllers/dashboardController.js

/**
 * Dashboard Controller
 * -------------------
 * Handles all tenant-specific dashboard requests:
 * - Inventory Dashboard
 * - Purchase Dashboard
 * - COGS Dashboard
 *
 * Responsibilities:
 * 1. Extract tenantId from route params (middleware ensures authentication).
 * 2. Extract filters from request body (POST requests support complex filters).
 * 3. Delegate to the appropriate service for data retrieval.
 * 4. Return JSON response with dashboard data or error.
 */

const inventoryService = require("@/services/dashboards/inventoryService");
const purchaseService = require("@/services/dashboards/purchaseService");
const cogsService = require("@/services/dashboards/cogsService");

/**
 * GET Inventory Dashboard Summary
 * POST /:tenantId/dashboards/inventory
 */
exports.getInventory = async (req, res) => {
  try {
    // Extract tenantId from the URL params
    const { tenantId } = req.params;

    // Filters are sent in the request body (e.g., date range, category filters)
    const payload = req.body;

    // Call the service layer to get tenant-specific inventory summary
    const data = await inventoryService.getSummary(tenantId, payload);

    // Return result as JSON
    res.json(data);
  } catch (err) {
    // Catch and respond with error
    res.status(500).json({ error: err.message });
  }
};

/**
 * GET Purchase Dashboard Summary
 * POST /:tenantId/dashboards/purchases
 */
exports.getPurchases = async (req, res) => {
  try {
    const { tenantId } = req.params;
    const payload = req.body;

    // Delegate to Purchase service
    const data = await purchaseService.getSummary(tenantId, payload);

    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

/**
 * GET COGS Dashboard Summary
 * POST /:tenantId/dashboards/cogs
 */
exports.getCogs = async (req, res) => {
  try {
    const { tenantId } = req.params;
    const payload = req.body;

    // Delegate to COGS service
    const data = await cogsService.getSummary(tenantId, payload);

    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
