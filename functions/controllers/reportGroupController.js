const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.REPORT_GROUPS);
const schema = require("@/models/reportGroupSchema");
const reportGroupService = require("@/services/reportGroupService");

exports.createReportGroup = async (req, res) => {
  const { tenantId } = req.params;
  const { error, value } = schema.validate({ ...req.body, tenantId });
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  try {
    const existingReportGroup = await db
      .where("tenantId", "==", value.tenantId)
      .where("name", "==", value.name)
      .limit(1)
      .get();

    if (!existingReportGroup.empty) {
      return res.status(400).json({
        status: "error",
        message: `Report Group '${value.name}' already exists`
      });
    }

    const newReportGroupRef = db.doc();
    await newReportGroupRef.set({ ...value, id: newReportGroupRef.id });
    res.status(200).json({ message: "Report Group created successfully" });
  } catch (err) {
    console.error("Error creating report group:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getReportGroups = async (req, res) => {
  const { tenantId } = req.params;

  try {
    const snapshot = await db.where("tenantId", "==", tenantId).get();
    const reportGroups = snapshot.docs.map((doc) => {
      const data = doc.data();
      return data;
    });

    res.status(200).json(reportGroups);
  } catch (err) {
    console.error("Error fetching report groups:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getReportGroupById = async (req, res) => {
  try {
    const doc = await db.doc(req.params.id).get();
    if (!doc.exists) {
      return res.status(404).json({ message: "Report Group not found" });
    }
    res.status(200).json(doc.data());
  } catch (err) {
    console.error("Error fetching report group:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.updateReportGroup = async (req, res) => {
  const { tenantId } = req.params;
  const { error, value } = schema.validate({ ...req.body, tenantId });
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  try {
    const existingReportGroup = await db
      .where("tenantId", "==", value.tenantId)
      .where("name", "==", value.name)
      .limit(1)
      .get();

    if (
      !existingReportGroup.empty &&
      existingReportGroup.docs[0].id !== req.params.id
    ) {
      return res
        .status(400)
        .json({ message: `Report Group '${value.name}' already exists` });
    }

    await db.doc(req.params.id).update(value);
    res.status(200).json({ message: "Report Group updated successfully" });
  } catch (err) {
    console.error("Error updating report group:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.activateReportGroup = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    await reportGroupService.activateReportGroup(tenantId, id); // Call service to activate

    res.status(200).json({
      message: "Report Group activated successfully"
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

exports.deactivateReportGroup = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    await reportGroupService.deactivateReportGroup(tenantId, id); // Call service to deactivate (with validation)

    res.status(200).json({
      message: "Report Group deactivated successfully"
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};
