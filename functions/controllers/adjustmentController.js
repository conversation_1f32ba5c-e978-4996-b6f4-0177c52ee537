const {
  createAdjustment,
  getAdjustments,
  getAdjustmentById,
  updateAdjustment
} = require("@/services/adjustmentService");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const { validateIdentityFilters } = require("@/helpers/reportHelper");

exports.insertAdjustment = async (req, res) => {
  const payload = {
    ...req.body,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
      time: FD.now()
    }
  };
  try {
    const adjustment = await createAdjustment(payload);
    res.status(201).json(adjustment);
  } catch (error) {
    console.error("Error creating adjustment:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getAdjustments = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const filters = {
      tenantId: tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null
    };

    const _filters = validateIdentityFilters(req.identity, filters);

    const adjustments = await getAdjustments(_filters);
    res.status(200).json(adjustments);
  } catch (error) {
    console.error("Error fetching adjustments:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getAdjustmentById = async (req, res) => {
  try {
    const adjustment = await getAdjustmentById(req.params.id);
    res.status(200).json(adjustment);
  } catch (error) {
    console.error("Error fetching adjustment:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateAdjustment = async (req, res) => {
  const payload = {
    ...req.body,
    updatedBy: {
      id: req.identity.userId,
      name: req.identity.userName
    }
  };
  try {
    await updateAdjustment(req.params.id, payload);
    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating adjustment:", err);
    res.status(500).json({ message: err.message });
  }
};
