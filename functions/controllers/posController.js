
const { createConsumptionData } = require("@/services/consumptionService");

exports.captureSales = async (req, res) => {
    try {
        const tenantId = req.params.tenantId;
        if (!tenantId) {
            return res.status(400).json({
                success: false,
                message: "tenantId is required",
            });
        }
        const data = req.body;
        const result = await createConsumptionData(tenantId, data);
        res.status(200).json(result);
    } catch (error) {
        console.error("Error creating consumption:", error);
        res.status(500).json({ success: false, message: error.message });
    }
};