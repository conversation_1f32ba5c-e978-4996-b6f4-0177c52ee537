const { constructColumns } = require("@/helpers/reportHelper");
const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const { render, ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const { validateIdentityFilters } = require("@/helpers/reportHelper");
const {
  processConsumptionData,
  createConsumptionData,
  rerunConsumption,
} = require("@/services/consumptionService");
const {
  fetchConsumptionReportData,
} = require("@/repositories/consumptionRepo");

exports.getConsumptionDetails = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { resultType = ResultType.JSON } = req.query;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const reportInfo = REPORT_INFORMATION[REPORTS.CONSUMPTION_TRACKING];
    if (!reportInfo) throw new Error("Invalid report type");

    const _filters = validateIdentityFilters(req.identity, req.body.filters);

    // Now this returns the fully processed data
    const result = await fetchConsumptionReportData(tenantId, _filters);

    const headers = constructColumns(
      reportInfo.headers,
      req.body.columns,
      {},
      null,
      resultType
    );

    const output = {
      data: result,
      payload: req.body,
      headers: headers,
      expand: false,
      id: reportInfo.id,
      name: reportInfo.name,
      tenantId: tenantId,
      totalRow: null,
    };

    switch (resultType) {
      case ResultType.EXCEL:
        const resBuffer = await createXlsxReport(output);
        render(res, resultType, resBuffer, reportInfo.id);
        return;
      default:
        render(res, resultType, output, reportInfo.id);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.createConsumption = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }
    const data = req.body;
    let result = [];
    // const result = await processConsumptionData(tenantId);
    // const result = await createConsumptionData(tenantId,data['saleDetails']);
    // check
    res.status(200).json(result);
  } catch (error) {
    console.error("Error creating consumption:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.rerunConsumptionById = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "id is required",
      });
    }

    // Call service and capture result
    const result = await rerunConsumption(tenantId, id);

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error rerun consumption:", error);

    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

