// controllers/tallyController.js

const { ResultTypes } = require("@/defs/resultTypeDefs");
const { listGrnSummaries } = require("@/services/grnService");
const { convertData } = require("@/repositories/grnRepo");

/**
 * Tally GRN listing (NO AUTH, app-id based)
 * Mirrors grnController.getGRNs but without req.identity
 */
exports.getGRNs = async function (req, res) {
  try {
    const { tenantId } = req.body;
    if (!tenantId) {
      return res.status(400).json({
        message: "tenantId is required",
      });
    }

    const { ResultType: resultTypeParam = "json" } = req.query;

    const filters = {
      tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
      attachments: req.body.attachments || null,
      grnStatus: req.body.grnStatus || null,
      vendors: req.body.vendors || null,
    };

    const resultType = resultTypeParam.toLowerCase();

    const data = await listGrnSummaries(filters, resultType);

    // Fix: Map over the data and await all conversions
    const convertedData = await Promise.all(
      data.map((grn) => convertData(grn, grn.id))
    );

    switch (resultType) {
      case ResultTypes.EXCEL.toLowerCase():
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=tally_grns.xlsx"
        );
        res.setHeader(
          "Content-Type",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        );
        return res.send(convertedData);

      default:
        return res.status(200).json(convertedData);
    }
  } catch (err) {
    console.error("Error listing Tally GRNs:", err);
    return res.status(500).json({ message: err.message });
  }
};