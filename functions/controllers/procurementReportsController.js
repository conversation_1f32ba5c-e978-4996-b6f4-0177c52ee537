/**
 * Procurement Reports Controller
 * ------------------------------
 * Handles all tenant-specific procurement report requests.
 */

const { REPORTS } = require("@/defs/reportDefs");
const { createReportHandler } = require("@/helpers/reportHandlerHelper");
const procurementReportService = require("@/services/procurementReportService");

// GRN Summary Report
exports.getGRNReport = createReportHandler(REPORTS.GRN, procurementReportService.getGRNReport);

// Detailed GRN Report
exports.getDetailedGRNReport = createReportHandler(REPORTS.DETAILED_GRN, procurementReportService.getDetailedGRNReport);

// Location Purchase Report
exports.getLocationWiseGRNReport = createReportHandler(REPORTS.LOCATION_WISE_GRN, procurementReportService.getLocationWiseGRNReport);

// Vendor Purchase Report
exports.getVendorWiseGRNReport = createReportHandler(REPORTS.VENDOR_WISE_GRN, procurementReportService.getVendorWiseGRNReport);

// Category Purchase Report
exports.getCategoryWiseGRNReport = createReportHandler(REPORTS.CATEGORY_WISE_GRN, procurementReportService.getCategoryWiseGRNReport);

// Sub-Category Purchase Report
exports.getSubCategoryWiseGRNReport = createReportHandler(REPORTS.SUB_CATEGORY_WISE_GRN, procurementReportService.getSubCategoryWiseGRNReport);

// Item Purchase Report
exports.getItemWiseGRNReport = createReportHandler(REPORTS.ITEM_WISE_GRN, procurementReportService.getItemWiseGRNReport);

// Daily Purchase Report
exports.getDailyGRNReport = createReportHandler(REPORTS.DAILY_GRN, procurementReportService.getDailyGRNReport);
