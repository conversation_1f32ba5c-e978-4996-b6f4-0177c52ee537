const admin = require("firebase-admin");
const { recipeSchema: schema } = require("@/models/receipeSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.RECEIPES);
const { rupeeToPaise, paiseToRupee } = require("@/utils/money");

const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const { getNextRecipeId } = require("@/services/counterService");
const { calculateRecipeCost } = require("@/utils/money");
const receipeService = require("@/services/receipeService");

exports.insertReceipe = async (req, res) => {
  const tenantId = req.body.tenantId;
  let recipeCode = req.body.recipeCode?.trim();

  if (!recipeCode) {
    recipeCode = await getNextRecipeId(tenantId);
  }

  try {
    const validatedData = handleValidation({ ...req.body, recipeCode }, schema);
    if (!validatedData) return;
    // check uniqueness ItemCode
    const duplicateRecipeCode = await db
      .where("tenantId", "==", tenantId)
      .where("itemCode", "==", recipeCode)
      .limit(1)
      .get();

    if (!duplicateRecipeCode.empty) {
      return res.status(400).json({ message: "Recipe Code Already Exists" });
    }
    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    const newDocRef = db.doc();

    let totalAmount = 0;

    // Assign Firebase IDs to each ingredient
    const ingredientsWithIds = validatedData.ingredients.map((ingredient) => {
      const result = {
        ...ingredient,
        id: db.doc().id, // unique ID
        unitCost: rupeeToPaise(ingredient.unitCost),
        totalCost: rupeeToPaise(ingredient.totalCost),
      };
      totalAmount += result.totalCost;
      return result;
    });
    validatedData.cost = totalAmount;

    const receipe = {
      ...validatedData,
      ingredients: ingredientsWithIds,
      id: newDocRef.id,
      name: cleanName,
      nameNormalized: normalizedName,
    };
    await newDocRef.set(receipe);
    res.status(201).json(receipe);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getReceipeById = async (req, res) => {
  try {
    const receipe = await db.doc(req.params.id).get();
    if (!receipe.exists)
      return res.status(404).json({ message: "receipe not found" });
    const data = receipe.data();
    data.cost = paiseToRupee(data.cost);
    data.ingredients = data.ingredients.map((ing) => ({
      ...ing,
      unitCost: paiseToRupee(ing.unitCost),
      totalCost: paiseToRupee(ing.totalCost),
    }));
    return res.status(200).json(data);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getReceipes = async (req, res) => {
  try {
    const { tenantId } = req.params;

    // Validate tenantId
    if (!tenantId) {
      return res.status(400).json({
        status: "error",
        message: "tenantId is required",
      });
    }

    // Fetch only recipes for this tenantId
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    // Map through the data and convert unitCost and cost
    const response = snapshot.docs.map((doc) => {
      const data = doc.data();
      data.ingredients = data.ingredients.map((ing) => ({
        ...ing,
        unitCost: paiseToRupee(ing.unitCost),
        totalCost: paiseToRupee(ing.totalCost),
      }));
      return { ...data, cost: paiseToRupee(data.cost) };
    });

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching recipes:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateReceipe = async (req, res) => {
  const tenantId = req.body.tenantId;
  let recipeCode = req.body.recipeCode?.trim();

  if (!recipeCode) {
    recipeCode = await getNextRecipeId(tenantId);
  }

  try {
    const validatedData = handleValidation({ ...req.body, recipeCode }, schema);
    if (!validatedData) return;
    // check uniqueness ItemCode
    const duplicateRecipeCode = await db
      .where("tenantId", "==", tenantId)
      .where("itemCode", "==", recipeCode)
      .limit(1)
      .get();

    if (
      !duplicateRecipeCode.empty &&
      duplicateRecipeCode.docs[0].id !== req.params.id
    ) {
      return res.status(400).json({ message: "Recipe Code Already Exists" });
    }
    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      req.params.id,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    // update recipe
    const recipe = db.doc(req.params.id);
    let totalAmount = 0;
    validatedData.ingredients = validatedData.ingredients.map((ing) => {
      const result = {
        ...ing,
        unitCost: rupeeToPaise(ing.unitCost),
        totalCost: rupeeToPaise(ing.totalCost),
      };
      totalAmount += result.totalCost;
      return result;
    });
    validatedData.cost = totalAmount;

    await recipe.update({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });
    res.status(200).json("updated successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Controller for activating a receipe
exports.activateReceipe = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get receipe ID from URL
    const result = await receipeService.activateReceipe(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Receipe activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., receipe not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a receipe
exports.deactivateReceipe = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get receipe ID from URL
    const result = await receipeService.deactivateReceipe(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Receipe deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};
