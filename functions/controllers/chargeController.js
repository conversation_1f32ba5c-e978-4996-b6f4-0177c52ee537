const chargeService = require("@/services/chargeService");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

/**
 * Create a new charge
 * URL: POST /tenants/:tenantId/charges
 */
async function createCharge(req, res) {
  try {
    const tenantId = req.params.tenantId;     

    const body = {
      ...req.body,
      tenantId,
    };

    const data = await chargeService.createCharge(tenantId, body);
    res.status(201).json({ success: true, data });

  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Update an existing charge
 * URL: PUT /tenants/:tenantId/charges/:id
 */
async function updateCharge(req, res) {
  try {
    const tenantId = req.params.tenantId;
    const chargeId = req.params.id;    
    const data = await chargeService.updateCharge(tenantId, chargeId, req.body);
    res.json({ success: true, data });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Get a charge by ID
 * URL: GET /tenants/:tenantId/charges/:id
 */
async function getChargeById(req, res) {
  try {
    const { tenantId, id: chargeId } = req.params;
    const data = await chargeService.getChargeById(tenantId, chargeId);
    if (!data) return res.status(404).json({ success: false, message: "Charge not found" });
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
}

/**
 * Fetch all charges for a tenant (List)
 * URL: GET /tenants/:tenantId/charges
 */
async function getCharges(req, res) {
  try {
    const { tenantId } = req.params;
    const data = await chargeService.getCharges(tenantId);
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
}

/**
 * Activate a charge
 * URL: PUT /tenants/:tenantId/charges/:id/activate
 */
async function activateCharge(req, res) {
  try {
    const { tenantId, id } = req.params;
    const result = await chargeService.activateCharge(tenantId, id);
    res.status(200).json({
      success: true,
      message: "Charge activated successfully",
      data: result
    });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Deactivate a charge
 * URL: PUT /tenants/:tenantId/charges/:id/deactivate
 */
async function deactivateCharge(req, res) {
  try {
    const { tenantId, id } = req.params;
    const result = await chargeService.deactivateCharge(tenantId, id);
    res.status(200).json({
      success: true,
      message: "Charge deactivated successfully",
      data: result
    });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

module.exports = {
  createCharge,
  updateCharge,
  getChargeById,
  getCharges,
  activateCharge,
  deactivateCharge,
};

