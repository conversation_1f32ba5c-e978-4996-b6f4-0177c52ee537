const admin = require("firebase-admin");
const schema = require("@/models/vendorSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.VENDORS);
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const { getNextVendorId } = require("@/services/counterService");
const vendorService = require("@/services/vendorService");
const { propagateVendorName } = require("@/utils/updateLinkedData");

exports.insertVendor = async (req, res) => {
  // Auto-generate vendorId if not provided
  const tenantId = req.body.tenantId;
  let vendorId = req.body.vendorId?.trim();
  if (!vendorId) {
    vendorId = await getNextVendorId(tenantId);
  }

  try {
    const validatedData = handleValidation({ ...req.body, vendorId }, schema);
    if (!validatedData) return;
    // check uniqueness vendor id
    const duplicateVendorId = await db
      .where("tenantId", "==", tenantId)
      .where("vendorId", "==", vendorId)
      .limit(1)
      .get();

    if (!duplicateVendorId.empty) {
      return res.status(400).json({ message: "Vendor ID Already Exists" });
    }

    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);
    const newDocRef = db.doc();

    const vendor = {
      ...validatedData,
      id: newDocRef.id,
      name: cleanName,
      nameNormalized: normalizedName,
    };
    await newDocRef.set(vendor);
    res.status(200).json(vendor);
  } catch (error) {
    console.error("Insert Vendor Failed:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getVendorById = async (req, res) => {
  try {
    const vendor = await db.doc(req.params.id).get();
    if (!vendor.exists)
      return res.status(404).json({ message: "Vendor Not Found" });
    return res.status(200).json(vendor.data());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getVendors = async (req, res) => {
  const { tenantId } = req.params;

  if (!tenantId) {
    return res.status(400).json({
      status: "error",
      message: "tenantId is required",
    });
  }

  try {
    const vendorsRef = admin.firestore().collection(COLLECTIONS.VENDORS);
    const snapshot = await vendorsRef
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    const response = snapshot.docs.map((doc) => doc.data());
    res.status(200).json(response);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateVendor = async (req, res) => {
  // Auto-generate vendorId if not provided
  const tenantId = req.body.tenantId;
  let vendorId = req.body.vendorId?.trim();
  if (!vendorId) {
    vendorId = await getNextVendorId(tenantId);
  }

  try {
    const validatedData = handleValidation({ ...req.body, vendorId }, schema);
    if (!validatedData) return;
    // check uniqueness vendor id
    const duplicateVendorId = await db
      .where("tenantId", "==", tenantId)
      .where("vendorId", "==", vendorId)
      .limit(1)
      .get();

    if (
      !duplicateVendorId.empty &&
      duplicateVendorId.docs[0].id !== req.params.id
    ) {
      return res.status(400).json({ message: "Vendor ID Already Exists" });
    }
    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      req.params.id,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    // Update vendor
    const vendorRef = db.doc(req.params.id);
    await vendorRef.update({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });
    await propagateVendorName(req.params.id, cleanName);
    res.status(200).json("Updated Successfully");
  } catch (error) {
    console.error("Update Vendor Failed:", error);
    res.status(500).json({ message: error.message });
  }
};

// Controller for activating a vendor
exports.activateVendor = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get vendor ID from URL
    const result = await vendorService.activateVendor(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Vendor activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., vendor not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a vendor
exports.deactivateVendor = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get vendor ID from URL
    const result = await vendorService.deactivateVendor(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Vendor deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};