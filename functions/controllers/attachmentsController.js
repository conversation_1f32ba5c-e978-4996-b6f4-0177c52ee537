// controllers/attachmentsController.js
const ATTACHMENT_TYPES = require('@/defs/attachmentTypesDefs');
const { generateUploadUrls, generateReadUrls } = require('@/utils/storage');

/**
 * Controller: Generate signed upload URLs for attachments
 *
 * @route POST /attachments/:tenantId/upload-urls
 * @body { files: Array<{name, contentType}>, type: string, docId?: string }
 * @params { tenantId: string }
 * @returns { urls: Array<{ filePath, fileUrl }> }
 */
exports.generateUploadUrls = async (req, res) => {
    try {
        const { tenantId } = req.params;
        const { files, type, docId = 'default' } = req.body;

        if (!files || !files.length) {
            return res.status(400).json({ error: 'No files provided' });
        }

        // Validate attachment type
        if (!Object.values(ATTACHMENT_TYPES).includes(type)) {
            return res.status(400).json({ error: `Invalid attachment type: ${type}` });
        }

        // Generate signed upload URLs
        const urls = await generateUploadUrls({
            tenantId,
            type,
            docId,
            files
        });

        return res.json({
            files: urls
        });
    } catch (err) {
        console.error('Error generating attachment upload URLs:', err);
        return res.status(500).json({ error: err.message || 'Failed to generate signed URLs' });
    }
};


/**
 * Generate a signed read URL for a file in Firestore Storage
 *
 * @route GET /attachments/read-url
 * @query { filePath: string }
 * @returns { filePath: string }
 */
exports.generateReadUrl = async (req, res) => {
    try {
        const { filePath } = req.query;
        if (!filePath) {
            return res.status(400).json({ error: 'filePath is required' });
        }
        const data = await generateReadUrls([{ filePath }]);
        return res.json(data[0]);
    } catch (err) {
        console.error("Download signed URL error:", err);
        return res.status(500).json({
            error: err.message || 'Failed to generate download URL',
        });
    }
};