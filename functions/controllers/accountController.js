const admin = require("firebase-admin");
const schema = require("@/models/accountSchema");
const storeSchema = require("@/models/storeSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.ACCOUNT);
const { insertStoreLogic } = require("./storeController");
const { insertMenuItemLogic } = require("./menuItemController");
const { insertModifierLogic } = require("./modifierController");

const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");

exports.getAccountsData = async (tenantId, accountIds = []) => {
  let q = db.where("tenantId", "==", tenantId);

  if (accountIds.length) {
    q = q.where("id", "in", accountIds);
  }

  const snapshot = await q.orderBy("nameNormalized").get();
  return snapshot.docs.map(doc => doc.data());
};

exports.getAccounts = async (req, res) => {
  const tenantId = req.params.tenantId;
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const snapshot = await db.where("tenantId", "==", tenantId).get();
    const response = snapshot.docs.map((doc) => doc.data());
    res.status(200).send(response);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.linkAccount = async (req, res) => {
  const {
    name,
    tenantId,
    posId,
    stores = [],
    menuItems = [],
    modifiers = [],
  } = req.body;

  try {
    // const tenantDetails = await fetchTenantDetails(tenantId);
    const account = await getOrCreateAccount(tenantId, posId, name, res);
    const storePayload = await prepareStores(stores, account, res);
    const menuItemPayload = prepareMenuItems(menuItems, account);
    const modifierPayload = prepareModifiers(modifiers, account);

    const [storeIds, menuItemIds, modifierIds] = await Promise.all([
      insertStoreLogic(storePayload, res),
      insertMenuItemLogic(menuItemPayload, res),
      insertModifierLogic(modifierPayload, res),
    ]);

    res.status(200).json({
      message: "Account created successfully",
      account,
      storeCount: storeIds.length,
      menuItemCount: menuItemIds.length,
      modifierCount: modifierIds.length,
    });
  } catch (error) {
    console.error("linkAccount error:", error);
    res.status(500).json({ error: error.message || "Internal Server Error" });
  }
};

// const fetchTenantDetails = async (tenantId) => {
//   const tenantDoc = await admin
//     .firestore()
//     .collection("tenant")
//     .doc(tenantId)
//     .get();
//   if (!tenantDoc.exists) throw new Error("Tenant not found");
//   return tenantDoc.data();
// };

const getOrCreateAccount = async (tenantId, posId, name, res) => {
  const accountQuery = await db
    .where("posId", "==", posId.trim())
    .where("tenantId", "==", tenantId)
    .limit(1)
    .get();

  if (!accountQuery.empty) return accountQuery.docs[0].data();

  const validatedData = handleValidation(
    {
      posId: posId.trim(),
      name: name,
      tenantId: tenantId,
    },
    schema
  );
  if (!validatedData) return;

  const cleanName = trimName(name);
  const { normalizedName } = await nameValidation(
    name,
    db,
    null,
    "tenantId",
    tenantId
  );

  const newAccountRef = db.doc();
  const newAccount = {
    ...validatedData,
    id: newAccountRef.id,
    name: cleanName,
    nameNormalized: normalizedName,
  };
  await newAccountRef.set(newAccount);
  return newAccount;
};

const prepareStores = async (stores, account, res) => {
  const prepared = [];

  for (const store of stores) {
    const validatedData = handleValidation(
      {
        posId: store.posId,
        name: store.name,
        locationType: store.storeType,
        accountId: account.id,
        accountName: account.name,
        tenantId: account.tenantId,
      },
      storeSchema
    );
    if (!validatedData) {
      console.log("validation failed for store", store);
      continue;
    }
    const storeDb = admin.firestore().collection(COLLECTIONS.LOCATIONS);
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      storeDb,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) {
      console.error("name validation failed for store", store);
      throw new Error(error);
    }

    prepared.push({
      posId: store.posId,
      name: trimName(store.name),
      nameNormalized: normalizedName,
      locationType: store.storeType,
      accountId: account.id,
      accountName: account.name,
      tenantId: account.tenantId,
    });
  }
  return prepared;
};

const prepareMenuItems = (menuItems, account) =>
  menuItems.map((item) => ({
    posId: item.posId,
    itemName: trimName(item.itemName),
    itemCode: item.itemCode,
    servingLevels: item.servingLevels ?? [],
    account: {
      id: account.id,
      name: account.name,
      posId: account.posId,
    },
    tenantId: account.tenantId,
  }));

const prepareModifiers = (modifiers, account) =>
  modifiers.map((mod) => ({
    posId: mod.posId,
    itemName: trimName(mod.itemName),
    servingLevels: mod.servingLevels ?? [],
    account: {
      id: account.id,
      name: account.name,
      posId: account.posId,
    },
    tenantId: account.tenantId,
  }));
