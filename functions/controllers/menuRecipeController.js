const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const {
  createMenuRecipe,
  getMenuRecipes,
  getMenuRecipeById,
  prepareMenuRecipeSplitup,
  debitStockByRecipe,
  updateMenuRecipe,
  deductSalesData
} = require("@/services/menuRecipeService");
const { validateIdentityFilters } = require("@/helpers/reportHelper");


exports.insertMenuRecipe = async (req, res) => {
  const payload = {
    ...req.body,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
      time: FD.now()
    }
  };
  try {
    const menuRecipe = await createMenuRecipe(payload);
    res.status(201).json(menuRecipe);
  } catch (error) {
    console.error("Error creating menu recipe:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getMenuRecipeSplit = async (req, res) => {
  try {
    const menuRecipe = await prepareMenuRecipeSplitup(req.params.tenantId,req.params.id,req.body.inventoryLocationId);
    // const available = await debitStockByRecipe({itemId:menuRecipe['ingredients'][0]['itemId'],qtyInRecipeUOM:1000,inventoryLocationId:req.body.inventoryLocationId});
    // console.log("🚀 ~ available:", available)
    res.status(200).json(menuRecipe);
  } catch (error) {
    console.error("Error fetching menu recipe:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.salesReduction = async (req, res) => {
  try {
    // const menuRecipe = await deductSalesData(req.params.tenantId);
    const menuRecipe = await deductSalesData('AKTBsuNbv3GjwLCa0eYf');
    res.status(200).json(menuRecipe);
  } catch (error) {
    console.error("Error fetching menu recipe:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getMenuRecipes = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const filters = {
      tenantId: tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null
    };

    const _filters = validateIdentityFilters(req.identity, filters);

    const menuRecipes = await getMenuRecipes(_filters);
    res.status(200).json(menuRecipes);
  } catch (error) {
    console.error("Error fetching menu recipes:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getMenuRecipeById = async (req, res) => {
  try {
    const menuRecipe = await getMenuRecipeById(req.params.id);
    res.status(200).json(menuRecipe);
  } catch (error) {
    console.error("Error fetching menu recipe:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateMenuRecipe = async (req, res) => {
  const payload = {
    ...req.body,
    completedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
      time: FD.now()
    }
  };
  try {
    await updateMenuRecipe(req.params.id, payload);
    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating menu recipe:", err);
    res.status(500).json({ message: err.message });
  }
};
