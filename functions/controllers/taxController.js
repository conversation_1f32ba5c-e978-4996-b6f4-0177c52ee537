const taxService = require("@/services/taxService");

/**
 * Create a new tax
 * URL: POST /tenants/:tenantId/taxes
 */
async function createTax(req, res) {
  try {
    const { tenantId } = req.params;
    const data = await taxService.createTax(tenantId, req.body);
    res.status(201).json({ success: true, data });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Update an existing tax
 * URL: PUT /tenants/:tenantId/taxes/:id
 */
async function updateTax(req, res) {
  try {
    const { tenantId } = req.params;
    const taxId = req.params.id;
    const data = await taxService.updateTax(tenantId, taxId, req.body);
    res.json({ success: true, data });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Get a tax by ID
 * URL: GET /tenants/:tenantId/taxes/:id
 */
async function getTaxById(req, res) {
  try {
    const { tenantId, id: taxId } = req.params;
    const data = await taxService.getTaxById(tenantId, taxId);
    if (!data)
      return res.status(404).json({ success: false, message: "Tax not found" });
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
}

/**
 * Fetch all active taxes for a tenant
 * URL: GET /tenants/:tenantId/taxes/active
 */
async function getActiveTaxes(req, res) {
  try {
    const { tenantId } = req.params;
    const data = await taxService.getTaxes(tenantId);
    res.json(data);
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
}

// Controller for activating a tax
async function activateTax(req, res) {
  try {
    const { tenantId, id } = req.params; // Get tax ID from URL
    const result = await taxService.activateTax(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Tax activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., tax not found)
    res.status(400).json({ message: err.message });
  }
}

// Controller for deactivating a tax
async function deactivateTax(req, res) {
  try {
    const { tenantId, id } = req.params; // Get tax ID from URL
    const result = await taxService.deactivateTax(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Tax deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
}

module.exports = {
  createTax,
  updateTax,
  getTaxById,
  getActiveTaxes,
  activateTax,
  deactivateTax,
};
