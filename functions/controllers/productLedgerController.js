const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.PRODUCT_LEDGERS);
const schema = require("@/models/productLedgerSchema");
const productLedgerService = require("@/services/productLedgerService");

exports.createProductLedger = async (req, res) => {
  const { tenantId } = req.params;
  const { error, value } = schema.validate({ ...req.body, tenantId });
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  try {
    const existingProductLedger = await db
      .where("tenantId", "==", value.tenantId)
      .where("name", "==", value.name)
      .limit(1)
      .get();

    if (!existingProductLedger.empty) {
      return res.status(400).json({
        status: "error",
        message: `Product Ledger '${value.name}' already exists`,
      });
    }

    const newProductLedgerRef = db.doc();
    await newProductLedgerRef.set({ ...value, id: newProductLedgerRef.id });
    res.status(200).json({ message: "Product Ledger created successfully" });
  } catch (err) {
    console.error("Error creating product ledger:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getProductLedgers = async (req, res) => {
  const { tenantId } = req.params;

  try {
    const snapshot = await db.where("tenantId", "==", tenantId).get();
    const productLedgers = snapshot.docs.map((doc) => {
      const data = doc.data();
      return data;
    });

    res.status(200).json(productLedgers);
  } catch (err) {
    console.error("Error fetching product ledgers:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getProductLedgerById = async (req, res) => {
  try {
    const doc = await db.doc(req.params.id).get();
    if (!doc.exists) {
      return res.status(404).json({ message: "Product Ledger not found" });
    }
    res.status(200).json(doc.data());
  } catch (err) {
    console.error("Error fetching product ledger:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.updateProductLedger = async (req, res) => {
  const { tenantId } = req.params;
  const { error, value } = schema.validate({ ...req.body, tenantId });
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  try {
    const existingProductLedger = await db
      .where("tenantId", "==", value.tenantId)
      .where("name", "==", value.name)
      .limit(1)
      .get();

    if (
      !existingProductLedger.empty &&
      existingProductLedger.docs[0].id !== req.params.id
    ) {
      return res
        .status(400)
        .json({ message: `Product Ledger '${value.name}' already exists` });
    }

    await db.doc(req.params.id).update(value);
    res.status(200).json({ message: "Product Ledger updated successfully" });
  } catch (err) {
    console.error("Error updating product ledger:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.activateProductLedger = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    await productLedgerService.activateProductLedger(tenantId, id); // Call service to activate

    res.status(200).json({
      message: "Product Ledger activated successfully",
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

exports.deactivateProductLedger = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    await productLedgerService.deactivateProductLedger(tenantId, id); // Call service to deactivate (with validation)

    res.status(200).json({
      message: "Product Ledger deactivated successfully",
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};
