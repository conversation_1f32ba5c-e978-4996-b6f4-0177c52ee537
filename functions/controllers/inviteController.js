const { COLLECTIONS } = require("@/defs/collectionDefs");
const admin = require("firebase-admin");
const db = admin.firestore().collection(COLLECTIONS.USERS);

exports.inviteCallback = async (req, res) => {
  const { digitorySsoId } = req.body;
  if (!digitorySsoId) {
    return res.status(400).json({ message: "digitorySsoId is required" });
  }
  try {
    const userDoc = db.doc(req.params.id);
    await userDoc.update({ digitorySsoId, verified: true });
    res.status(200).json("Digitory SSO ID Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};