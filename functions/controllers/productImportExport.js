// handler for import & export product configuration

const { validateAndFormat } = require("@/modules/productImpex/sheet");
const {
  exportProductConfiguration: ExportData,
} = require("@/modules/productImpex/exportData");
const {
  importProductConfiguration: ImportData,
} = require("@/modules/productImpex/importData");
const { COLLECTIONS } = require("@/defs/collectionDefs");

const admin = require("firebase-admin");
const schema = require("@/models/importExportLogSchema");
const dbCollection = admin
  .firestore()
  .collection(COLLECTIONS.IMPORT_EXPORT_LOGS);
const { handleValidation } = require("@/utils/validation");

/**
 * Validates if a pending task is already running for the same tenant
 * and starts a new import task if none are running.
 *
 * @param {string} tenantId
 * @param {string} fileName
 * @param {Response} res
 * @throws {Error} if a pending task is already running
 * @returns {Promise<{ taskId: string, error: string }>} a promise with the created
 * task ID and error message if any
 */
const validateAndCreateImportLog = async ({
  tenantId,
  fileName,
  userName,
  userId,
  res,
}) => {
  try {
    const taskQuery = dbCollection
      .where("tenantId", "==", tenantId)
      .where("requestStatus", "==", "pending")
      .limit(1);

    const taskSnapshot = await taskQuery.get();

    if (taskSnapshot.empty) {
      return await startImport({ tenantId, fileName, userName, userId, res });
    }

    const [pendingTask] = taskSnapshot.docs;
    if (pendingTask.exists) {
      const error = new Error(
        `Previous task ${pendingTask.id} is already in progress. Please wait for it to complete`
      );
      throw error;
    }
  } catch (error) {
    const { message } = error;
    console.error("error starting import task", error);
    res.status(error instanceof Error ? 400 : 500).json({ message });
  }
};

const startImport = async ({ tenantId, fileName, userName, userId }) => {
  try {
    const taskDetails = {
      tenantId: tenantId,
      fileName: fileName,
      uploadedAt: new Date().toISOString(),
      uploadedById: userId,
      uploadedByName: userName,
      requestStatus: "pending",
      type: "import",
    };

    const validatedData = handleValidation(taskDetails, schema);
    if (!validatedData) {
      console.log("validation failed");
    }

    const newDocRef = dbCollection.doc();
    await newDocRef.set({ ...validatedData, id: newDocRef.id });
    return {
      taskId: newDocRef.id,
      error: null,
    };
  } catch (error) {
    console.log("error in validation", error);
    return {
      taskId: null,
      error: error.message,
    };
  }
};

const completeImport = async (taskId, result) => {
  const invalidHeaders = result.invalidHeaders || [];
  const logEntries = result.logEntry || [];

  try {
    if (!Array.isArray(logEntries)) {
      throw new Error("logEntries must be an array");
    }

    // ✅ Normalize all log entries
    const formattedLogs = logEntries.map((entry) => ({
      source: entry.sheetName,
      totalRecordsProcessed: entry.totalRecords,
      successCount: entry.successCount,
      failureCount: entry.failureCount,
      errors: (entry.errors || []).map((err) => ({
        rowNumber: err.rowNumber,
        fields: (err.fields || []).map((f) => ({
          name: f.name,
          errors: f.errors,
        })),
      })),
    }));

    const formattedHeaderErrors = invalidHeaders.map((entry) => ({
      source: entry.sheet,
      errors: (entry.errors || []).map((err) => ({
        type: err.type,
        position: err.position,
        expected: {
          header: err.expected?.header,
          key: err.expected?.key,
        },
        got: err.got,
      })),
    }));

    // ✅ Determine import status
    const hasErrors = formattedLogs.some((log) => log.errors?.length > 0);
    const hasHeaderErrors = formattedHeaderErrors.length > 0;

    const importStatus =
      hasErrors || hasHeaderErrors ? "full_error" : "success";

    // ✅ Construct Firestore document update payload
    const taskUpdateDetails = {
      completedAt: new Date().toISOString(),
      requestStatus: "completed",
      importStatus,
      result: {
        message:
          formattedLogs.length === 1
            ? `${formattedLogs[0].source} import completed`
            : `Import completed for ${formattedLogs.length} sheets`,
        logs: formattedLogs,
        headerErrors: formattedHeaderErrors,
      },
    };

    await dbCollection.doc(taskId).update(taskUpdateDetails);
  } catch (err) {
    console.error(`❌ Error updating import task ${taskId}`, err);
  }
};

exports.getImportExportLogs = async (req, res) => {
  const tenantId = req.params.tenantId;
  try {
    const snapshot = await dbCollection.where("tenantId", "==", tenantId).get();
    const result = snapshot.docs.map((doc) => doc.data());
    res.status(200).json(result);
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: error });
  }
};

/**
 * @api {post} /tenants/:tenantId/import-product-configuration Import product configuration
 * @apiName ImportProductConfiguration
 * @apiGroup Product
 * @apiDescription Import product configuration from an Excel file
 * @apiParam {String} tenantId Tenant ID
 * @apiParam {File} file Excel file containing product configuration
 * @apiSuccess {String} message Success message
 * @apiError {String} message Error message
 *
 */

exports.importProductConfiguration = async (req, res) => {
  const { userName, userId } = req.identity;
  try {
    const uploadedFile = req.files?.find((f) => f.fieldname === "file");
    if (!uploadedFile) {
      return res.status(400).json({
        error: "No file uploaded",
      });
    }

    const tenantId = req.params.tenantId;
    const sheets = req.query.sheets;
    const sheetNames = sheets ? sheets.split(",") : [];
    const { isValid, formattedSheets, invalidSheets, message } =
      validateAndFormat(sheetNames);

    // Return error if invalid sheet names provided
    if (!isValid) {
      return res.status(400).json({
        message,
        invalidSheets,
      });
    }

    const { taskId, error } = await validateAndCreateImportLog({
      tenantId,
      fileName: uploadedFile.originalname,
      userName,
      userId,
      res,
    });

    if (error) {
      return res.status(400).json({ message: error });
    }

    // Import product configuration
    const result = await ImportData(tenantId, uploadedFile, formattedSheets);
    await completeImport(taskId, result);

    if (result.err && result.invalidHeaders?.length) {
      return res.status(400).json({
        message: "Validation failed: Invalid headers in uploaded files.",
        taskId,
        validationReport: result,
      });
    }

    const hasErrors =
      result.logEntry &&
      result.logEntry.some(
        (entry) =>
          (entry.errors && entry.errors.length > 0) ||
          (entry.failureCount && entry.failureCount > 0)
      );

    if (result.err || hasErrors) {
      return res.status(500).json({
        message:
          "Product configuration imported successfully, but there were errors.",
        taskId: taskId,
        hasErrors: true,
        error: result.err,
      });
    }

    res.status(200).json({
      message: "Product configuration imported successfully",
    });
  } catch (error) {
    console.log({ error }, "unexpected error");
    res.status(500).json({ message: error });
  }
};

/**
 * @api {get} /tenants/:tenantId/export-product-configuration Export product configuration
 * @apiName ExportProductConfiguration
 * @apiGroup Product
 * @apiDescription Export product configuration to an Excel file
 * @apiParam {String} tenantId Tenant ID
 * @apiSuccess {File} file Excel file containing product configuration
 * @apiError {String} message Error message
 */
exports.exportProductConfiguration = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const isTemplate = req.query.isTemplate === "true" ? true : false;
    const sheets = req.query.sheets;
    const sheetNames = sheets ? sheets.split(",") : [];
    const { isValid, formattedSheets, invalidSheets, message } =
      validateAndFormat(sheetNames);

    // Return error if invalid sheet names provided
    if (!isValid) {
      return res.status(400).json({
        message,
        invalidSheets,
      });
    }
    // Export product configuration
    const { err, workbook, fileName } = await ExportData(
      tenantId,
      isTemplate,
      formattedSheets
    );

    if (err) {
      return res.status(500).json({
        message: "Error generating Excel",
        error: err,
      });
    }

    // Set headers for Excel download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);

    res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
    // Stream workbook to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.log(error, "error");
    res.status(500).json({ message: error });
  }
};
