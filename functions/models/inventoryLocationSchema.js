const Joi = require("joi");

const approvalLevelSchema = Joi.object({
  order: Joi.number().required(),
  roleId: Joi.string().required(),
  roleName: Joi.string().required(),
  amountBased: Joi.boolean().required(),
  minAmount: Joi.when("amountBased", {
    is: true,
    then: Joi.number().required(),
    otherwise: Joi.forbidden(),
  }),
});

const inventoryLocationSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().max(100).required(),
  locationId: Joi.string().required(),
  locationName: Joi.string().optional(),
  tagId: Joi.string().allow("").optional(),
  tagName: Joi.string().allow("").optional(),
  tenantId: Joi.string().required(),
  nameNormalized: Joi.string().max(100).optional(),
  activeStatus: Joi.boolean().default(true),
  isDefault: Joi.boolean().optional(),
  floorIds: Joi.array().items(Joi.object({
    id: Joi.number().required(),
    name: Joi.string().allow(null, "").optional()
  })).optional(),
  isBarCounter: Joi.boolean().default(false),
  approvalConfig: Joi.object({
    po: Joi.array().items(approvalLevelSchema).optional(),
    pr: Joi.array().items(approvalLevelSchema).optional(),
    grn: Joi.array().items(approvalLevelSchema).optional(),
    transfer: Joi.array().items(approvalLevelSchema).optional(),
  }).optional(),
});

module.exports = inventoryLocationSchema;
