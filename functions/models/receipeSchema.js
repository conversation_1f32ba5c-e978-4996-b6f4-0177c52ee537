const Joi = require("joi");

const ingredientSchema = require("@/models/ingredientSchema");
const uomSchema = require("@/models/uomSchema");

const recipeSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  recipeType: Joi.string().valid("recipe", "subRecipe").default("recipe"),
  name: Joi.string().max(100).required(),
  recipeCode: Joi.string().min(4).max(20).required(),
  nameNormalized: Joi.string().max(100).optional(),
  tags: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().max(100).optional(),
      })
    )
    .optional(),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  recipeUnit: uomSchema.required(),
  ingredients: Joi.array()
    .items(ingredientSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Ingredient is Required" }),
  cookingProcedure: Joi.array().items(Joi.string().max(1000)).optional(),
  cost: Joi.number().precision(2).allow(null).optional(),
  activeStatus: Joi.boolean().default(true),
  rowNumber: Joi.number().optional(),
  remarks: Joi.string().allow("", null).optional(),
});

module.exports = { recipeSchema, ingredientSchema };
