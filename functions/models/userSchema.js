const Joi = require("joi");

const userSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  tenantName: Joi.string().optional().allow("", null),
  name: Joi.string().max(100).optional(),
  roleId: Joi.string().optional(),
  roleName: Joi.string().optional(),
  emailId: Joi.string()
    .email()
    .required()
    .messages({ "string.pattern.base": "E-mail Must be Valid" }),
  activeStatus: Joi.boolean().default(true),
  isAdmin: Joi.boolean().default(false),
  verified: Joi.boolean().default(false),
  digitorySsoId: Joi.string().allow("", null),
  allLocations: Joi.boolean().required(),
  allInventoryLocations: Joi.boolean().required(),
  locationIds: Joi.array()
    .items(Joi.string())
    .min(1)
    .when('allLocations', { is: false, then: Joi.required(), otherwise: Joi.forbidden() }),
  inventoryLocationIds: Joi.array()
    .items(Joi.string())
    .min(1)
    .when('allInventoryLocations', { is: false, then: Joi.required(), otherwise: Joi.forbidden() })
});

module.exports = userSchema;
