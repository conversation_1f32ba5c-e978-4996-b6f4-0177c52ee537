const Joi = require("joi");

const symbolPattern = /^[^\s]{1,30}$/;

const houseUnitSchema = Joi.object({
  id: Joi.string(),
  name: Joi.string().max(100).required(),
  nameNormalized: Joi.string().max(100).optional(),
  symbol: Joi.string()
    .max(30)
    .pattern(symbolPattern)
    .required()
    .messages({ "string.pattern.base": "Invalid symbol" }),
  tenantId: Joi.string().required(),
  toUnit: Joi.string().required(),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity must be greater than zero" }),
  activeStatus: Joi.boolean().default(true),
  default: Joi.boolean().default(false),
});

module.exports = houseUnitSchema;
