const Joi = require("joi");

const packageSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().max(100).required(),
  packageCode: Joi.string().max(50).allow("").optional(),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  unitCost: Joi.number().min(0).required(),
  emptyWeight: Joi.number().min(0).default(0),
  fullWeight: Joi.number().min(0).default(0),
  rowNumber: Joi.number().optional(),
  toUnit: Joi.string().allow(null).optional(),
});

module.exports = packageSchema;
