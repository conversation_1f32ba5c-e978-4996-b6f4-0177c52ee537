const Joi = require("joi");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const { amountSchema } = require("@/models/amountSchema");

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
});

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  }),
);

const itemSchema = Joi.object({
  ordinal: Joi.number().integer().required(),
  itemId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  vendor: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }),
  quantity: Joi.number().positive().required(),
  pkgUOM: Joi.string().allow("", null).optional(),
  purchaseUOM: Joi.string().required(),
  countingUOM: Joi.string().allow(null, "").optional(),
  itemType: Joi.string().allow(null, "").optional(),
  categoryId: Joi.string().required(),
  subcategoryId: Joi.string().required(),
  categoryName: Joi.string().required(),
  subcategoryName: Joi.string().required(),
  stockable: Joi.boolean().default(true),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
    toUnit: Joi.string().optional(),
    emptyWeight: Joi.number().default(0).optional(),
    fullWeight: Joi.number().default(0).optional(),
  })
    .allow(null)
    .optional(),
  remarks: Joi.string().allow("", null),
  hsnCode: Joi.string().allow("", null),
  contractType: Joi.string().allow("", null),
  contractPrice: Joi.number().precision(2).default(0),
  contractId: Joi.string().allow("", null),
  contractNumber: Joi.string().allow("", null),
  inStock: Joi.number().default(0),
  inclTax: Joi.boolean().default(false),
  unitCost: Joi.number().precision(2).required(),
  mrp: Joi.number().allow(null).optional(),
  ...amountSchema,
});

const statusTimelineEntrySchema = Joi.object({
  name: Joi.string()
    .valid(
      purchaseStatus.DRAFT,
      purchaseStatus.SUBMITTED,
      purchaseStatus.APPROVED,
      purchaseStatus.REJECTED,
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
    )
    .required(),
  time: timestampSchema.required(),
  by: userSchema.required(),
});

const purchaseRequestSchema = Joi.object({
  location: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).required(),
  inventoryLocation: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).optional(),
  tenantId: Joi.string().required(),

  items: Joi.array().items(itemSchema).min(1).required(),

  requestedBy: userSchema.optional(),
  updatedBy: userSchema.optional(),

  status: Joi.string()
    .valid(
      purchaseStatus.DRAFT,
      purchaseStatus.SUBMITTED,
      purchaseStatus.APPROVED,
      purchaseStatus.REJECTED,
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
    )
    .required(),

  statusTimeline: Joi.array()
    .items(statusTimelineEntrySchema)
    .min(1)
    .optional(),
  prNumber: Joi.string().optional(),
  id: Joi.string().optional(),
  deliveryDate: timestampSchema.optional(),
  vendorType: Joi.number().default(2),
  rejectedReason: Joi.string().optional(),
  closedReason: Joi.string().optional(),
  activeStatus: Joi.boolean().default(true),
  lastUpdatedTime: timestampSchema.allow(null).optional(),
  remarks: Joi.string().allow("", null),

  transferDetails: Joi.array()
    .items(
      Joi.object({
        transferNumber: Joi.string().optional(),
        transferId: Joi.string().optional(),
        workAreaId: Joi.string().optional(),
        workAreaName: Joi.string().optional(),
        locationId: Joi.string().optional(),
        locationName: Joi.string().optional(),
        items: Joi.array().items(Joi.any()).optional(),
      }),
    )
    .optional(),
  ...amountSchema,
});

module.exports = purchaseRequestSchema;
