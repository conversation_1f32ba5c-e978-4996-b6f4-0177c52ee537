const Joi = require("joi");

const uomSchema = require("@/models/uomSchema");

const ingredientSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  consumptionQuantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  purchaseUnit: uomSchema.optional(),
  countingUnit: uomSchema.optional(),
  recipeUnit: uomSchema,
  rowNumber: Joi.number().optional(),
  unitCost: Joi.number().min(0).required(),
  totalCost: Joi.number().min(0).optional(),
  isSubRecipe: Joi.boolean().default(false),
  yield: Joi.number().optional(),
  itemType: Joi.string().optional(),
  stockable: Joi.boolean().optional(),
  defaultPackage: Joi.boolean().optional(),
  recipeQuantity: Joi.number().optional(),
  packages: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
        packageCode: Joi.string().optional(),
        unitCost: Joi.number().optional(),
        quantity: Joi.number().positive().optional(),
        toUnit: Joi.string().optional(),
        emptyWeight: Joi.number().default(0).optional(),
        fullWeight: Joi.number().default(0).optional(),
      })
    )
    .allow(null)
    .optional(),
  serviceType: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().max(100).optional(),
      })
    )
    .optional(),
});

module.exports = ingredientSchema;
