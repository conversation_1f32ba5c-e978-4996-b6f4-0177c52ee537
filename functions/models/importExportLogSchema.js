const Joi = require("joi");

const fieldErrorSchema = Joi.object({
  name: Joi.string().required(),
  errors: Joi.array().items(Joi.string()).required(),
});

const rowErrorSchema = Joi.object({
  rowNumber: Joi.number().integer().min(1).required(),
  fields: Joi.array().items(fieldErrorSchema).required(),
});

const rowHeaderErrorSchema = Joi.object({
  type: Joi.string().optional(),
  position: Joi.string().optional(),
  expected: Joi.object({
    header: Joi.string().optional(),
    key: Joi.string().optional(),
  }),
  got: Joi.string().optional(),
});

const logSchema = Joi.object({
  source: Joi.string().required(), // was sheetName
  totalRecordsProcessed: Joi.number().integer().min(0).required(),
  successCount: Joi.number().integer().min(0).required(),
  failureCount: Joi.number().integer().min(0).required(),
  errors: Joi.array().items(rowErrorSchema).required(),
});

const headerSchema = Joi.object({
  source: Joi.string().required(),
  errors: Joi.array().items(rowHeaderErrorSchema).required(),
})

const importLogSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  fileName: Joi.string().required(),
  type: Joi.string().valid("import", "export").required(),
  uploadedAt: Joi.string().required(),
  completedAt: Joi.string().allow(null),

  uploadedById: Joi.string().required(),
  uploadedByName: Joi.string().required(),

  requestStatus: Joi.string().valid("pending", "completed").required(),
  importStatus: Joi.string()
    .valid("success", "partial_error", "full_error")
    .optional(),

  result: Joi.object({
    message: Joi.string().required(),
    logs: Joi.array().items(logSchema).default([]),
    headerErrors: Joi.array().items(headerSchema).default([])
  }),
});

module.exports = importLogSchema;
