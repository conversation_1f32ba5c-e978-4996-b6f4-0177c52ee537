const Joi = require("joi");

const subCategorySchema = Joi.object({
  id: Joi.string().optional(), // will be generated
  name: Joi.string().required(),
});

const categorySchema = Joi.object({
  id: Joi.string().optional(),
  tenantId: Joi.string().optional(),
  name: Joi.string().required(),
  subCategories: Joi.array().items(subCategorySchema).min(1).required(),
  location: Joi.string().optional(),
  activeStatus: Joi.boolean().default(true),
  isBarCategory: Joi.boolean().default(false), 
  nameNormalized: Joi.string().max(100).optional(),
});

module.exports = categorySchema;
