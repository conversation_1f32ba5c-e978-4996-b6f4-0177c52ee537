// repositories/stockLedgerRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { paiseToRupee } = require("@/utils/money");

const LEDGER_COLLECTION = "stockLedgers";
const INVENTORY_COLLECTION = "inventoryItems";
const { getInventoryItem } = require("@/repositories/itemRepo");
const { StockTransactionType, LedgerTypes } = require("@/defs/ledgerDefs");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");

/**
 * Adds a new entry to the StockLedger collection. Must be called with an active Firestore transaction.
 * @param {Object} entry - The entry to add. Must have the following properties:
 *   - referenceId: The ID of the item or order that this entry is for.
 *   - referenceType: "Item" or "Order".
 *   - quantity: The quantity of the item.
 *   - unitPrice: The unit price of the item.
 *   - totalPrice: The total price of the item (quantity * unitPrice).
 *   - type: "Credit" or "Debit".
 * @param {FirebaseFirestore.Transaction} t - The active transaction.
 * @returns {string} The ID of the newly added entry.
 * @throws {Error} If no transaction is provided.
 */
exports.addEntry = async function (entry, t) {
  if (!t) throw new Error("Transaction is required for addLedgerEntry");

  const ref = db.collection(LEDGER_COLLECTION).doc();
  const newLedger = {
    ...entry,
    id: ref.id,
    createdAt: FD.now(),
    updatedAt: FD.now(),
  };
  t.set(ref, newLedger);
  return newLedger;
};

/**
 * Updates the remaining quantity of a ledger entry.
 * @param {string} id - The Firestore document ID of the ledger entry to update.
 * @param {number} remainingQty - The new remaining quantity of the ledger entry.
 * @returns {Promise<void>} Resolves when the update is complete.
 */
exports.updateRemaining = async function (
  id,
  remainingQty,
  remainingQtyInRecipeUOM,
  t
) {
  if (!t) throw new Error("Transaction is required for updateRemaining");

  const ref = db.collection(LEDGER_COLLECTION).doc(id);
  t.update(ref, {
    remainingQty,
    remainingQtyInRecipeUOM,
    updatedAt: FD.now(),
  });
};

/**
 * Fetches available stock batches for a given item and inventory location.
 *
 * The batches are sorted using FEFO/FIFO logic:
 * - FEFO (First Expiry, First Out): batches with the earliest expiry date come first.
 * - FIFO fallback: within the same expiry date (or if expiry is not set), older batches (by createdAt) come first.
 *
 * @param {string} itemId - The unique identifier of the item.
 * @param {string} inventoryLocationId - The inventory location to fetch stock from.
 * @param {FirebaseFirestore.Transaction} [t] - Optional Firestore transaction object.
 * @returns {Promise<Array<Object>>} Array of batch objects, each containing the Firestore document ID as `id` and batch data.
 */

exports.getAvailableBatches = async function (
  itemId,
  inventoryLocationId,
  pkgId,
  t
) {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("itemId", "==", itemId)
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("remainingQty", ">", 0)
    .orderBy("remainingQty")
    .orderBy("expiryDate")   // FEFO
    .orderBy("createdAt");   // FIFO fallback

  if (pkgId) {
    query = query.where("pkg.id", "==", pkgId);
  }

  const snapshot = t
    ? await query.get({ transaction: t })
    : await query.get();

  return snapshot.docs.map((d) => ({ id: d.id, ...d.data() }));
};

/**
 * Get the most recent stock-IN record for price reference
 * Falls back to inventory item pricing with unit conversion if no ledger records exist
 * Returns cost as 0 if all attempts fail
 * INCLUDES TAX in all calculations
 */
exports.getLatestPriceReference = async function (
  itemId,
  inventoryLocationId,
  t
) {
  // Static unit conversion factors (symbol-based)
  const UNIT_CONVERSIONS = {
    'ml': 1,
    'l': 1000,
    'g': 1,
    'kg': 1000,
    'nos': 1
  };

  // Helper to get unit type from symbol
  const getUnitType = (symbol) => {
    if (['ml', 'l'].includes(symbol)) return 'volume';
    if (['g', 'kg'].includes(symbol)) return 'weight';
    if (['nos'].includes(symbol)) return 'count';
    return null;
  };

  // Priority 1: Try to get latest IN record from ledger
  try {
    const query = db
      .collection(LEDGER_COLLECTION)
      .where("itemId", "==", itemId)
      .where("inventoryLocationId", "==", inventoryLocationId)
      .where("transactionType", "==", "IN")
      .orderBy("createdAt", "desc")
      .limit(1);

    const snapshot = t
      ? await query.get({ transaction: t })
      : await query.get();

    if (!snapshot.empty) {
      const ledgerData = snapshot.docs[0].data();
      
      const totalCost = ledgerData.totalCost;  // Already includes tax
      const recipeQty = ledgerData.recipeQty;
      
      if (recipeQty && recipeQty > 0) {
        const unitCostPerRecipeUnit = totalCost / recipeQty;
        
        console.log(`   Using ledger price: ${totalCost} for ${recipeQty} ${ledgerData.recipeUOM}`);
        console.log(`   Cost per ${ledgerData.recipeUOM}: ${unitCostPerRecipeUnit.toFixed(4)} (incl. tax)`);
        
        return {
          id: snapshot.docs[0].id,
          source: "ledger",
          unitCost: unitCostPerRecipeUnit,  // Already includes tax
          itemName: ledgerData.itemName,
          itemCode: ledgerData.itemCode,
          recipeUOM: ledgerData.recipeUOM,
          recipeQty: recipeQty,
          totalCost: totalCost,
          createdAt: ledgerData.createdAt
        };
      } else {
        console.warn(`⚠️  Invalid recipeQty in ledger record: ${ledgerData.id}, falling back to inventory`);
      }
    }
  } catch (ledgerError) {
    console.error(`⚠️  Error fetching ledger data: ${ledgerError.message}, falling back to inventory`);
  }

  // Priority 2: Fall back to inventory item's pricing
  try {
    console.log(`⚠️  No valid ledger records found for item ${itemId}, falling back to inventory item pricing`);
    
    const inventoryItem = await db
      .collection(INVENTORY_COLLECTION)
      .doc(itemId)
      .get();

    if (!inventoryItem.exists) {
      throw new Error(`Item ${itemId} not found in inventory`);
    }

    const data = inventoryItem.data();
    
    if (!data.unitCost) {
      throw new Error(`No unitCost for item ${itemId}`);
    }

    const purchaseUnit = data.purchaseUnit;
    const recipeUnit = data.recipeUnit;

    if (!purchaseUnit || !recipeUnit) {
      throw new Error(`Missing unit data for item ${itemId}`);
    }

    // Get symbols
    const purchaseSymbol = purchaseUnit.symbol;
    const recipeSymbol = recipeUnit.symbol;

    if (!purchaseSymbol || !recipeSymbol) {
      throw new Error(`Missing unit symbols for item ${itemId}`);
    }

    // Get conversion factors using SYMBOL (not toUnit)
    const purchaseUnitFactor = UNIT_CONVERSIONS[purchaseSymbol];
    const recipeUnitFactor = UNIT_CONVERSIONS[recipeSymbol];

    if (purchaseUnitFactor === undefined || recipeUnitFactor === undefined) {
      throw new Error(`Unknown unit symbols: ${purchaseSymbol} or ${recipeSymbol}`);
    }

    // Check if units are compatible by checking their type
    const purchaseType = getUnitType(purchaseSymbol);
    const recipeType = getUnitType(recipeSymbol);

    if (purchaseType !== recipeType) {
      throw new Error(`Incompatible unit types: ${purchaseSymbol} (${purchaseType}) cannot convert to ${recipeSymbol} (${recipeType})`);
    }

    // Include tax in unit cost
    const unitCostInPaise = data.unitCost;
    const unitTax = data.unitTax || 0;
    const unitCostWithTax = unitCostInPaise + unitTax;

    // Calculate conversion factor
    const conversionFactor = purchaseUnitFactor / recipeUnitFactor;
    const unitCostPerRecipeUnit = unitCostWithTax / conversionFactor;

    console.log(`   Converting inventory price:`);
    console.log(`   ${unitCostInPaise} paise per ${purchaseSymbol} (base cost)`);
    console.log(`   + ${unitTax} paise tax`);
    console.log(`   = ${unitCostWithTax} paise per ${purchaseSymbol} (incl. tax)`);
    console.log(`   → ${unitCostPerRecipeUnit.toFixed(4)} paise per ${recipeSymbol} (factor: ${conversionFactor})`);

    return {
      id: inventoryItem.id,
      source: "inventory",
      unitCost: unitCostPerRecipeUnit,  // Includes tax
      itemName: data.itemName,
      itemCode: data.itemCode,
      recipeUOM: recipeUnit.symbol,
      createdAt: data.createdAt || null
    };

  } catch (inventoryError) {
    console.error(`❌ Error fetching inventory data: ${inventoryError.message}`);
    console.error(`⚠️  Returning cost as 0 for item ${itemId}`);
    
    // Priority 3: Return zero cost as fallback
    return {
      id: itemId,
      source: "fallback",
      unitCost: 0,
      itemName: "Unknown",
      itemCode: "Unknown",
      recipeUOM: "unit",
      error: `Failed to get price reference: ${inventoryError.message}`,
      createdAt: null
    };
  }
};

/**
 * Retrieves ledger entries linked to the given GRN ID and tenant ID.
 * - Queries ledger collection with the given tenant ID and GRN ID.
 * - Returns an array of ledger entries with their IDs and data.
 * @param {string} tenantId - Tenant ID to filter ledger entries by.
 * @param {string} grnId - Unique identifier of the GRN to retrieve ledger entries for.
 * @returns {Promise<Array<object>>} - Array of ledger entries with their IDs and data.
 */
exports.getLedgersByGRNId = async function (grnId, tenantId) {
  const snapshot = await db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("grnMeta.id", "==", grnId)
    .get();

  return snapshot.docs.map((d) => ({ id: d.id, ...d.data() }));
};

// exports.getStockLedgers = async (tenantId, filters = {}) => {
//   if (!tenantId) {
//     throw new Error("tenantId is required");
//   }

//   let query = db
//     .collection(LEDGER_COLLECTION)
//     .where("tenantId", "==", tenantId);

//   if (filters.locations?.length) {
//     query = query.where("locationId", "in", filters.locations);
//   }
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocationId",
//       "in",
//       filters.inventoryLocations
//     );
//   }
//   if (filters.categories?.length) {
//     query = query.where("categoryId", "in", filters.categories);
//   }
//   if (filters.subCategories?.length) {
//     query = query.where("subcategoryId", "in", filters.subCategories);
//   }
//   if (filters.inventoryItems?.length) {
//     query = query.where("itemId", "in", filters.inventoryItems);
//   }

//   const snapshot = await query.orderBy("updatedAt", "desc").get();
//   if (snapshot.empty) return [];

//   return snapshot.docs.map((doc) => ({
//     id: doc.id,
//     ...doc.data(),
//   }));
// };

exports.getStockLedgers = async (tenantId, filters = {}) => {
  if (!tenantId) {
    throw new Error("tenantId is required");
  }

  const raw = await fetchAllStockLedgers(tenantId, filters);
  return applyStockLedgerFilters(raw, filters);
};

async function fetchAllStockLedgers(tenantId, filters = {}) {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)

  if (filters.fromDate) {
    query = query.where(
      "createdAt",
      ">=",
      FD.toFirestore(filters.fromDate, TIME_OPTION.START)
    );
  }

  if (filters.toDate) {
    query = query.where(
      "createdAt",
      "<=",
      FD.toFirestore(filters.toDate, TIME_OPTION.END)
    );
  }

// orderBy
  query = query.orderBy("createdAt", "desc");
  const snapshot = await query.get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
}

function applyStockLedgerFilters(ledgers, filters = {}) {
  const {
    locations = [],
    inventoryLocations = [],
    categories = [],
    subCategories = [],
    inventoryItems = [],
  } = filters;


  return ledgers.filter((l) => {
    if (locations.length && !locations.includes(l.locationId)) return false;
    if (
      inventoryLocations.length &&
      !inventoryLocations.includes(l.inventoryLocationId)
    )
      return false;
    if (categories.length && !categories.includes(l.categoryId)) return false;
    if (subCategories.length && !subCategories.includes(l.subcategoryId))
      return false;
    if (inventoryItems.length && !inventoryItems.includes(l.itemId))
      return false;
    return true;
  });
}

exports.findGrnItemPricesBetween = async ({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) => {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("ledgerType", "==", "GRN")
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("itemId", "==", itemId);

  if (pkgId) {
    query = query.where("pkg.id", "==", pkgId);
  }

  const snapshot = await query.orderBy("createdAt", "desc").limit(6).get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => {
    const data = doc.data();
    return {
      grnId: data.grnMeta.id,
      grnNumber: data.grnMeta.grnNumber,
      date: FD.toFormattedDate(data.createdAt, DATE_FORMAT.DATE_ONLY),
      vendorId: data.grnMeta.vendorId,
      vendorName: data.grnMeta.vendorName,
      unitCost: paiseToRupee(data.unitCost),
      quantity: data.qty,
      pkg: data.pkg || "default",
      taxRate: data.taxRate || 0,
      UOM:
        pkgId && pkgId !== "default" ? data.pkg.packageCode : data.countingUOM,
      discount: data.discount ? paiseToRupee(data.discount) : 0,
    };
  });
};

exports.findLastGrnItemPrice = async ({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) => {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("ledgerType", "==", "GRN")
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("itemId", "==", itemId);

  if (pkgId) {
    query = query.where("pkg.id", "==", pkgId);
  }

  const snapshot = await query.orderBy("createdAt", "desc").limit(1).get();

  // If no GRN found → fetch master item and fallback
  if (snapshot.empty) {
    const item = await getInventoryItem(itemId);

    let pkgInfo = null;
    if (pkgId && pkgId !== "default") {
      pkgInfo = item.packages?.find((p) => p.id === pkgId) || null;
    }
    return {
      grnId: null,
      grnNumber: null,
      date: null,
      unitCost: pkgInfo ? pkgInfo.unitCost : item.unitCost,
      quantity: 0,
      pkg: "default",
      UOM: item.countingUOM,
    };
  }

  // GRN exists → return ledger data
  const data = snapshot.docs[0].data();
  return {
    grnId: data.grnMeta.id,
    grnNumber: data.grnMeta.grnNumber,
    date: FD.toFormattedDate(data.createdAt, DATE_FORMAT.DATE_ONLY),
    unitCost: paiseToRupee(data.unitCost),
    quantity: data.qty,
    pkg: data.pkg.name,
    UOM: pkgId && pkgId !== "default" ? data.pkg.packageCode : data.countingUOM,
  };
};

exports.calculateFifoDispatchCost = async (inventoryLocationId, items = []) => {
  if (!inventoryLocationId || !items.length) {
    throw new Error("inventoryLocationId and items are required");
  }

  const results = [];

  for (const { itemId, pkgId, requestedQuantity = 0 } of items) {
    if (!itemId || !pkgId) {
      throw new Error("itemId and pkgId are required for each entry");
    }

    // Fetch all inbound batches FIFO (oldest first)
    const ledgerSnap = await db
      .collection(LEDGER_COLLECTION)
      .where("inventoryLocationId", "==", inventoryLocationId)
      .where("itemId", "==", itemId)
      .where("pkg.id", "==", pkgId)
      .where("transactionType", "==", StockTransactionType.IN)
      .orderBy("createdAt", "asc")
      .get();

    if (ledgerSnap.empty) {
      results.push({
        itemId,
        pkgId,
        requestedQuantity,
        currentPrice: 0,
        batches: [],
      });
      continue;
    }

    const batches = [];

    ledgerSnap.forEach((doc) => {
      const data = doc.data();

      // Fallback for old ledgers
      const qty =
        data.remainingQty !== undefined ? data.remainingQty : data.pkgQty ?? 0;

      const price = data.unitCost ?? 0;
      const taxRate = data.taxRate ?? 0; // e.g. "8" → 8%

      const priceWithTax = price + price * (taxRate / 100);

      batches.push({
        batchId: doc.id,
        qty,
        priceWithTax,
        createdAt: data.createdAt,
      });
    });

    let fifoPrice = 0;

    // -------- FIFO + Partial WAC Logic WITH TAX --------

    if (requestedQuantity > 0) {
      // 1. Check if first batch can satisfy full request
      const firstBatch = batches[0];
      const fullMatch = firstBatch.qty >= requestedQuantity;

      if (fullMatch) {
        fifoPrice = firstBatch.priceWithTax;
      } else {
        // 2. Need to pull from multiple batches (FIFO Weighted Avg)
        let qtyNeeded = requestedQuantity;
        let totalUsedQty = 0;
        let totalUsedCost = 0;

        for (const batch of batches) {
          if (qtyNeeded <= 0) break;

          const useQty = Math.min(batch.qty, qtyNeeded);

          totalUsedQty += useQty;
          totalUsedCost += useQty * batch.priceWithTax;

          qtyNeeded -= useQty;
        }

        fifoPrice = totalUsedQty > 0 ? totalUsedCost / totalUsedQty : 0;
      }
    } else {
      // Default FIFO if no requested quantity
      const firstAvailable = batches.find((b) => b.qty > 0);
      fifoPrice = firstAvailable ? firstAvailable.priceWithTax : 0;
    }

    results.push({
      itemId,
      pkgId,
      requestedQuantity,
      currentPrice: paiseToRupee(fifoPrice),
      batches,
    });
  }

  return results;
};

exports.getTransferInLedgersForReport = async function ({
  tenantId,
  dateField,
  fromDate,
  toDate,
  locationIds = [],
}) {
  const query = new QueryBuilder(COLLECTIONS.LEDGERS)
    .whereMust("tenantId", "==", tenantId)
    .whereMust("ledgerType", "==", LedgerTypes.TRANSFER_IN)
    .whereIf(dateField, ">=", fromDate)
    .whereIf(dateField, "<=", toDate)
    .whereIf("locationId", "in", locationIds);

  const snap = await query.query.get();
  return snap.docs.map((d) => ({ id: d.id, ...d.data() }));
};
