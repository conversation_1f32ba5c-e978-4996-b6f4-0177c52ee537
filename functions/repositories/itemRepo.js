// repositories/categoryRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { paiseToRupee } = require("@/utils/money");

const ITEM_COLLECTION = db.collection(COLLECTIONS.INVENTORY_ITEMS);

/**
 * Get a item by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} item data or null if not found / tenant mismatch
 */
async function getItemById(tenantId, docId) {
    const docRef = ITEM_COLLECTION.doc(docId);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const data = snapshot.data();
    if (data.tenantId !== tenantId) return null; // tenant validation

    return { id: snapshot.id, ...data };
}

/**
 * Get inventory item by ID (without tenant validation)
 * Used internally by repositories
 * @param {string} id - Document ID
 * @returns {object|null} item data with converted prices or null if not found
 */
async function getInventoryItem(id) {
    const docRef = ITEM_COLLECTION.doc(id);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const data = snapshot.data();
    data.packages = data.packages.map((pkg) => ({
        ...pkg,
        unitCost: paiseToRupee(pkg.unitCost),
    }));

    return {
        ...data,
        unitCost: paiseToRupee(data.unitCost),
        unitTax: paiseToRupee(data.unitTax),
    };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateItemStatus(tenantId, docId, activeStatus) {
    const docRef = ITEM_COLLECTION.doc(docId);

    await db.runTransaction(async (t) => {
        const snapshot = await t.get(docRef);
        if (!snapshot.exists) throw new Error("Item not found");

        const data = snapshot.data();
        if (data.tenantId !== tenantId) throw new Error("Unauthorized");

        t.update(docRef, { activeStatus });
    });
}

module.exports = {
    getItemById,
    updateItemStatus,
    getInventoryItem,
};
