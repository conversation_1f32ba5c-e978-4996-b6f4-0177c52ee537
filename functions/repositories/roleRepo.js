// repositories/vendorRepo.js
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();

const ROLE_COLLECTION = db.collection(COLLECTIONS.ROLE);

/**
 * Get a role by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} Role data or null if not found / tenant mismatch
 */
async function getRoleById(tenantId, docId) {
    const docRef = ROLE_COLLECTION.doc(docId);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const data = snapshot.data();
    if (data.tenantId !== tenantId) return null; // tenant validation

    return { id: snapshot.id, ...data };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateRoleStatus(tenantId, docId, activeStatus) {
    const docRef = ROLE_COLLECTION.doc(docId);

    await db.runTransaction(async (t) => {
        const snapshot = await t.get(docRef);
        if (!snapshot.exists) throw new Error("Role not found");

        const data = snapshot.data();
        if (data.tenantId !== tenantId) throw new Error("Unauthorized");

        t.update(docRef, { activeStatus });
    });
}

module.exports = {
    getRoleById,
    updateRoleStatus,
};
