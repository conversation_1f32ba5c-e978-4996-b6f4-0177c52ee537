const admin = require("firebase-admin");
const db = admin.firestore();

const COUNTERS_COLLECTION = "counters"; // collection name kept local to repo

/**
 * Atomically increments and returns the updated counter doc
 * @param {string} tenantId
 * @param {string} key
 * @param {string} prefix
 * @returns {Promise<{ tenantId: string, key: string, prefix: string, nextNumber: number }>}
 */
const getNextNumber = async (tenantId, key, prefix) => {
  const docId = `${tenantId}_${key}`;
  const docRef = db.collection(COUNTERS_COLLECTION).doc(docId);

  const updatedDoc = await db.runTransaction(async (tx) => {
    const doc = await tx.get(docRef);

    if (!doc.exists) {
      // Initialize counter if not exists
      const newData = { tenantId, key, prefix, nextNumber: 1 };
      tx.set(docRef, newData);
      return newData;
    }

    const data = doc.data();
    const nextNumber = (data.nextNumber || 0) + 1;

    tx.update(docRef, { nextNumber });
    return { ...data, nextNumber };
  });

  return updatedDoc;
};

const getNextNumberRange = async (tenantId, key, prefix, count) => {
  const docId = `${tenantId}_${key}`;
  const docRef = db.collection(COUNTERS_COLLECTION).doc(docId);

  const updatedDoc = await db.runTransaction(async (tx) => {
    const doc = await tx.get(docRef);

    if (!doc.exists) {
      const newData = {
        tenantId,
        key,
        prefix,
        nextNumber: count, // reserve [1..count]
      };
      tx.set(docRef, newData);
      return { ...newData, start: 1, end: count };
    }

    const data = doc.data();
    const start = (data.nextNumber || 0) + 1;
    const end = start + count - 1;

    tx.update(docRef, { nextNumber: end });
    return { ...data, start, end };
  });

  return {
    prefix: updatedDoc.prefix,
    start: updatedDoc.start,
    end: updatedDoc.end,
  };
};

module.exports = {
  getNextNumber,
  getNextNumberRange,
};
