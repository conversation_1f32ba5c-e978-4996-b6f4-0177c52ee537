// repos/locationRepo.js

const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const LOCATION_COLLECTION = db.collection(COLLECTIONS.LOCATIONS);
const INVENTORY_LOCATION_COLLECTION = db.collection(COLLECTIONS.WORK_AREAS);

/**
 * Get Location and  Inventory Location by IDs
 */

// pass single arg(locationId) to get location details and by locationId and workArea to get workArea details
const getLocationById = async (tenantId, location, workArea = "") => {
  let query;

  if (!workArea) {
    query = LOCATION_COLLECTION.where("tenantId", "==", tenantId)
      .where("id", "==", location)
      .limit(1);
  } else {
    query = INVENTORY_LOCATION_COLLECTION.where("tenantId", "==", tenantId)
      .where("locationId", "==", location)
      .where("id", "==", workArea)
      .limit(1);
  }

  const locationSnap = await query.get();

  if (locationSnap.empty) return null;

  return locationSnap.docs[0].data();
};

const getDefaultWorkAreaByLocationId = async (tenantId, locationId) => {
  let query = INVENTORY_LOCATION_COLLECTION.where("tenantId", "==", tenantId)
    .where("locationId", "==", locationId)
    .where("isDefault", "==", true)
    .limit(1);

  const workAreaSnap = await query.get();

  if (workAreaSnap.empty) return null;

  const workArea = workAreaSnap.docs[0].data();

  return { id: workArea.id, name: workArea.name };
};

const updateLocationStatus = async (tenantId, docId, activeStatus) => {
  const docRef = INVENTORY_LOCATION_COLLECTION.doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Location not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
  getLocationById,
  getDefaultWorkAreaByLocationId,
  updateLocationStatus
};
