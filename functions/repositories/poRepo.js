// repos/poRepo.js

const admin = require("firebase-admin");
const db = admin.firestore();

const PO_COLLECTION = "purchaseOrders";
const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { purchaseStatus, activeStatuses } = require("@/defs/purchaseStatusDefs");

/**
 * Get PO by ID
 */
const getPOById = async (poId, transaction = null) => {
  const ref = db.collection(PO_COLLECTION).doc(poId);
  const doc = transaction ? await transaction.get(ref) : await ref.get();
  return doc.exists ? { poId: doc.id, ...doc.data() } : null;
};

/**
 * Retrieves a Purchase Order (PO) document by its PO number.
 * @param {string} tenantId - The tenant ID associated with the PO.
 * @param {string} poNumber - The PO number.
 * @returns {Promise<object|null>} - The retrieved PO document, or null if not found.
 */
const getPOByNumber = async (tenantId, poNumber) => {
  let query = db
    .collection(PO_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("poNumber", "==", poNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
};

/**
 * Update PO document with new items and metadata
 */
const updatePO = async (
  poId,
  { rejectedReason = null, activeStatus = true, ...rest },
  transaction = null
) => {
  const ref = db.collection(PO_COLLECTION).doc(poId);
  const payload = {
    ...rest,
    lastUpdatedTime: FD.now(),
    activeStatus,
    rejectedReason,
  };

  if (transaction) {
    await transaction.update(ref, payload);
  } else {
    await ref.update(payload);
  }
};

const updatePurchaseOrderById = async (id, data) => {
  try {
    const docRef = db.collection(PO_COLLECTION).doc(id);
    await docRef.update(data);
  } catch (err) {
    throw Error(err.message);
  }
};

/**
 * Returns the count of active POs for a tenant
 * @param {string} tenantId
 * @returns {Promise<number>} count of active POs
 */
const getActivePOCount = async (tenantId) => {
  if (!tenantId) throw new Error("tenantId is required");
  const snapshot = await db
    .collection(PO_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("status", "in", activeStatuses)
    .count()
    .get();

  return snapshot.data()?.count || 0; // safe fallback
};

// const getPurchaseOrders = async (tenantId, filters) => {
//   const statuses = filters.status || [];
//   const hasPending = statuses.includes("pending");
//   const hasCompleted = statuses.includes("completed");

//   // -------------------------------
//   // CASE 1: Both pending & completed
//   // -------------------------------
//   if (hasPending && hasCompleted) {
//     const [pending, completed] = await Promise.all([
//       fetchPendingPurchaseOrders(tenantId, filters),
//       fetchCompletedPurchaseOrders(tenantId, filters),
//     ]);

//     // merge unique by id
//     const map = new Map();
//     [...pending, ...completed].forEach((t) => map.set(t.id, t));
//     return Array.from(map.values());
//   }

//   if (hasPending) {
//     return fetchPendingPurchaseOrders(tenantId, filters);
//   }

//   if (hasCompleted) {
//     return fetchCompletedPurchaseOrders(tenantId, filters);
//   }

//   return [];
// };

// const fetchPendingPurchaseOrders = async (tenantId, filters) => {
//   let query = db.collection(PO_COLLECTION).where("tenantId", "==", tenantId);

//   // status filter
//   if (filters.status?.length) {
//     query = query.where("status", "in", activeStatuses);
//   }

//   // locations filter
//   if (filters.locations?.length) {
//     query = query.where("location.id", "in", filters.locations);
//   }

//   // inventory locations filter
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocation.id",
//       "in",
//       filters.inventoryLocations
//     );
//   }

//   if (filters.vendors?.length) {
//     query = query.where("vendor.id", "in", filters.vendors);
//   }

//   const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
//   return snapshot.docs.map((doc) => doc.data());
// };

// const fetchCompletedPurchaseOrders = async (tenantId, filters) => {
//   let query = db.collection(PO_COLLECTION).where("tenantId", "==", tenantId);

//   // status filter
//   if (filters.status?.length) {
//     query = query.where("status", "in", [
//       purchaseStatus.REJECTED,
//       purchaseStatus.COMPLETED,
//       purchaseStatus.DELETED,
//       purchaseStatus.CLOSED,
//     ]);
//   }

//   // locations filter
//   if (filters.locations?.length) {
//     query = query.where("location.id", "in", filters.locations);
//   }

//   // inventory locations filter
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocation.id",
//       "in",
//       filters.inventoryLocations
//     );
//   }

//   if (filters.vendors?.length) {
//     query = query.where("vendor.id", "in", filters.vendors);
//   }

//   // date range filter (convert incoming filters to millis)
//   if (filters.fromDate) {
//     query = query.where(
//       "lastUpdatedTime",
//       ">=",
//       FD.toFirestore(filters.fromDate, TIME_OPTION.START)
//     );
//   }
//   if (filters.toDate) {
//     query = query.where(
//       "lastUpdatedTime",
//       "<=",
//       FD.toFirestore(filters.toDate, TIME_OPTION.END)
//     );
//   }

//   const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
//   return snapshot.docs.map((doc) => doc.data());
// };

const getPurchaseOrders = async (tenantId, filters = {}) => {
  const raw = await fetchAllPurchaseOrders(tenantId, filters);
  return applyPurchaseOrderFilters(raw, filters);
};

const fetchAllPurchaseOrders = async (tenantId, filters) => {
  let query = db.collection(PO_COLLECTION).where("tenantId", "==", tenantId);

  if (filters.fromDate) {
    query = query.where(
      "lastUpdatedTime",
      ">=",
      FD.toFirestore(filters.fromDate, TIME_OPTION.START)
    );
  }

  if (filters.toDate) {
    query = query.where(
      "lastUpdatedTime",
      "<=",
      FD.toFirestore(filters.toDate, TIME_OPTION.END)
    );
  }

  const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
  return snapshot.docs.map((d) => d.data());
};

const applyPurchaseOrderFilters = (items, filters = {}) => {
  const {
    status = [],
    locations = [],
    inventoryLocations = [],
    vendors = [],
  } = filters;

  const wantsPending = status.includes("pending");
  const wantsCompleted = status.includes("completed");

  return items.filter((item) => {
    // -----------------------
    // Status logic
    // -----------------------
    if (status.length) {
      const isPending = activeStatuses.includes(item.status);
      const isCompleted = [
        purchaseStatus.REJECTED,
        purchaseStatus.COMPLETED,
        purchaseStatus.DELETED,
        purchaseStatus.CLOSED,
      ].includes(item.status);

      if (wantsPending && !wantsCompleted && !isPending) return false;
      if (wantsCompleted && !wantsPending && !isCompleted) return false;
      if (!wantsPending && !wantsCompleted) return false;
    }

    // -----------------------
    // Location filter
    // -----------------------
    if (locations?.length && !locations.includes(item.location?.id)) {
      return false;
    }

    // -----------------------
    // Inventory location filter
    // -----------------------
    if (
      inventoryLocations?.length &&
      !inventoryLocations.includes(item.inventoryLocation?.id)
    ) {
      return false;
    }

    // -----------------------
    // Vendor filter
    // -----------------------
    if (vendors?.length && !vendors.includes(item.vendor?.id)) {
      return false;
    }

    return true;
  });
};

const savePurchaseOrder = async (purchaseOrders) => {
  const poDocs = [];
  for (const poData of purchaseOrders) {
    const docRef = db.collection(PO_COLLECTION).doc();
    poData.id = docRef.id;
    await docRef.set(poData);
    poDocs.push({ id: docRef.id, poNumber: poData.poNumber });
  }
  return poDocs;
};

module.exports = {
  getPOById,
  updatePO,
  getActivePOCount,
  getPOByNumber,
  getPurchaseOrders,
  savePurchaseOrder,
  updatePurchaseOrderById,
};
