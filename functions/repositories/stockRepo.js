// repositories/stockRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();
const {
  debitStockByRecipeQty: sharedDebitStockByRecipeQty,
} = require("@/services/stockOperations"); // Circular dependency

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { tenantDoc } = require("@/helpers/tenantFirestore");

const STOCK_COLLECTION = "stocks";
const ITEM_COLLECTION = "inventoryItems";

const getDocId = (inventoryLocationId, itemId, pkgId) =>
  `${inventoryLocationId}_${itemId}_${pkgId}`;

/**
 * Increases the stock of the given item in the given inventory location.
 *
 * If the item does not exist in the inventory location, it creates a new stock entry.
 * If the item already exists, it updates the existing stock entry.
 *
 * @param {Object} payload - { itemCode, inventoryLocationId, qty, cost, uom }
 * @param {firebase.firestore.Transaction} t - Firestore transaction object
 */
exports.increaseStock = async function (payload, t) {
  if (!t) throw new Error("Transaction is required for increaseStock");

  const {
    itemId,
    inventoryLocationId,
    pkgQty,
    totalCost,
    pkg: package,
  } = payload;
  const docId = getDocId(inventoryLocationId, itemId, package.id);
  const ref = db.collection(STOCK_COLLECTION).doc(docId);

  // @todo correct this
  const doc = await ref.get();

  const costData = {};
  if (payload.grnMeta && payload.itemPrice)
    costData.masterUnitCost = payload.itemPrice;
  if (payload.grnMeta && payload.unitCost) {
    costData.lastGRNUnitCost = payload.unitCost;
    costData.lastGRNUnitCostIncludingTax =
      payload.unitCost + payload.unitCost * (payload.taxRate / 100);
  }

  if (!doc.exists) {
    // First stock entry
    t.set(ref, {
      tenantId: payload.tenantId,
      groupKey: `${inventoryLocationId}_${itemId}`,
      locationId: payload.locationId,
      locationName: payload.locationName,
      inventoryLocationId: payload.inventoryLocationId,
      inventoryLocationName: payload.inventoryLocationName,
      categoryId: payload.categoryId,
      subcategoryId: payload.subcategoryId,
      categoryName: payload.categoryName,
      subcategoryName: payload.subcategoryName,
      itemId: payload.itemId,
      itemCode: payload.itemCode,
      itemName: payload.itemName,
      pkgUOM: payload.pkgUOM,
      purchaseUOM: payload.purchaseUOM,
      countingUOM: payload.countingUOM,
      recipeUOM: payload.recipeUOM,
      pkgQty: payload.pkgQty,
      recipeQty: payload.recipeQty,
      avgCost: totalCost / payload.pkgQty,
      totalValue: totalCost,
      parLevel: 0,
      createdAt: FD.now(),
      updatedAt: FD.now(),
      pkg: payload.pkg,
      remarks: payload.remarks,
      ...costData,
    });
    return;
  }

  const data = doc.data();
  const newQty = data.pkgQty + pkgQty;
  const newRecipeQty = data.recipeQty + payload.recipeQty;
  const newTotal = data.totalValue + totalCost;

  t.update(ref, {
    pkgQty: newQty,
    recipeQty: newRecipeQty,
    totalValue: newTotal,
    avgCost: newTotal / newQty,
    updatedAt: FD.now(),
    pkg: payload.pkg,
    ...costData,
  });
};

/**
 * Decreases the stock of the given item in the given inventory location.
 * If the item does not exist in the inventory location, it throws an error.
 * If the item already exists, it updates the existing stock entry.
 * Throws an error if the item does not have sufficient stock.
 *
 * @param {Object} payload - { itemCode, inventoryLocationId, qty }
 * @param {firebase.firestore.Transaction} t - Firestore transaction object
 */
exports.decreaseStock = async function (
  { itemId, inventoryLocationId, packageId, qty, recipeQty = 0, totalValue },
  t
) {
  if (!t) throw new Error("Transaction is required for decreaseStock");

  const docId = getDocId(inventoryLocationId, itemId, packageId);
  const ref = db.collection(STOCK_COLLECTION).doc(docId);

  // @todo correct this
  const doc = await ref.get();
  if (!doc.exists) throw new Error("Stock not found");

  const data = doc.data();
  if (data.pkgQty < qty) throw new Error("Insufficient stock");

  const newQty = data.pkgQty - qty;
  const newRecipeQty = recipeQty ? data.recipeQty - recipeQty : 0;
  const newTotal = data.totalValue - totalValue;
  const wac = newQty > 0 ? newTotal / newQty : 0;
  t.update(ref, {
    pkgQty: newQty,
    recipeQty: newRecipeQty,
    totalValue: newTotal,
    updatedAt: FD.now(),
    avgCost: wac,
  });
};

// /**
//  * Fetch stocks with tenant & dynamic filters
//  * @param {string} tenantId - Mandatory tenant identifier
//  * @param {Object} filters { locationId, inventoryLocationId, categoryId, subCategoryId, itemId }
//  * @returns {Promise<Array>}
//  */
// exports.getStocks = async (tenantId, filters = {}) => {
//   if (!tenantId) {
//     throw new Error("tenantId is required");
//   }

//   let query = db.collection(STOCK_COLLECTION).where("tenantId", "==", tenantId);

//   if (filters.locations?.length) {
//     query = query.where("locationId", "in", filters.locations);
//   }
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocationId",
//       "in",
//       filters.inventoryLocations
//     );
//   }
//   if (filters.categories?.length) {
//     query = query.where("categoryId", "in", filters.categories);
//   }
//   if (filters.subCategories?.length) {
//     query = query.where("subcategoryId", "in", filters.subCategories);
//   }
//   if (filters.inventoryItems?.length) {
//     query = query.where("itemId", "in", filters.inventoryItems);
//   }

//   const snapshot = await query.orderBy("updatedAt", "desc").get();
//   if (snapshot.empty) return [];

//   return snapshot.docs.map((doc) => ({
//     id: doc.id,
//     ...doc.data(),
//   }));
// };

exports.getStocks = async (tenantId, filters = {}) => {
  if (!tenantId) {
    throw new Error("tenantId is required");
  }

  const raw = await fetchAllStocks(tenantId);
  return applyStockFilters(raw, filters);
};

async function fetchAllStocks(tenantId) {
  let query = db
    .collection(STOCK_COLLECTION)
    .where("tenantId", "==", tenantId)
    .orderBy("updatedAt", "desc");

  const snapshot = await query.get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
}

function applyStockFilters(stocks, filters = {}) {
  const {
    locations = [],
    inventoryLocations = [],
    categories = [],
    subCategories = [],
    inventoryItems = [],
  } = filters;

  return stocks.filter((s) => {
    if (locations.length && !locations.includes(s.locationId)) return false;
    if (
      inventoryLocations.length &&
      !inventoryLocations.includes(s.inventoryLocationId)
    )
      return false;
    if (categories.length && !categories.includes(s.categoryId)) return false;
    if (subCategories.length && !subCategories.includes(s.subcategoryId))
      return false;
    if (inventoryItems.length && !inventoryItems.includes(s.itemId))
      return false;

    return true;
  });
}

/**
 * Fetch stocks with tenant & dynamic filters
 * @param {string} inventoryLocationId - Mandatory inventory location identifier
 * @param {Array} items - Array of item objects with at least `itemId` property
 * @returns {Promise<Array>}
 */

exports.getInventoryItemStocks = async (
  inventoryLocationId,
  items = [],
  itemId = "itemId",
  pkgId = "pkgId"
) => {
  if (!inventoryLocationId || !items.length) {
    throw new Error("inventoryLocationId and items are required");
  }

  const requiredStockIds = [];
  for (const item of items) {
    if (!item.itemId) {
      throw new Error("itemId is required for all items");
    }
    const stockId = getDocId(inventoryLocationId, item[itemId], item[pkgId]);
    item.stockId = stockId;
    requiredStockIds.push(stockId);
  }

  // Fetch stock documents in parallel
  const stockPromises = requiredStockIds.map((stockId) =>
    db.collection(STOCK_COLLECTION).doc(stockId).get()
  );

  const stockDocs = await Promise.all(stockPromises);

  // Build stock map
  const stockMap = new Map();
  for (const doc of stockDocs) {
    if (doc.exists) {
      const data = doc.data();
      stockMap.set(`${data.itemId}_${data.pkg.id}`, {
        pkgQty: data.pkgQty,
        lastGrnPrice: data.lastGRNUnitCost,
        lastGrnPriceIncludingTax: data.lastGRNUnitCostIncludingTax,
        recipeQty: data.recipeQty,
        recipeUOM: data.recipeUOM,
      });
    }
  }

  // Calculate prices in parallel for items that need it
  const priceCalculations = items.map((item) => {
    const result = stockMap.get(`${item[itemId]}_${item[pkgId]}`);
    // Only calculate if lastGrnPriceIncludingTax is missing
    if (result?.lastGrnPriceIncludingTax == null) {
      return calculateCostAgainstPkg(item[itemId], item[pkgId]);
    }
    return Promise.resolve(null); // Already has price
  });

  const calculatedPrices = await Promise.all(priceCalculations);

  // Enrich items with stock and price data
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const result = stockMap.get(`${item[itemId]}_${item[pkgId]}`);

    item.inStock = result?.pkgQty ?? 0;
    item.recipeQtyInStock = result?.recipeQty ?? 0;
    item.lastGrnPrice = result?.lastGrnPrice ?? 0;
    item.lastGrnPriceIncludingTax =
      result?.lastGrnPriceIncludingTax ?? calculatedPrices[i] ?? 0;
    item.unitCost =
      result?.lastGrnPriceIncludingTax ?? calculatedPrices[i] ?? 0;
    item.recipeUOM = item.recipeUOM ?? result?.recipeUOM;
  }

  return items;
};

exports.getMadeItemStock = async (inventoryLocationId, madeItemId) => {
  if (!inventoryLocationId || !madeItemId) {
    throw new Error("inventoryLocationId and madeItem.itemId are required");
  }

  const stockId = getDocId(inventoryLocationId, madeItemId, "default");

  const doc = await db.collection(STOCK_COLLECTION).doc(stockId).get();
  if (!doc.exists) {
    return {
      inStock: 0,
      recipeQtyInStock: 0,
    };
  }

  const data = doc.data();
  return {
    inStock: data.pkgQty,
    recipeQtyInStock: data.recipeQty,
  };
};

exports.getBusinessDate = function getBusinessDate(eventDate) {
  return FD.toFormattedDate(eventDate, "DD-MMM-YYYY");
};

exports.getStockMovementItems = async (
  tenantId,
  locationId,
  inventoryLocationId,
  eventDate,
  items
) => {
  if (!tenantId || !locationId || !inventoryLocationId || !eventDate) {
    throw new Error("Missing required parameters");
  }

  if (!Array.isArray(items) || items.length === 0) {
    return items;
  }

  try {
    const itemsSnapshot = await tenantDoc(tenantId)
      .collection("locations")
      .doc(locationId)
      .collection("workAreas")
      .doc(inventoryLocationId)
      .collection("stockMovements")
      .doc(eventDate)
      .collection("items")
      .get();

    // Build lookup map for O(1) access
    const smItemsMap = new Map();
    itemsSnapshot.docs.forEach((doc) => {
      smItemsMap.set(doc.id, doc.data());
    });

    // Process all items
    await Promise.all(
      items.map(async (item) => {
        const stockKey = `${item.itemId}_${item.pkg.id}`;
        const smItem = smItemsMap.get(stockKey);

        if (smItem && smItem.finalClosing) {
          const { qty, totalValue, recipeQty } = smItem.finalClosing;
          item.inStock = qty ?? 0;
          item.recipeQtyInStock = recipeQty ?? 0;
          // ✅ Use master data as fallback when qty is 0
          item.unitCost =
            qty > 0
              ? totalValue / qty
              : (await calculateCostAgainstPkg(item.itemId, item.pkg.id)) ?? 0; // @todo replace with last GRN price function
        } else {
          // No stock movement found
          item.inStock = 0;
          item.recipeQtyInStock = 0;
          item.unitCost =
            (await calculateCostAgainstPkg(item.itemId, item.pkg.id)) ?? 0;
        }
      })
    );

    return items;
  } catch (error) {
    console.error("Error fetching stock movement items:", error);
    throw error;
  }
};

async function calculateCostAgainstPkg(itemId, pkgId) {
  try {
    const inventoryItem = await db
      .collection(ITEM_COLLECTION)
      .doc(itemId)
      .get();

    if (!inventoryItem.exists) {
      console.warn(`Item not found: ${itemId}`);
      return 0;
    }

    const data = inventoryItem.data();
    let unitCost = 0;

    const taxRate =
      data.taxes?.reduce((acc, tax) => acc + (tax.valuePercentage || 0), 0) ||
      0;

    if (pkgId === "default") {
      unitCost = data.unitCost || 0;
    } else {
      const pkg = data.packages?.find((p) => p.id === pkgId);
      if (!pkg) {
        return 0;
      }
      unitCost = pkg.unitCost || 0;
    }
    return unitCost * (1 + taxRate / 100);
  } catch (error) {
    console.error(`Error calculating cost for item ${itemId}:`, error);
    return 0;
  }
}

const chunkArray = (array, size) => {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

exports.calculateAggregateQty = async (inventoryLocationId, items = []) => {
  if (!inventoryLocationId || !items.length) {
    throw new Error("inventoryLocationId and items are required");
  }

  const itemIds = items.map((item) => item.itemId).filter(Boolean);

  if (!itemIds.length) {
    return items.map((item) => ({ ...item, inStock: 0 }));
  }

  // Batch queries (10 items per query - Firestore limit)
  const chunks = chunkArray(itemIds, 10);

  const snapshots = await Promise.all(
    chunks.map((chunk) =>
      db
        .collection(STOCK_COLLECTION)
        .where("inventoryLocationId", "==", inventoryLocationId)
        .where("itemId", "in", chunk)
        .get()
    )
  );

  // Build stock map from all results
  const stockMap = snapshots.reduce((map, snapshot) => {
    snapshot.docs.forEach((doc) => {
      const { itemId, recipeQty = 0 } = doc.data();
      map[itemId] = (map[itemId] || 0) + recipeQty;
    });
    return map;
  }, {});

  // Return items with stock quantities
  return items.map((item) => ({
    ...item,
    inStock: stockMap[item.itemId] || 0,
  }));
};

exports.debitStockByRecipeQty = async function (payload, t) {
  // Import debitStock here to avoid circular dependency
  const { debitStock } = require("@/services/stockService");

  return await sharedDebitStockByRecipeQty(
    payload,
    t,
    exports.calculateAggregateQty,
    debitStock
  );
};
