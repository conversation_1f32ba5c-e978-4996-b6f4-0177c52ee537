const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const admin = require("firebase-admin");
const db = admin.firestore();

const CONTRACT_COLLECTION = "contracts";
const contractCollection = db.collection(CONTRACT_COLLECTION);

const getVendorContracts = async (tenantId, vendorId) => {
  const snapshot = await db
    .collection(CONTRACT_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("vendor.id", "==", vendorId)
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => doc.data());
};

const saveContract = async (data) => {
  const ref = db.collection(CONTRACT_COLLECTION).doc();
  await ref.set({ ...data, id: ref.id });
  return { id: ref.id, message: "Contract created successfully" };
};

const getAllContracts = async (tenantId) => {
  const contracts = await db
    .collection(CONTRACT_COLLECTION)
    .where("tenantId", "==", tenantId)
    .get();
  return contracts.docs.map((doc) => ({ ...doc.data() }));
};

const getById = async (id) => {
  const ref = db.collection(CONTRACT_COLLECTION).doc(id);
  const doc = await ref.get();
  return doc.exists ? { ...doc.data() } : null;
};

const updateById = async (id, data) => {
  const ref = db.collection(CONTRACT_COLLECTION).doc(id);
  const result = await ref.update(data);
  return result;
};

const updateContractStatus = async (tenantId, docId, activeStatus) => {
  const docRef = db.collection(CONTRACT_COLLECTION).doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Contract not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
};

const findActiveContracts = async (tenantId, vendorId, idle = true) => {
  const today = FD.now(TIME_OPTION.START);
  let q = db
    .collection(CONTRACT_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("vendor.id", "==", vendorId)
    .where("activeStatus", "==", true)
    .where("endDate", ">=", today);

  //  idle: include not yet started contracts
  if (idle) {
    q = q.where("startDate", "<=", today);
  }

  const snapshot = await q.get();
  return snapshot.docs.map((doc) => doc.data());
};

/**
 * Retrieves a Contract details (CT) document by its CT number.
 * @param {string} tenantId - The tenant ID associated with the CT.
 * @param {string} ctNumber - The CT number.
 * @returns {Promise<object|null>} - The retrieved CT document, or null if not found.
 */
const getContractByNumber = async (tenantId, ctNumber) => {
  let query = db
    .collection(CONTRACT_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("contractNumber", "==", ctNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
};

const updateAttachments = async (id, attachments) => {
  const ref = db.collection(CONTRACT_COLLECTION).doc(id);
  const doc = await ref.get();

  if (!doc.exists) {
    throw new Error("Contract not found");
  }

  const existingAttachments = doc.data().attachments || [];

  const updatedAttachments = [
    ...existingAttachments.filter(
      (f) =>
        !attachments.some(
          (s) => s.filePath === f.filePath || s.fileName === f.fileName
        )
    ),
    ...attachments,
  ];

  await ref.update({ attachments: updatedAttachments });

  return updatedAttachments;
};

const deleteAttachment = async (id, filePath) => {
  const ref = db.collection(CONTRACT_COLLECTION).doc(id);
  const doc = await ref.get();

  if (!doc.exists) throw new Error("Contract not found");

  const attachments = doc.data().attachments || [];
  const updatedAttachments = attachments.filter((f) => f.filePath !== filePath);

  await ref.update({ attachments: updatedAttachments });

  return updatedAttachments;
};

module.exports = {
  saveContract,
  getAllContracts,
  getById,
  updateById,
  updateContractStatus,
  findActiveContracts,
  contractCollection,
  getVendorContracts,
  getContractByNumber,
  updateAttachments,
  deleteAttachment,
};
