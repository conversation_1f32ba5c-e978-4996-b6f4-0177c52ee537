// repositories/chargeRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const CHARGE_COLLECTION = db.collection(COLLECTIONS.CHARGES);

/**
 * Create a new charge
 * @param {Object} data - Charge data (include tenantId)
 * @returns {Object} Created charge with ID
 */
async function createCharge(data) {
  const docRef = await CHARGE_COLLECTION.add({
    ...data
  });
  return { id: docRef.id, ...data };
}

/**
 * Update a charge by ID (full update)
 * @param {string} chargeId
 * @param {Object} data - Fields to update
 * @returns {Object} Updated charge
 */
async function updateCharge(chargeId, data) {
  await CHARGE_COLLECTION.doc(chargeId).update({
    ...data,
  });
  return { id: chargeId, ...data };
}

/**
 * Get a charge by doc ID and validate tenant
 * @param {string} tenantId
 * @param {string} chargeId
 * @returns {Object|null} Charge data or null if not found / tenant mismatch
 */
async function getChargeById(tenantId, chargeId) {
  const docRef = CHARGE_COLLECTION.doc(chargeId);
  const snapshot = await docRef.get();

  if (!snapshot.exists) return null;

  const data = snapshot.data();
  if (data.tenantId !== tenantId) return null; // tenant validation

  return { id: snapshot.id, ...data };
}

/**
 * Get all charges for a tenant
 * @param {string} tenantId
 * @returns {Array} List of charges
 */
async function getCharges(tenantId) {
  const snapshot = await CHARGE_COLLECTION.where("tenantId", "==", tenantId).get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating
 * @param {string} tenantId
 * @param {string} chargeId
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateChargeStatus(tenantId, chargeId, activeStatus) {
  const docRef = CHARGE_COLLECTION.doc(chargeId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Charge not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
  createCharge,
  updateCharge,
  getChargeById,
  getCharges,
  updateChargeStatus,
};

