// repositories/taxRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const TAX_COLLECTION = db.collection(COLLECTIONS.TAXES);

/**
 * Create a new tax
 * @param {Object} data - Tax data (include tenantId)
 * @returns {Object} Created tax with ID
 */
async function createTax(data) {
  const docRef = await TAX_COLLECTION.add({
    ...data,
    createdAt: FD.now(),
    updatedAt: FD.now(),
  });
  return { id: docRef.id, ...data };
}

/**
 * Update a tax by ID (full update)
 * @param {string} taxId
 * @param {Object} data - Fields to update
 * @returns {Object} Updated tax
 */
async function updateTax(taxId, data) {
  await TAX_COLLECTION.doc(taxId).update({
    ...data,
    updatedAt: FD.now(),
  });
  return { id: taxId, ...data };
}

/**
 * Get a tax by doc ID and validate tenant
 * @param {string} tenantId
 * @param {string} taxId
 * @returns {Object|null} Tax data or null if not found / tenant mismatch
 */
async function getTaxById(tenantId, taxId) {
  const docRef = TAX_COLLECTION.doc(taxId);
  const snapshot = await docRef.get();

  if (!snapshot.exists) return null;

  const data = snapshot.data();
  if (data.tenantId !== tenantId) return null; // tenant validation

  return { id: snapshot.id, ...data };
}

/**
 * Get all taxes for a tenant
 * @param {string} tenantId
 * @returns {Array} List of taxes
 */
async function getTaxes(tenantId) {
  const snapshot = await TAX_COLLECTION.where("tenantId", "==", tenantId).get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating
 * @param {string} tenantId
 * @param {string} taxId
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateTaxStatus(tenantId, taxId, activeStatus) {
  const docRef = TAX_COLLECTION.doc(taxId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Tax not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus, updatedAt: FD.now() });
  });
}

module.exports = {
  createTax,
  updateTax,
  getTaxById,
  getTaxes,
  updateTaxStatus,
};
