// repositories/vendorRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const VENDOR_COLLECTION = db.collection(COLLECTIONS.VENDORS);

/**
 * Get a vendor by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} Vendor data or null if not found / tenant mismatch
 */
async function getVendorById(tenantId, docId) {
  const docRef = VENDOR_COLLECTION.doc(docId);
  const snapshot = await docRef.get();

  if (!snapshot.exists) return null;

  const data = snapshot.data();
  if (data.tenantId !== tenantId) return null; // tenant validation

  return { id: snapshot.id, ...data };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateVendorStatus(tenantId, docId, activeStatus) {
  const docRef = VENDOR_COLLECTION.doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Vendor not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
  getVendorById,
  updateVendorStatus,
};
