// repositories/categoryRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const CATEGORY_COLLECTION = db.collection(COLLECTIONS.CATEGORIES);

/**
 * Get a category by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} category data or null if not found / tenant mismatch
 */
async function getCategoryById(tenantId, docId) {
    const docRef = CATEGORY_COLLECTION.doc(docId);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;

    const data = snapshot.data();
    if (data.tenantId !== tenantId) return null; // tenant validation

    return { id: snapshot.id, ...data };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateCategoryStatus(tenantId, docId, activeStatus) {
    const docRef = CATEGORY_COLLECTION.doc(docId);

    await db.runTransaction(async (t) => {
        const snapshot = await t.get(docRef);
        if (!snapshot.exists) throw new Error("Category not found");

        const data = snapshot.data();
        if (data.tenantId !== tenantId) throw new Error("Unauthorized");

        t.update(docRef, { activeStatus });
    });
}

async function getBarCategoryIds(tenantId, categoryIds = []) {  
  let q = CATEGORY_COLLECTION
    .where("tenantId", "==", tenantId)
    .where("isBarCategory", "==", true);

  if (categoryIds.length) {
    q = q.where("id", "in", categoryIds);
  }

  const snapshot = await q.get();  
  return snapshot.docs.map(doc => doc.id);
}

module.exports = {
    getCategoryById,
    updateCategoryStatus,
    getBarCategoryIds
};
