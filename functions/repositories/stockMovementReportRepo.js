const { COLLECTIONS } = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");
const { FirestoreDateHelper: FD, DEFAULT_TZ } = require("@/helpers/dateHelper");
const admin = require("firebase-admin");
const db = admin.firestore();
const dayjs = require("dayjs");
const axios = require("axios");
const { getStores } = require("@/repositories/storeRepo");
const {
  getWorkAreaIds,
  getBarWorkAreaIds,
} = require("@/repositories/locationRepo");
const {
  getTransferInLedgersForReport,
} = require("@/repositories/stockLedgerRepo");
const { getBarCategoryIds } = require("@/repositories/categoryRepo");
const { getAccountsData } = require("@/controllers/accountController");

/**
 * Fetches report groups data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchReportGroupsData = async (tenantId, filters = {}) => {
  if (!tenantId) return [];

  const {
    reportDateType = "createdAt",
    _fsFromDate,
    _fsToDate,
    locations = [],
  } = filters;

  const [fromDate, toDate] = generateDateRange(_fsFromDate, _fsToDate, false);

  /* ---------- Sales Data ---------- */
  const departments = await fetchSalesData(
    tenantId,
    locations,
    fromDate,
    toDate,
  );

  /* ---------- Report Groups ---------- */
  const groupsSnap = await new QueryBuilder(COLLECTIONS.REPORT_GROUPS)
    .whereMust("tenantId", "==", tenantId)
    .query.get();

  if (groupsSnap.empty) return [];

  /* ---------- Transfer-In Ledgers ---------- */
  const ledgers = await getTransferInLedgersForReport({
    tenantId,
    dateField: reportDateType,
    fromDate: _fsFromDate,
    toDate: _fsToDate,
    locationIds: locations,
  });

  /* ---------- Collect mapped category IDs ---------- */
  const mappedCategoryIds = new Set();

  groupsSnap.docs.forEach((doc) => {
    (doc.data().category || []).forEach((cat) => {
      if (cat.id) mappedCategoryIds.add(cat.id);
    });
  });

  /* ---------- Calculate category cost + others cost ---------- */
  const categoryCostMap = {};
  let othersCost = 0;

  ledgers.forEach(({ categoryId, totalCost = 0 }) => {
    if (!categoryId) return;

    categoryCostMap[categoryId] =
      (categoryCostMap[categoryId] || 0) + totalCost;

    if (!mappedCategoryIds.has(categoryId)) {
      othersCost += totalCost;
    }
  });

  const groups = groupsSnap.docs.map((doc) => {
    const group = doc.data();
    const deptSales = (group.department || []).reduce(
      (acc, dep) => {
        const sales = departments[dep?.trim()];
        if (!sales) return acc;

        acc.grossAmount += sales.grossAmount || 0;
        acc.netSales += sales.netSales || 0;
        return acc;
      },
      { grossAmount: 0, netSales: 0 },
    );

    return {
      id: doc.id,
      ...group,
      grossAmount: deptSales.grossAmount || 0,
      netSales: deptSales.netSales || 0,
      category: (group.category || []).map((cat) => ({
        ...cat,
        cost: categoryCostMap[cat.id] || 0,
      })),
    };
  });

  return { groups, othersCost };
};

/**
 * Fetch sales data by posIds, startDate and endDate
 */
const fetchSalesData = async (tenantId, locations, fromDate, toDate) => {
  const stores = await getStores(tenantId, locations);
  if (!stores?.length) return {};

  const posIds = [
    ...new Set(stores.map((s) => Number(s.posId)).filter(Boolean)),
  ];
  if (!posIds.length) return {};

  const accountIds = [
    ...new Set(stores.map((s) => s.accountId).filter(Boolean)),
  ];
  if (!accountIds.length) return {};

  const accounts = await getAccountsData(tenantId, accountIds);
  if (!accounts?.length) return {};

  const accPosIds = [
    ...new Set(accounts.map((a) => Number(a.posId)).filter(Boolean)),
  ];
  if (!accPosIds.length) return {};

  const responses = await Promise.all(
    accPosIds.map((id) =>
      axios
        .put(
          `${process.env.BO_URI}/accounts/${id}/department/summary`,
          {
            store_ids: posIds,
            from_date: fromDate,
            to_date: toDate,
          },
          {
            headers: {
              "App-Id": process.env.BO_APP_ID,
              "App-Code": process.env.BO_APP_CODE,
              "Content-Type": "application/json",
            },
          },
        )
        .catch((err) => {
          console.error(`Sales API failed for account POS ${id}`, err.message);
          return null;
        }),
    ),
  );

  // Merge department data
  return responses.reduce((salesData, res) => {
    if (res?.data?.departments) {
      Object.assign(salesData, res.data.departments);
    }
    return salesData;
  }, {});
};

/**
 * Fetches transfer report data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchTransferReportData = async (tenantId, filters = {}) => {
  const dateField = filters.reportDateType || "requestedBy.time";
  const stockType = filters?.stockType;

  // Initialize QueryBuilder for transfers collection
  const q = new QueryBuilder(COLLECTIONS.TRANSFERS)
    .whereMust("tenantId", "==", tenantId)
    .whereIf(dateField, ">=", filters._fsFromDate)
    .whereIf(dateField, "<=", filters._fsToDate)
    .whereIf("requester.id", "in", filters.inventoryLocations);

  if (stockType === 2) {
    q.whereMust("stockableItems", "==", true);
  } else if (stockType === 3) {
    q.whereMust("stockableItems", "==", false);
  }

  // .whereIf("vendorId", "in", filters.vendors);

  //@todo: add select columns if needed

  // Execute query and return snapshot
  return await q.query.get();
};

const fetchTenantsReportData = async (tenantId, filters = {}, options = {}) => {
  const {
    defaultWorkArea = null,
    barWorkArea = false,
    barCategory = false,
  } = options;

  const dateRange = generateDateRange(filters._fsFromDate, filters._fsToDate);

  // 1. Resolve Locations
  let locations = filters.locations?.length ? filters.locations : [];
  if (locations.length === 0) {
    const locationsRef = db
      .collection("tenants")
      .doc(tenantId)
      .collection("locations");
    const documentRefs = await locationsRef.listDocuments();
    locations = documentRefs.map((doc) => doc.id);
  }

  // 2. Resolve Work Areas for each location
  // We use a Map to keep track of which WorkAreas belong to which Location
  let workAreasMap = {};
  const workAreaPromises = locations.map(async (locId) => {
    const waIds = filters.inventoryLocations?.length
      ? filters.inventoryLocations
      : [];

    let workAreaIds = [];

    if (waIds.length === 0) {
      const waRef = db
        .collection("tenants")
        .doc(tenantId)
        .collection("locations")
        .doc(locId)
        .collection("workAreas");

      const waDocs = await waRef.listDocuments();
      workAreaIds = waDocs.map((doc) => doc.id);
    } else {
      workAreaIds = waIds;
    }

    if (barWorkArea) {
      workAreasMap[locId] = await getBarWorkAreaIds(tenantId, workAreaIds);
    } else if (defaultWorkArea === null) {
      workAreasMap[locId] = workAreaIds;
    } else {
      workAreasMap[locId] = await getWorkAreaIds(
        tenantId,
        workAreaIds,
        defaultWorkArea,
      );
    }
  });

  await Promise.all(workAreaPromises);

  // 3. Prepare promises for the "items" subcollection
  // Structure: stockMovements > {date} > items > {id_pkgId}
  const promises = [];

  for (const locId of locations) {
    for (const waId of workAreasMap[locId]) {
      for (const date of dateRange) {
        const itemsRef = db
          .collection("tenants")
          .doc(tenantId)
          .collection("locations")
          .doc(locId)
          .collection("workAreas")
          .doc(waId)
          .collection("stockMovements")
          .doc(date)
          .collection("items");

        // Push the get() promise to the array
        promises.push(itemsRef.get());
      }
    }
  }

  // 4. Fetch all data and flatten results
  const snapshots = await Promise.all(promises);

  // Extract data from snapshots and apply secondary filters (categories, vendors, etc.)
  const reportData = snapshots.flatMap((snap) => {
    return snap.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        // Helpful for debugging or grouping later
        _locationId: doc.ref.parent.parent.parent.parent.parent.parent.id,
        _workAreaId: doc.ref.parent.parent.parent.parent.id,
        _date: doc.ref.parent.parent.id,
      };
    });
  });

  let filteredReportData = reportData;

  // 5. Filter by bar category
  if (barCategory) {
    const categoryIds = [...new Set(reportData.map((r) => r.categoryId))];
    const barCategoryIds = await getBarCategoryIds(tenantId, categoryIds);

    filteredReportData = reportData.filter((item) =>
      barCategoryIds.includes(item.categoryId),
    );
  }

  // 6. Apply existing JS-side filters
  const result = filteredReportData.filter((item) => {
    const matchCategory = filters.categories?.length
      ? filters.categories.includes(item.categoryId)
      : true;
    const matchSubCategory = filters.subCategories?.length
      ? filters.subCategories.includes(item.subCategoryId)
      : true;
    const matchItem = filters.inventoryItems?.length
      ? filters.inventoryItems.includes(item.inventoryItemId)
      : true;

    return matchCategory && matchSubCategory && matchItem;
  });

  // console.log(result, "result");
  return result;
};

/**
 * Generate date range from Firestore timestamps
 * Returns array of date strings in format 'DD-MMM-YYYY' (e.g., '30-Nov-2026')
 */
function generateDateRange(fromTimestamp, toTimestamp, dateRange = true) {
  const dates = [];

  if (!fromTimestamp || !toTimestamp) return dates;

  // Convert Firestore Timestamp → dayjs (IST)
  let startDate = dayjs(FD.toJSDate(fromTimestamp))
    .tz(DEFAULT_TZ)
    .startOf("day");

  const endDate = dayjs(FD.toJSDate(toTimestamp)).tz(DEFAULT_TZ).startOf("day");

  // if dateRange is false, return only start & end
  if (!dateRange) {
    return [startDate.format("DD-MMM-YYYY"), endDate.format("DD-MMM-YYYY")];
  }

  // if dateRange is true, returns start - end
  let currentDate = startDate;

  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, "day")) {
    dates.push(currentDate.format("DD-MMM-YYYY"));
    currentDate = currentDate.add(1, "day");
  }
  return dates;
}

module.exports = {
  fetchTransferReportData,
  fetchTenantsReportData,
  fetchReportGroupsData,
};
