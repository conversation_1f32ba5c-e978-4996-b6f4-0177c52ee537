const { COLLECTIONS } = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");
const { LedgerTypes } = require("@/defs/ledgerDefs");

/**
 * Fetches GRN report data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchGRNReportData = async (tenantId, filters = {}) => {
  const dateField = filters.reportDateType || "createdAt";

  const q = new QueryBuilder(COLLECTIONS.GRN)
    .whereMust("tenantId", "==", tenantId)
    .whereIf("status", "!=", "deleted")
    .whereIf(dateField, ">=", filters._fsFromDate)
    .whereIf(dateField, "<=", filters._fsToDate);

  if (filters.inventoryLocations?.length) {
    q.whereIf("inventoryLocation.id", "in", filters.inventoryLocations);
  } else if (filters.locations?.length) {
    q.whereIf("location.id", "in", filters.locations);
  }

  if (filters.vendors?.length) {
    q.whereIf("vendorId", "in", filters.vendors);
  }

  return await q.query.get();
};

/**
 * Fetches GRN data from the ledger with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, category, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchGRNFromLedger = async (tenantId, filters = {}) => {
  // "grnMeta.grnDate" + filters.reportDateType ||
  const dateField = "createdAt";

  const q = new QueryBuilder(COLLECTIONS.LEDGERS)
    .whereMust("tenantId", "==", tenantId)
    .whereMust("ledgerType", "==", LedgerTypes.GRN)
    .whereIf(dateField, ">=", filters._fsFromDate)
    .whereIf(dateField, "<=", filters._fsToDate)
    .whereIf("locationId", "in", filters.locations)
    .whereIf("inventoryLocationId", "in", filters.inventoryLocations)
    .whereIf("categoryId", "in", filters.categories)
    .whereIf("subCategoryId", "in", filters.subCategories)
    .whereIf("inventoryItemId", "in", filters.inventoryItems);

  // @todo: add select columns if needed

  return await q.query.get();
};

module.exports = {
  fetchGRNReportData,
  fetchGRNFromLedger,
};
