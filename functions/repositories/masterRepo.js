/**
 * @fileoverview Repository layer for fetching master data from Firestore.
 * Uses centralized collection definitions from CollectionDefs.js.
 */

const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { DEFAULT_UNITS } = require("@/utils/defaultData");
const { paiseToRupee } = require("@/utils/money");

const db = admin.firestore();

/**
 * Fetch all active locations for a tenant.
 * @param {string} tenantId - Tenant ID to filter locations
 * @returns {Promise<Array<{id: string, name: string}>>} Array of locations
 */
async function getLocations(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.LOCATIONS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "inventoryLocationId", "inventoryLocationName")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active work areas for a tenant.
 * @param {string} tenantId - Tenant ID to filter work areas
 * @returns {Promise<Array<{id: string, name: string, location_id: string, location_name: string, menu_tag: string, tag_id: string}>>} Array of work areas
 */
async function getInventoryLocations(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.WORK_AREAS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "locationId", "locationName", "tagId", "isDefault", "isBarCounter")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active categories and subcategories for a tenant.
 * Splits subcategories from the main category document.
 * @param {string} tenantId - Tenant ID to filter categories
 * @returns {Promise<{
 *   categories: Array<{id: string, name: string}>,
 *   subCategories: Array<{id: string, name: string, categoryId: string}>
 * }>} Object containing categories and subcategories arrays
 */
async function getCategoriesAndSubCategories(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.CATEGORIES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "subCategories", "isBarCategory")
    .get();

  const categories = [];
  const subCategories = [];

  snapshot.docs.forEach((doc) => {
    const data = doc.data();
    categories.push({ id: doc.id, name: data.name, isBarCategory: data.isBarCategory });

    if (Array.isArray(data.subCategories)) {
      data.subCategories.forEach((sub) => {
        subCategories.push({
          id: sub.id,
          name: sub.name,
          categoryId: doc.id,
          categoryName: data.name,
        });
      });
    }
  });

  return { categories, subCategories };
}

/**
 * Fetch all active vendors for a tenant.
 * @param {string} tenantId - Tenant ID to filter vendors
 * @returns {Promise<Array<{id: string, name: string}>>} Array of vendors
 */
async function getVendors(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.VENDORS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "poTerms", "paymentTerms", "contactEmailId", "contactName", "contactNo")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active menu items for a tenant.
 * Maps Firestore fields to consistent keys.
 * @param {string} tenantId - Tenant ID to filter menu items
 * @returns {Promise<Array<{
 *   id: string,
 *   name: string,
 *   categoryId: string,
 *   subCategoryId: string,
 *   menuTags: Array<string>,
 *   vendors: Array<string>
 * }>>} Array of menu items
 */
async function getInventoryItems(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select(
      "itemName",
      "itemCode",
      "hsnCode",
      "stockable",
      "category",
      "subCategory",
      "tags",
      "vendors",
      "packages",
      "purchaseUnit",
      "countingUnit",
      "recipeUnit",
      "unitCost",
      "unitCostIncludingTax",
      "defaultPackage",
      "showWeight",
      "itemType",
      "recipe",
      "allVendors"
    )
    .get();

  return snapshot.docs.map((doc) => {
    const data = doc.data();
    let subtitle = data.itemCode;
    if (data.hsnCode) {
      subtitle += `, HSN Code: ${data.hsnCode}`;
    }
    return {
      id: doc.id,
      name: data.itemName,
      subtitle,
      code: data.itemCode,
      hsnCode: data.hsnCode,
      stockable: data.stockable,
      showWeight: data.showWeight,
      itemType: data.itemType,
      recipeName: data.recipe?.name || null,
      recipeId: data.recipe?.id || null,
      categoryId: data.category?.id,
      subCategoryId: data.subCategory?.id,
      categoryName: data.category?.name,
      subCategoryName: data.subCategory?.name,
      menuTags: data.tags?.map((tag) => tag.id) || [],
      vendors: data.vendors?.map((vendor) => vendor.id) || [],
      allVendors: data.allVendors || false,
      // purchaseUnit: data.purchaseUnit.symbol,
      purchaseUnit: data.purchaseUnit,
      countingUnit: data.countingUnit,
      recipeUnit: data.recipeUnit,
      defaultPackage: data.defaultPackage ?? true,
      unitCost: data.unitCost ? paiseToRupee(data.unitCost) : 0,
      unitCostIncludingTax: data.unitCostIncludingTax
        ? paiseToRupee(data.unitCostIncludingTax)
        : 0,
      packages:
        data.packages?.map((pkg) => ({
          ...pkg,
          id: pkg.id,
          name: pkg.name,
          unitCost: paiseToRupee(pkg.unitCost),
        })) || [],
    };
  });
}

/**
 * Fetch all active tags for a tenant.
 * @param {string} tenantId - Tenant ID to filter tags
 * @returns {Promise<Array<{id: string, name: string}>>} Array of tags
 */
async function getTags(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.TAGS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("name")
    .select("name")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active product ledgers for a tenant.
 * @param {string} tenantId - Tenant ID to filter product ledgers
 * @returns {Promise<Array<{id: string, name: string}>>} Array of active product ledgers
 */
async function getProductLedgers(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.PRODUCT_LEDGERS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("name")
    .select("name")
    .get();

  // Map the snapshot documents to an array of objects with their IDs and names
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active taxes for a tenant.
 * @param {string} tenantId - Tenant ID to filter taxes
 * @returns {Promise<Array<{id: string, name: string, components?: Array}>>} Array of taxes
 */
async function getTaxes(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.TAXES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select(
      "name",
      "components",
      "valuePercentage",
      "valueAmt",
      "valueType",
      "taxLevel"
    )
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active charges for a tenant.
 * @param {string} tenantId - Tenant ID to filter charges
 * @returns {Promise<Array<{id: string, name: string, components?: Array}>>} Array of charges
 */
async function getCharges(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.CHARGES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "valueAmt", "valueType", "valuePercentage", "type")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active house units for a tenant.
 * @param {string} tenantId - Tenant ID to filter house units
 * @returns {Promise<Array<{id: string, name: string, symbol: string, toUnit: string, quantity: number}>>} Array of house units
 */
async function getHouseUnits(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.HOUSE_UNITS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "symbol", "toUnit", "quantity", "default")
    .get();

  const houseUnits = snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
  return [...DEFAULT_UNITS, ...houseUnits];
}

/*************  ✨ Windsurf Command 🌟  *************/
/**
 * Fetch all active recipes for a tenant.
 * @param {string} tenantId - Tenant ID to filter recipes
 * @returns {Promise<Array<{id: string, name: string, recipeCode: string, cost: number, recipeUnit: object}>>} Array of recipes
 */
async function getRecipes(tenantId) {
  // Get all active recipes for the given tenant ID
  const snapshot = await db
    .collection(COLLECTIONS.RECEIPES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select(
      "name",
      "recipeCode",
      "cost",
      "recipeUnit",
      "recipeType",
      "quantity"
    )
    .get();

  return snapshot.docs.map((doc) => {
    const data = doc.data();
    return {
      id: doc.id,
      name: data.name,
      recipeCode: data.recipeCode,
      cost: data.cost ? paiseToRupee(data.cost) : 0,
      recipeUnit: data.recipeUnit,
      recipeType: data.recipeType,
      recipeQuantity: data.quantity,
    };
  });
}

module.exports = {
  getLocations,
  getInventoryLocations,
  getCategoriesAndSubCategories,
  getVendors,
  getInventoryItems,
  getTags,
  getTaxes,
  getCharges,
  getHouseUnits,
  getProductLedgers,
  getRecipes,
};
