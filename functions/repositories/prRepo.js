// repositories/prRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();

const PR_COLLECTION = "purchaseRequests";
const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { purchaseStatus, activeStatuses } = require("@/defs/purchaseStatusDefs");

const getPRByNumber = async (tenantId, prNumber) => {
  let query = db
    .collection(PR_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("prNumber", "==", prNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
};

// const getPurchaseRequests = async (tenantId, filters) => {
//   const statuses = filters.status || [];
//   const hasPending = statuses.includes("pending");
//   const hasCompleted = statuses.includes("completed");

//   // -------------------------------
//   // CASE 1: Both pending & completed
//   // -------------------------------
//   if (hasPending && hasCompleted) {
//     const [pending, completed] = await Promise.all([
//       fetchPendingPurchaseRequests(tenantId, filters),
//       fetchCompletedPurchaseRequests(tenantId, filters),
//     ]);

//     // merge unique by id
//     const map = new Map();
//     [...pending, ...completed].forEach((t) => map.set(t.id, t));
//     return Array.from(map.values());
//   }

//   if (hasPending) {
//     return fetchPendingPurchaseRequests(tenantId, filters);
//   }

//   if (hasCompleted) {
//     return fetchCompletedPurchaseRequests(tenantId, filters);
//   }

//   return [];
// };

// const fetchPendingPurchaseRequests = async (tenantId, filters) => {
//   let query = db.collection(PR_COLLECTION).where("tenantId", "==", tenantId);

//   // status filter
//   if (filters.status?.length) {
//     query = query.where("status", "in", activeStatuses);
//   }

//   // locations filter
//   if (filters.locations?.length) {
//     query = query.where("location.id", "in", filters.locations);
//   }

//   // inventory locations filter
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocation.id",
//       "in",
//       filters.inventoryLocations
//     );
//   }

//   const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
//   return snapshot.docs.map((doc) => doc.data());
// };

// const fetchCompletedPurchaseRequests = async (tenantId, filters) => {
//   let query = db.collection(PR_COLLECTION).where("tenantId", "==", tenantId);

//   // status filter
//   if (filters.status?.length) {
//     query = query.where("status", "in", [
//       purchaseStatus.REJECTED,
//       purchaseStatus.COMPLETED,
//       purchaseStatus.DELETED,
//       purchaseStatus.CLOSED,
//     ]);
//   }

//   // locations filter
//   if (filters.locations?.length) {
//     query = query.where("location.id", "in", filters.locations);
//   }

//   // inventory locations filter
//   if (filters.inventoryLocations?.length) {
//     query = query.where(
//       "inventoryLocation.id",
//       "in",
//       filters.inventoryLocations
//     );
//   }

//   // date range filter (convert incoming filters to millis)
//   if (filters.fromDate) {
//     query = query.where(
//       "lastUpdatedTime",
//       ">=",
//       FD.toFirestore(filters.fromDate, TIME_OPTION.START)
//     );
//   }
//   if (filters.toDate) {
//     query = query.where(
//       "lastUpdatedTime",
//       "<=",
//       FD.toFirestore(filters.toDate, TIME_OPTION.END)
//     );
//   }

//   const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
//   return snapshot.docs.map((doc) => doc.data());
// };

const getPurchaseRequests = async (tenantId, filters = {}) => {
  const raw = await fetchAllPurchaseRequests(tenantId, filters);
  return applyFilters(raw, filters);
};

const fetchAllPurchaseRequests = async (tenantId, filters) => {
  let query = db.collection(PR_COLLECTION).where("tenantId", "==", tenantId);

  if (filters.fromDate) {
    query = query.where(
      "lastUpdatedTime",
      ">=",
      FD.toFirestore(filters.fromDate, TIME_OPTION.START)
    );
  }

  if (filters.toDate) {
    query = query.where(
      "lastUpdatedTime",
      "<=",
      FD.toFirestore(filters.toDate, TIME_OPTION.END)
    );
  }

  const snapshot = await query.orderBy("lastUpdatedTime", "desc").get();
  return snapshot.docs.map((d) => d.data());
};

const applyFilters = (items, filters = {}) => {
  const { status = [], locations = [], inventoryLocations = [] } = filters;

  const wantsPending = status.includes("pending");
  const wantsCompleted = status.includes("completed");

  return items.filter((item) => {
    // -----------------------
    // Status logic
    // -----------------------
    if (status.length) {
      const isPending = activeStatuses.includes(item.status);
      const isCompleted = [
        purchaseStatus.REJECTED,
        purchaseStatus.COMPLETED,
        purchaseStatus.DELETED,
        purchaseStatus.CLOSED,
      ].includes(item.status);

      if (wantsPending && !wantsCompleted && !isPending) return false;
      if (wantsCompleted && !wantsPending && !isCompleted) return false;
      if (!wantsPending && !wantsCompleted) return false;
    }

    // -----------------------
    // Location filter
    // -----------------------
    if (locations?.length && !locations.includes(item.location?.id)) {
      return false;
    }

    // -----------------------
    // Inventory location filter
    // -----------------------
    if (
      inventoryLocations?.length &&
      !inventoryLocations.includes(item.inventoryLocation?.id)
    ) {
      return false;
    }

    return true;
  });
};

const savePurchaseRequest = async (data) => {
  const newDocRef = db.collection(PR_COLLECTION).doc();

  try {
    const purchaseRequest = { ...data, id: newDocRef.id };
    await newDocRef.set(purchaseRequest);
    return purchaseRequest;
  } catch (err) {
    throw Error(err.message);
  }
};

const getPurchaseRequestById = async (id) => {
  try {
    const docRef = db.collection(PR_COLLECTION).doc(id);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;
    return snapshot.data();
  } catch (err) {
    throw Error(err.message);
  }
};

const updatePurchaseRequestById = async (id, data) => {
  try {
    const docRef = db.collection(PR_COLLECTION).doc(id);
    await docRef.update(data);
  } catch (err) {
    throw Error(err.message);
  }
};

/**
 * Returns the count of active PRs for a tenant
 * @param {string} tenantId
 * @returns {Promise<number>} count of active PRs
 */
const getActivePRCount = async (tenantId) => {
  if (!tenantId) throw new Error("tenantId is required");
  const snapshot = await db
    .collection(PR_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("status", "in", activeStatuses)
    .count()
    .get(); // Firestore count() aggregate query

  return snapshot.data().count; // returns the numeric count
};

module.exports = {
  getPRByNumber,
  getPurchaseRequests,
  savePurchaseRequest,
  getPurchaseRequestById,
  updatePurchaseRequestById,
  getActivePRCount,
};
