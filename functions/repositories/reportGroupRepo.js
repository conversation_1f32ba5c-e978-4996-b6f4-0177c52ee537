// repositories/vendorRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const REPORT_GROUPS_COLLECTION = db.collection(COLLECTIONS.REPORT_GROUPS);

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateReportGroupStatus(tenantId, docId, activeStatus) {
  const docRef = REPORT_GROUPS_COLLECTION.doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Report Group not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
  updateReportGroupStatus,
};
