// repositories/vendorValidateRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");
/**
 * Count how many menu items a vendor is linked to
 * Uses the vendorIds array for efficient query
 * @param {string} tenantId - Tenant ID
 * @param {string} vendorId - Vendor ID
 * @returns {number} Count of menu items linked to the vendor
 */

const getVendorCountInventoryItems = async (tenantId, vendorId) => {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    const vendors = data.vendors || [];
    if (vendors.some(v => v.id === vendorId)) {
      count++;
    }
  });

  return count;
};

const getTagCountInventoryItems = async (tenantId, tagId) => {
  const countQuery = db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .where("tags", "array-contains", tagId)
    .count();

  const snapshot = await countQuery.get();
  return snapshot.data().count;
};

const getTagCountRecipes = async (tenantId, tagId) => {
  const countQuery = db
    .collection(COLLECTIONS.RECEIPES)
    .where("tenantId", "==", tenantId)
    .where("tags", "array-contains", tagId)
    .count();

  const snapshot = await countQuery.get();
  return snapshot.data().count;
};

const getTagCountWorkAreas = async (tenantId, tagId) => {
  const countQuery = db
    .collection(COLLECTIONS.WORK_AREAS)
    .where("tenantId", "==", tenantId)
    .where("tagId", "==", tagId)
    .count();

  const snapshot = await countQuery.get();
  return snapshot.data().count;
};

const getCategoryCountInventoryItems = async (tenantId, categoryId) => {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    const category = data.category;
    if (category && category.id === categoryId) {
      count++;
    }
  });

  return count;
};

const getHouseUnitsCountInventoryItems = async (tenantId, symbol) => {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;

  snapshot.forEach(doc => {
    const data = doc.data();

    const purchaseSymbol = data.purchaseUnit?.symbol;
    const recipeSymbol = data.recipeUnit?.symbol;
    const countingSymbol = data.countingUnit?.symbol;

    // Match any of the 3 symbols
    if (
      purchaseSymbol === symbol ||
      recipeSymbol === symbol ||
      countingSymbol === symbol
    ) {
      count++;
    }
  });

  return count;
};

const getHouseUnitsCountRecipes = async (tenantId, symbol) => {
  const snapshot = await db
    .collection(COLLECTIONS.RECEIPES)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;

  snapshot.forEach(doc => {
    const data = doc.data();

    const recipeUnit = data.recipeUnit;
    if (
      recipeUnit === symbol
    ) {
      count++;
    }
  });

  return count;
};

const getItemsCountRecipes = async (tenantId, itemId) => {
  const snapshot = await db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;

  snapshot.forEach(doc => {
    const data = doc.data();
    const items = data.items || [];
    const hasItem = items.some(item => item.itemId === itemId);

    if (hasItem) {
      count++;
    }
  });

  return count;
};

const getLocationCountWorkAreas = async (tenantId, storeId) => {
  const countQuery = db
    .collection(COLLECTIONS.WORK_AREAS)
    .where("tenantId", "==", tenantId)
    .where("storeId", "==", storeId)
    .count();

  const snapshot = await countQuery.get();
  return snapshot.data().count;
};

const getRoleCountUsers = async (tenantId, roleName) => {
  const countQuery = db
    .collection(COLLECTIONS.USERS)
    .where("tenantId", "==", tenantId)
    .where("roleName", "==", roleName)
    .count();

  const snapshot = await countQuery.get();
  return snapshot.data().count;
};

const getTaxCountInventoryItems = async (tenantId, tagId) => {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .get();

  let count = 0;
  snapshot.forEach(doc => {
    const data = doc.data();
    const taxes = data.taxes || [];
    if (taxes.some(t => t.id === tagId)) {
      count++;
    }
  });

  return count;
};

module.exports = {
  getVendorCountInventoryItems,
  getTagCountInventoryItems,
  getTagCountRecipes,
  getTagCountWorkAreas,
  getCategoryCountInventoryItems,
  getHouseUnitsCountInventoryItems,
  getHouseUnitsCountRecipes,
  getItemsCountRecipes,
  getLocationCountWorkAreas,
  getRoleCountUsers,
  getTaxCountInventoryItems
};