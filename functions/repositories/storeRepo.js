// repositories/vendorRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const STORE_COLLECTION = db.collection(COLLECTIONS.LOCATIONS);

async function getStores(tenantId, locationIds=[]) {
  let q = STORE_COLLECTION
    .where("tenantId", "==", tenantId)
    if (locationIds.length){
      q = q.where("id", "in", locationIds)
    }
    const snapshot = await q.orderBy("nameNormalized")
    .get();
  
  const response = snapshot.docs.map((doc) => doc.data());
  return response
}

/**
 * Get a store by doc ID and validate tenant
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @returns {object|null} store data or null if not found / tenant mismatch
 */
async function getStoreById(tenantId, docId) {
  const docRef = STORE_COLLECTION.doc(docId);
  const snapshot = await docRef.get();

  if (!snapshot.exists) return null;

  const data = snapshot.data();
  if (data.tenantId !== tenantId) return null; // tenant validation

  return { id: snapshot.id, ...data };
}

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateStoreStatus(tenantId, docId, activeStatus) {
  const docRef = STORE_COLLECTION.doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Store not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
    getStoreById,
    updateStoreStatus,
    getStores
};
