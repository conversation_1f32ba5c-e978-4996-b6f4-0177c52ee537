// repositories/consumptionRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");
const { paiseToRupee } = require("@/utils/money");

const CONSUMPTION_COLLECTION = db.collection(COLLECTIONS.CONSUMPTION_TRACKING);

async function createConsumption(data) {
  const ref = CONSUMPTION_COLLECTION.doc();

  // Filter out undefined values to prevent Firestore errors
  const cleanData = Object.fromEntries(
    Object.entries({ ...data, id: ref.id }).filter(
      ([_, value]) => value !== undefined
    )
  );

  await ref.set(cleanData);
  return { id: ref.id, ...data };
}

/**
 * Fetches consumption report data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchConsumptionReportData = async (tenantId, filters = {}) => {
  const q = new QueryBuilder(COLLECTIONS.CONSUMPTION_TRACKING).whereMust(
    "tenantId",
    "==",
    tenantId
  );

  if (filters.inventoryLocations?.length) {
    q.whereIf("workAreaId", "in", filters.inventoryLocations);
  } else if (filters.locations?.length) {
    q.whereIf("locationId", "in", filters.locations);
  }

  if (filters.status?.length) {
    q.whereIf("status", "in", filters.status);
  }
  if (filters.type?.length) {
    q.whereIf("type", "in", filters.type);
  }

  // Apply orderBy directly on the Firestore query
  q.query = q.query.orderBy("createdAt", "desc");

  const snapshot = await q.query.get();

  // Map and transform the data
  return snapshot.docs.map((doc) => {
    const data = doc.data();

    // Add cost to each invItem
    let totalCost = 0;

    const invItemsWithCost =
      data.invItems?.map((item) => {
        const costInRupees = paiseToRupee(item.cost);
        const soldQty = data.soldQuantity || 0;

        totalCost += Number(costInRupees) || 0; 

        return {
          ...item,
          cost: costInRupees,
          unitCost: soldQty > 0 ? costInRupees / soldQty : 0,
          unitQuantity: soldQty > 0 ? item.requiredQty / soldQty : 0,
        };
      }) ?? [];

    return {
      id: doc.id,
      ticketNo: data.ticketNo,
      floorNo: data.floorNo,
      floorName: data.floorName,
      posMenuItemName: data.posMenuItemName,
      posItemCode: data.posItemCode,
      invItemCode: data.invItemCode,
      invItemName: data.invItemName,
      invItems: invItemsWithCost,
      modifierName: data?.modifierName,
      type: data?.type,
      servingSizeName: data.servingSizeName,
      soldQuantity: data.soldQuantity,
      posItemTotalAmount: data.posItemTotalAmount,
      location: data.location,
      workArea: data.workArea,
      status: data?.status ?? "-",
      // statusSummary: data?.statusSummary ?? "-",
      statusSummary: data?.status && data.status==='error'? data.statusSummary : ' ',
      costPrice: totalCost,
    };
  });
};

/**
 * Fetches pending consumption data for a tenant.
 *
 * @param {string} tenantId - Tenant identifier.
 * @returns {Promise<Array>} - Array of consumption records with pending status.
 */
async function getConsumptionData(tenantId) {
  const snapshot = await CONSUMPTION_COLLECTION.where(
    "tenantId",
    "==",
    tenantId
  )
    .where("status", "==", "pending")
    .get();

  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
}

async function getConsumptionDataById(tenantId, docId) {
  const doc = await CONSUMPTION_COLLECTION.doc(docId).get();

  if (!doc.exists) return null;

  const data = doc.data();

  // safety check
  if (data.tenantId !== tenantId) {
    throw new Error("Tenant mismatch for consumption record");
  }

  return {
    id: doc.id,
    ...data,
  };
}


/**
 * Updates an existing consumption record
 * @param {string} id - consumption document id
 * @param {object} data - updated consumption record
 */
const updateConsumptionRecord = async (id, data) => {
  // Do not persist `id` field back into Firestore
  const { id: _, ...payload } = data;

  await CONSUMPTION_COLLECTION
    .doc(id)
    .update(payload);
};


/**
 * Update consumption records in Firestore
 */
const updateConsumptionRecordsInFirestore = async (updatedRecords) => {
  if (!updatedRecords || updatedRecords.length === 0) {
    console.log("ℹ️  No records to update in Firestore");
    return { success: true, successCount: 0, failCount: 0, results: [] };
  }

  console.log(`\n📝 Updating ${updatedRecords.length} records in Firestore...`);

  const updatePromises = updatedRecords.map(async (record) => {
    try {
      // Prepare update payload
      const updatePayload = {
        status: record.status,
        statusSummary: record.statusSummary,
        invItems: record.invItems,
      };

      // Update the document in Firestore
      await CONSUMPTION_COLLECTION.doc(record.id).update(updatePayload);

      console.log(
        `   ✓ Updated record ${record.id} (Ticket #${record.ticketNo})`
      );

      return {
        success: true,
        recordId: record.id,
        ticketNo: record.ticketNo,
      };
    } catch (error) {
      console.error(
        `   ❌ Failed to update record ${record.id}:`,
        error.message
      );
      return {
        success: false,
        recordId: record.id,
        ticketNo: record.ticketNo,
        error: error.message,
      };
    }
  });

  const results = await Promise.all(updatePromises);

  const successCount = results.filter((r) => r.success).length;
  const failCount = results.filter((r) => !r.success).length;

  console.log(
    `\n✓ Firestore updates complete: ${successCount} success, ${failCount} failed`
  );

  if (failCount > 0) {
    const failedRecords = results.filter((r) => !r.success);
    console.error("⚠️  Failed records:", failedRecords);
  }

  return {
    success: failCount === 0,
    successCount,
    failCount,
    results,
  };
};

module.exports = {
  createConsumption,
  fetchConsumptionReportData,
  getConsumptionData,
  getConsumptionDataById,
  updateConsumptionRecord,
  updateConsumptionRecordsInFirestore,
};
