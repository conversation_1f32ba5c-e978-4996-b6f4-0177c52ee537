// repositories/vendorRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const PRODUCT_LEDGER_COLLECTION = db.collection(COLLECTIONS.PRODUCT_LEDGERS);

/**
 * Update activeStatus safely using transaction
 * Validates tenant before updating without fetching the full document
 * @param {string} tenantId - Tenant ID
 * @param {string} docId - Document ID
 * @param {boolean} activeStatus - true = activate, false = deactivate
 */
async function updateProductLedgerStatus(tenantId, docId, activeStatus) {
  const docRef = PRODUCT_LEDGER_COLLECTION.doc(docId);

  await db.runTransaction(async (t) => {
    const snapshot = await t.get(docRef);
    if (!snapshot.exists) throw new Error("Product Ledger not found");

    const data = snapshot.data();
    if (data.tenantId !== tenantId) throw new Error("Unauthorized");

    t.update(docRef, { activeStatus });
  });
}

module.exports = {
  updateProductLedgerStatus,
};
