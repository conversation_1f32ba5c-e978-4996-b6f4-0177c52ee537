const schema = require("@/models/contractSchema");
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const {
  saveContract,
  getAllContracts,
  getById,
  updateById,
  findActiveContracts,
  contractCollection,
  getVendorContracts,
  updateAttachments,
  deleteAttachment,
} = require("@/repositories/contractRepo");
const { rupeeToPaise, paiseToRupee } = require("@/utils/money");
const { getNextContractId } = require("./counterService");
const { contractStatus } = require("@/defs/contractTypesDefs");

const createContract = async (payload) => {
  try {
    payload.startDate = FD.toFirestore(payload.startDate);
    payload.endDate = FD.toFirestore(payload.endDate);
    payload.items = payload.items.map((i) => ({
      ...i,
      contractPrice: rupeeToPaise(i.contractPrice),
    }));

    const data = handleValidation(payload, schema);
    if (!data) throw new Error("Validation failed");

    const cleanName = trimName(data.name);

    const { valid, normalizedName, error } = await nameValidation(
      cleanName,
      contractCollection,
      null,
      "tenantId",
      data.tenantId
    );

    if (!valid) throw new Error(error);

    await validateContractOverlap(data);

    const contractId = await getNextContractId(data.tenantId);
    data.contractNumber = contractId;
    data.name = cleanName;
    data.nameNormalized = normalizedName;
    data.createdAt = FD.now();

    const result = await saveContract(data);
    return result;
  } catch (err) {
    throw new Error(err.message);
  }
};

const validateContractOverlap = async (data) => {
  const contracts = await findActiveContracts(
    data.tenantId,
    data.vendor.id,
    false
  );
  if (!contracts.length) return;

  const newItems = data.items.map((i) => ({
    key: `${i.itemId}-${i.pkg.id}`,
    itemName: i.itemName,
    packageName: i.pkg.name,
  }));

  const overlappingItems = contracts.flatMap((contract) => {
    const vendorName = contract.vendor.name;
    const endDate = FD.toFormattedDate(contract.endDate, DATE_FORMAT.DATE_ONLY);

    const existingSet = new Set(
      contract.items.map((i) => `${i.itemId}-${i.pkg.id}`)
    );

    return newItems
      .filter((item) => existingSet.has(item.key))
      .map((item) => ({
        vendorName,
        itemName: item.itemName,
        packageName: item.packageName,
        endDate,
      }));
  });

  if (!overlappingItems.length) return;

  const message =
    overlappingItems
      .map(
        ({ vendorName, itemName, packageName, endDate }) =>
          `"${itemName}-${packageName}" already contracted with "${vendorName}" until ${endDate}`
      )
      .join("\n") + "\nPlease choose a later start date.";

  throw new Error(message);
};

const getContractStatus = (startDate, endDate, active) => {
  const start = FD.now(TIME_OPTION.START);
  const end = FD.now(TIME_OPTION.END);

  let status = contractStatus.IDLE;
  switch (true) {
    case !active:
      status = contractStatus.CLOSED;
      break;
    case endDate < start:
      status = contractStatus.EXPIRED;
      break;
    case startDate > end:
      status = contractStatus.IDLE;
      break;
    default:
      status = contractStatus.ACTIVE;
      break;
  }
  return status;
};

const getContracts = async (tenantId) => {
  const contracts = await getAllContracts(tenantId);
  const result = contracts.map((c) => {
    const status = getContractStatus(c.startDate, c.endDate, c.activeStatus);
    return {
      id: c.id,
      vendor: c.vendor,
      startDate: FD.toFormattedDate(c.startDate, DATE_FORMAT.DATE_ONLY),
      endDate: FD.toFormattedDate(c.endDate, DATE_FORMAT.DATE_ONLY),
      createdAt: FD.toFormattedDate(c.createdAt),
      requestedBy: c.requestedBy,
      name: c.name,
      contractNumber: c.contractNumber,
      reference: c.reference,
      activeStatus: c.activeStatus,
      status,
      attachments: c.attachments,
    };
  });
  return result;
};

const getContractById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    data.startDate = FD.toFormattedDate(data.startDate, DATE_FORMAT.DATE_ONLY);
    data.endDate = FD.toFormattedDate(data.endDate, DATE_FORMAT.DATE_ONLY);
    // data.location = data.location.map((l) => l.id);
    data.items = data.items.map((i) => ({
      ...i,
      contractPrice: paiseToRupee(i.contractPrice),
    }));
    return data;
  } catch (error) {
    throw Error(err.message);
  }
};

const updateContract = async (id, payload) => {
  try {
    payload.startDate = FD.toFirestore(payload.startDate);
    payload.endDate = FD.toFirestore(payload.endDate);
    payload.updatedAt = FD.now();
    payload.items = payload.items.map((i) => ({
      ...i,
      contractPrice: rupeeToPaise(i.contractPrice),
    }));

    const data = handleValidation(payload, schema);
    if (!data) throw new Error("Validation failed");

    const cleanName = trimName(data.name);

    const { valid, normalizedName, error } = await nameValidation(
      cleanName,
      contractCollection,
      id,
      "tenantId",
      data.tenantId
    );

    if (!valid) throw new Error(error);
    // await validateContractOverlap(data);

    const result = await updateById(id, {
      ...data,
      name: cleanName,
      nameNormalized: normalizedName,
    });

    return result;
  } catch (err) {
    throw new Error(err.message);
  }
};

const closeContractById = async (data, id) => {
  try {
    const updatedData = {
      ...data,
      activeStatus: false,
    };
    await updateById(id, updatedData);
  } catch (error) {
    throw Error(error.message);
  }
};

const findCurrentContractItemPrice = async ({
  tenantId,
  itemId,
  locationId,
  vendorId,
  pkgId = null,
}) => {
  const result = {
    contractPrice: 0,
    contractType: null,
    inclTax: false,
    contractId: null,
    contractNumber: null,
  };

  // Fetch all matching contracts
  const contracts = await findActiveContracts(
    tenantId,
    vendorId,
    (idle = false)
  );

  for (let contract of contracts) {
    const valid = contract.location.some((loc) => loc.id === locationId);
    if (!valid) continue;
    const item = contract.items.find((item) => {
      if (!pkgId || pkgId == "default")
        return item.itemId === itemId && item.pkg?.id == "default";
      return item.itemId === itemId && item.pkg?.id === pkgId;
    });
    if (!item) continue;

    result.contractPrice = paiseToRupee(item.contractPrice);
    result.contractType = item.contractType;
    result.inclTax = item.inclTax;
    result.contractId = contract.id;
    result.contractNumber = contract.contractNumber;
    return result;
  }

  // No matching contract found
  return result;
};

const updateContractAttachments = async (id, attachments) => {
  try {
    const updatedAttachments = await updateAttachments(id, attachments);
    return {
      success: true,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

const deleteContractAttachment = async (id, filePath) => {
  try {
    if (!filePath) throw new Error("File path required");

    const updatedAttachments = await deleteAttachment(id, filePath);

    return {
      success: true,
      message: `File deleted successfully`,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

module.exports = {
  createContract,
  getContracts,
  getContractById,
  updateContract,
  findCurrentContractItemPrice,
  closeContractById,
  updateContractAttachments,
  deleteContractAttachment,
};
