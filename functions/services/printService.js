// services/purchaseService.js
const {
  FirestoreDateHelper: FD,
  DATE_FORMAT,
} = require("@/helpers/dateHelper");
const { getTransferDataById } = require("@/repositories/transferRepo");
const { getVendorById } = require("@/repositories/vendorRepo");
const { getLocationById } = require("@/repositories/locationRepo");
const { getGrnByID } = require("@/services/grnService.js");
const {
  getPRById,
  getPurchaseOrderById,
} = require("@/services/purchaseService.js");

const {
  formatNumber,
  generateAmountInWordsRow,
} = require("@/helpers/printHelper");

async function preparePOData(id, tenantId) {
  const po = await getPurchaseOrderById(id);
  if (!po) return null;

  const vendorId = po?.vendor?.id;
  if (!vendorId) return null;

  const vendor = await getVendorById(tenantId, vendorId);
  if (!vendor) return null;

  const vendorObj = {
    type: "Vendor",
    Name: vendor?.name || "",
    Contact: vendor?.contactName || "",
    Email: vendor?.contactEmailId || "",
    "Bank Name": vendor?.bankName || "",
    "Account Number": vendor?.accountNumber || "",
    "IFSC Code": vendor?.ifscCode || "",
    PAN: vendor?.panNo || "",
    GST: vendor?.gstNo || "",
    Address: vendor?.address?.address
      ? [
          vendor?.address?.address,
          [
            vendor?.address?.city,
            vendor?.address?.state,
            vendor?.address?.pincode,
          ]
            .filter(Boolean)
            .join(", "),
        ]
          .filter(Boolean)
          .join("\n")
      : "",
  };

  const poObj = {
    type: "PO",
    Location: po?.location?.name ? po?.location?.name : "",
    Workarea: po?.inventoryLocation?.name ? po?.inventoryLocation?.name : "",
    "PO No.": po?.poNumber,
    "PR No.": po?.prNumber,
    "Requested Date": po?.requestedTime,
    GST: "",
    PAN: "",
    Creator: po?.requestedBy?.name,
    Delivery: po?.deliveryDate,
  };

  const poTerms = po?.vendor?.poTerms;
  const paymentTerms = po?.vendor?.paymentTerms;
  const remarks = po?.remarks?.split(",").filter(Boolean) || [];
  const itemsTable = await preparePurchaseTable(po, "PO");

  const grandTotal =
    po?.items?.reduce((sum, item) => sum + item.totalPrice, 0) + 50;

  return {
    po,
    vendor,
    vendorObj,
    poObj,
    poTerms,
    paymentTerms,
    remarks,
    grandTotal,
    itemsTable,
  };
}

async function prepareTransferData(id, baseType, dispatchNo) {
  const transfer = await getTransferDataById(id);
  if (!transfer) return null;

  let resolvedType = baseType;
  let dispatchEntry = null;

  if (dispatchNo) {
    dispatchEntry = transfer.timeLine?.find(
      (entry) => entry.dispatchNo === dispatchNo,
    );

    if (dispatchEntry) {
      resolvedType =
        dispatchEntry.status === "completed" ? "Receive" : "Dispatch";
    }
  }

  const transferObj = {
    type: resolvedType,
    "Transfer No.": transfer.transferNumber,
    "Expected Delivery Date": FD.toFormattedDate(
      transfer.transferDate,
      DATE_FORMAT.DATE_ONLY,
    ),
    "From Location": transfer.issuer.locationName,
    "From Work/Storage Area": transfer.issuer.name,
    "To Location": transfer.requester.locationName,
    "To Work/Storage Area": transfer.requester.name,
    "Requested By": transfer.requestedBy.name,
    "Requested Date": FD.toFormattedDate(transfer.requestedBy.time),
  };

  const remarks = transfer?.remarks?.split(",").filter(Boolean) || [];

  if (dispatchEntry) {
    transferObj["Dispatch No."] = dispatchNo;
    transferObj["Actual Dispatch Date"] = FD.toFormattedDate(
      dispatchEntry.dispatchDate,
      DATE_FORMAT.DATE_ONLY,
    );

    transferObj["Dispatched By"] = dispatchEntry.dispatchedBy?.name;

    if (dispatchEntry.receivedBy) {
      transferObj["Actual Receive Date"] = FD.toFormattedDate(
        dispatchEntry.receiveDate,
        DATE_FORMAT.DATE_ONLY,
      );
      transferObj["Received By"] = dispatchEntry.receivedBy?.name;
    }
  }

  return { transfer, transferObj, type: resolvedType, remarks };
}

async function prepareGRNData(grnId, tenantId) {
  const grn = await getGrnByID(grnId, tenantId);
  if (!grn) return null;

  const vendorId = grn.vendorId;
  if (!vendorId) return null;

  const vendor = await getVendorById(tenantId, vendorId);
  if (!vendor) return null;

  const vendorObj = {
    type: "Vendor",
    Name: vendor.name,
    Contact: vendor.contactName,
    Email: vendor.contactEmailId,
    PAN: vendor.panNo,
    GST: vendor.gstNo,
    Address: vendor?.address?.address
      ? [
          vendor.address.address,
          [vendor.address.city, vendor.address.state, vendor.address.pincode]
            .filter(Boolean)
            .join(", "),
        ]
          .filter(Boolean)
          .join("\n")
      : "",
  };

  const grnObj = {
    type: "GRN",
    Location: grn.location?.name ? grn.location.name : "",
    Workarea: grn.inventoryLocation?.name ? grn.inventoryLocation.name : "",
    "GRN No.": grn.grnNumber,
    "GRN Date": grn.grnDate,
    "PO No.": grn.poNumber,
    "Invoice Date": grn.invoiceDate,
    "Invoice No.": grn.invoiceNumber || "",
    GST: vendor.gstNo,
    PAN: vendor.panNo,
  };
  if (grn.securityNumber) {
    grnObj["GRN Security No."] = grn.securityNumber;
  }
  if (grn.transferDetails && grn.transferDetails.length > 0) {
    grnObj["Transfer Nos."] =
      grn.transferDetails.map((t) => t.transferNumber).join(", ") || "-";
    grnObj["Direct Issue WorkAreas"] =
      grn.transferDetails.map((t) => t.workAreaName).join(", ") || "-";
  }
  const grnTerms = vendor.grnTerms?.split(",").filter(Boolean) || [];
  const paymentTerms = vendor.paymentTerms?.split(",").filter(Boolean) || [];
  const remarks = vendor.remarks?.split(",").filter(Boolean) || [];
  const grandTotal =
    grn.items.reduce((sum, item) => sum + item.totalValue, 0) + 0;
  const itemsTable = await preparePurchaseTable(grn);
  return {
    grn,
    vendor,
    vendorObj,
    grnObj,
    grnTerms,
    paymentTerms,
    remarks,
    grandTotal,
    itemsTable,
  };
}

async function preparePRData(prId, tenantId) {
  const pr = await getPRById(prId);
  if (!pr) return null;
  const firstCol = {
    type: "PR",
    "PR No.": pr.prNumber,
    "Delivery Date": pr.deliveryDate,
  };
  const secondCol = {
    "Created By": pr.requestedBy.name ? pr.requestedBy.name : "",
    "Created At": pr.statusTimeline[0].time,
  };
  const itemsTable = await preparePurchaseTable(pr, "PR");
  return {
    pr,
    firstCol,
    secondCol,
    itemsTable,
  };
}

async function preparePurchaseTable(data, type = "GRN") {
  const items = data["items"];
  const qtyKey =
    type === "PR" ||
    (type === "PO" && !["partial", "completed"].includes(data.status))
      ? "quantity"
      : "receivedQty";

  return {
    items: items,
    columns: [
      { key: "index", header: "#", width: 20 },
      {
        key: "itemName",
        header: "Item Name",
        width: "*",
        getValue: (item) => {
          const base = item.foc ? `${item.itemName}(FOC)` : item.itemName;
          return item.remarks ? `${base}\nRemark - ${item.remarks}` : base;
        },
        format: (value) => {
          if (value.includes("\nRemark - ")) {
            const [itemName, remark] = value.split("\nRemark - ");
            return {
              stack: [
                { text: itemName, fontSize: 9 },
                {
                  text: `Remark - ${remark}`,
                  fontSize: 8,
                  margin: [0, 3, 0, 0],
                },
              ],
              style: "cellStyle",
              alignment: "left",
            };
          }
          return {
            text: value,
            style: "cellStyle",
            fontSize: 9,
            alignment: "left",
          };
        },
      },
      {
        key: "pkg",
        header: "Pkg",
        width: 40,
        getValue: (item) => item.pkg?.name || "",
      },
      { key: qtyKey, header: "Qty", width: 40, format: formatNumber },
      { key: "unitCost", header: "Unit Cost", width: 40, format: formatNumber },
      {
        key: "totalDiscount",
        header: "Disc",
        width: 25,
        format: (v) => formatNumber(v || 0),
      },
      { key: "taxRate", header: "Tax", width: 25, format: formatNumber },
      { key: "totalCess", header: "Cess", width: 28, format: formatNumber },
      { key: "netAmount", header: "Net Amt", width: 40, format: formatNumber },
      {
        key: "totalTaxAmount",
        header: "Tax Amt",
        width: 40,
        format: formatNumber,
      },
      { key: "totalAmount", header: "Total", width: 40, format: formatNumber },
    ],

    totalRows: [
      {
        label: "Total",
        values: {
          totalCess: data.totalCess,
          netAmount: data.netAmount,
          totalTaxAmount: data.totalTaxAmount,
          totalAmount: data.netAmount + data.totalTaxAmount,
        },
      },
      // Only show FOC if type is NOT 'PR'
      ...(type !== "PR"
        ? [
            {
              label: "FOC",
              values: {
                totalAmount: data.totalFocAmount ? data.totalFocAmount : 0,
              },
            },
          ]
        : []),
      ...(data["taxes"] && data.taxes.length > 0
        ? transformArray(data.taxes)
        : []),
      ...(data["charges"] && data.charges.length > 0
        ? transformArray(data.charges)
        : []),
      // only show Roundoff if type is GRN
      ...(type === "GRN"
        ? [
            {
              label: "Roundoff",
              values: {
                totalAmount: data.roundoff ? data.roundoff : 0,
              },
            },
          ]
        : []),
      {
        label: "Grand Total",
        values: {
          totalAmount: data.totalAmount ? data.totalAmount : 0,
        },
      },
    ],

    specialRows: [
      (columnCount) => generateAmountInWordsRow(data.totalAmount, columnCount),
    ],
  };
}

function transformArray(data) {
  return data.map((item) => ({
    label: item.name,
    values: {
      totalAmount: item.valueAmt,
    },
  }));
}

module.exports = {
  preparePOData,
  prepareTransferData,
  getLocationById,
  prepareGRNData,
  preparePRData,
};
