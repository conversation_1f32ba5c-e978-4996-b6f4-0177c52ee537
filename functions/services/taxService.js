const taxSchema = require("@/schema/taxSchema");
const taxRepo = require("@/repositories/taxRepo");
const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.TAXES);

const { checkConstraints } = require("./validationHelper"); // optional helper
const { propagateTaxName } = require("@/utils/updateLinkedData");

/**
 * Create a new tax
 */
async function createTax(tenantId, data) {
  // Validate input
  const { error, value: result } = taxSchema.validate(data, {
    abortEarly: false,
  });
  if (error) {
    throw new Error(error.details.map((d) => d.message).join(", "));
  }

  const { name, components = [] } = result;

  const nameNormalized = name.replace(/\s+/g, "").toLowerCase();

  // 🔍 Check for existing tax (case-sensitive as per your logic)
  const existingTax = await db
    .where("tenantId", "==", tenantId)
    .where("nameNormalized", "==", nameNormalized)
    .limit(1)
    .get();

  if (!existingTax.empty) {
    throw new Error(`Tax '${name}' already exists`);
  }

  // 🧩 Validate components
  const componentCount = components.length;

  if (componentCount === 1) {
    throw new Error("There must be either 0 or at least 2 components.");
  }

  if (componentCount > 0) {
    const names = components.map((c) => c.name.trim().toLowerCase());
    const duplicates = [
      ...new Set(names.filter((n, i) => names.indexOf(n) !== i)),
    ];
    if (duplicates.length > 0) {
      throw new Error(
        `Duplicate component names found: ${duplicates.join(", ")}`
      );
    }

    const key =
      result.valueType === "percentage" ? "valuePercentage" : "valueAmt";
    const total = components.reduce((sum, c) => sum + c[key], 0);
    if (componentCount >= 2 && total !== result[key]) {
      throw new Error(
        `Sum of component values (${total}) must equal the main tax value (${result[key]}).`
      );
    }
  }

  // ✅ Add tenantId
  result.tenantId = tenantId;
  result.nameNormalized = nameNormalized;

  // 💾 Create in repo
  return await taxRepo.createTax(result);
}

/**
 * Update an existing tax
 */
async function updateTax(tenantId, taxId, data) {
  // Validate data using Joi
  const { error, value: result } = taxSchema.validate(data, {
    abortEarly: false,
  });
  if (error) throw new Error(error.details.map((d) => d.message).join(", "));

  const { name, components = [] } = result;

  const nameNormalized = name.replace(/\s+/g, "").toLowerCase();

  // 🔍 Check for duplicate tax name (exclude current taxId)
  const existingTax = await db
    .where("tenantId", "==", tenantId)
    .where("nameNormalized", "==", nameNormalized)
    .limit(1)
    .get();

  if (!existingTax.empty && existingTax.docs[0].id !== taxId) {
    throw new Error(`Tax '${name}' already exists`);
  }

  // 🧩 Validate components
  const componentCount = components.length;

  if (componentCount === 1) {
    throw new Error("There must be either 0 or at least 2 components.");
  }

  if (componentCount > 0) {
    const names = components.map((c) => c.name.trim().toLowerCase());
    const duplicates = [
      ...new Set(names.filter((n, i) => names.indexOf(n) !== i)),
    ];
    if (duplicates.length > 0) {
      throw new Error(
        `Duplicate component names found: ${duplicates.join(", ")}`
      );
    }

    const key =
      result.valueType === "percentage" ? "valuePercentage" : "valueAmt";
    const total = components.reduce((sum, c) => sum + c[key], 0);
    if (componentCount >= 2 && total !== result[key]) {
      throw new Error(
        `Sum of component values (${total}) must equal the main tax value (${result[key]}).`
      );
    }
  }

  // ✅ Add tenantId
  result.tenantId = tenantId;

  await propagateTaxName(taxId, name);

  // 💾 Update in repo
  return await taxRepo.updateTax(taxId, result);
}

/**
 * Get tax by ID (tenant validated)
 */
async function getTaxById(tenantId, taxId) {
  const tax = await taxRepo.getTaxById(tenantId, taxId);
  if (!tax) throw new Error("Tax not found");
  return tax;
}

/**
 * Get all taxes for a tenant
 */
async function getTaxes(tenantId) {
  return await taxRepo.getTaxes(tenantId);
}

/**
 * Activate a tax
 * No extra validation needed
 */
async function activateTax(tenantId, taxId) {
  await taxRepo.updateTaxStatus(tenantId, taxId, true);
  return { activeStatus: true };
}

/**
 * Inactivate a tax
 * Runs validation before deactivation if needed
 */
async function deactivateTax(tenantId, taxId) {
  await checkConstraints("taxes", tenantId, taxId);

  await taxRepo.updateTaxStatus(tenantId, taxId, false);
  return { activeStatus: false };
}

module.exports = {
  createTax,
  updateTax,
  getTaxById,
  getTaxes,
  activateTax,
  deactivateTax,
};
