const {
    updateHouseUnitStatus
} = require("@/repositories/houseUnitsRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for House unit operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a House unit
 * No validation needed for activation
 * @param {string} houseunitId
 * @returns {object} activeStatus
 */
async function activateHouseunit(tenantId, houseunitId) {
    // Update status to active
    await updateHouseUnitStatus(tenantId, houseunitId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a house unit
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} houseunitId
 * @returns {object} activeStatus
 */
async function deactivateHouseunit(tenantId, houseunitId, houseunitSymbol) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("houseUnits", tenantId, houseunitSymbol);

    // Update status to inactive
    await updateHouseUnitStatus(tenantId, houseunitId, false);
    return { activeStatus: false };
}

module.exports = {
    activateHouseunit,
    deactivateHouseunit
};
