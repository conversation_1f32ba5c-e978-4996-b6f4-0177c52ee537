const {
    updateItemStatus
} = require("@/repositories/itemRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Item operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a Item
 * No validation needed for activation
 * @param {string} itemId
 * @returns {object} activeStatus
 */
async function activateItem(tenantId, itemId) {
    // Update status to active
    await updateItemStatus(tenantId, itemId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} itemId
 * @returns {object} activeStatus
 */
async function deactivateItem(tenantId, itemId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("inventoryItems", tenantId, itemId);

    // Update status to inactive
    await updateItemStatus(tenantId, itemId, false);
    return { activeStatus: false };
}

module.exports = {
    activateItem,
    deactivateItem
};
