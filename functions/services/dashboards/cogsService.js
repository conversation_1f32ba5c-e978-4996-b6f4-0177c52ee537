// services/dashboards/cogsService.js

/**
 * COGS Dashboard Service
 * ----------------------
 * Returns tenant-specific Cost of Goods Sold summary.
 */

exports.getSummary = async (tenantId, payload) => {
  return {
    "totalRevenue": 2456789.50,
    "totalCogs": 721450.30,
    "cogsRatio": 29.4,
    "grossProfit": 1735339.20,

    "performanceAnalysis": [
      { "department": "Kitchen", "revenue": 1450000.00, "cogs": 425000.00, "ratio": 29.3 },
      { "department": "Bar", "revenue": 650000.00, "cogs": 192000.00, "ratio": 29.5 },
      { "department": "Bakery", "revenue": 230000.00, "cogs": 69000.00, "ratio": 30.0 },
      { "department": "Pantry", "revenue": 62789.50, "cogs": 21450.30, "ratio": 34.1 },
      { "department": "Outdoor Counter", "revenue": 35000.00, "cogs": 13800.00, "ratio": 39.4 }
    ],

    "actionDashboard": [
      { "department": "Kitchen", "status": "Good", "ratio": 29.3 },
      { "department": "Bar", "status": "Good", "ratio": 29.5 },
      { "department": "Bakery", "status": "Watch", "ratio": 30.0 },
      { "department": "Pantry", "status": "Alert", "ratio": 34.1 },
      { "department": "Outdoor Counter", "status": "Alert", "ratio": 39.4 }
    ],

    "reconciliation": [
      {
        "department": "Kitchen",
        "opening": 800000.00,
        "purchase": 400000.00,
        "transferIn": 95000.00,
        "transferOut": 45000.00,
        "closing": 470000.00,
        "consumption": 880000.00
      }
    ],

    "transferWorkareas": [
      { "workarea": "Kitchen", "transferIn": 95000.00, "transferOut": 45000.00 },
      { "workarea": "Bar", "transferIn": 30000.00, "transferOut": 21000.00 },
      { "workarea": "Bakery", "transferIn": 15000.00, "transferOut": 9000.00 }
    ],

    "transferStore": [
      {
        "location": "Main Store",
        "purchase": 410000.00,
        "ibtIn": 25000.00,
        "ibtOut": 15000.00,
        "returns": 10000.00,
        "spoilage": 5000.00
      }
    ],

    "costAnalysis": [
      { "department": "Kitchen", "sales": 1450000.00, "cogs": 425000.00, "ratio": 29.3, "percentage": 58.9 },
      { "department": "Bar", "sales": 650000.00, "cogs": 192000.00, "ratio": 29.5, "percentage": 26.6 },
      { "department": "Bakery", "sales": 230000.00, "cogs": 69000.00, "ratio": 30.0, "percentage": 9.6 },
      { "department": "Pantry", "sales": 62789.50, "cogs": 21450.30, "ratio": 34.1, "percentage": 3.0 },
      { "department": "Outdoor Counter", "sales": 35000.00, "cogs": 13800.00, "ratio": 39.4, "percentage": 1.9 }
    ]
  };
};
