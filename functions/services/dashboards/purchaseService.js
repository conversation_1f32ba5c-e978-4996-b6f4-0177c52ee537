// services/dashboards/purchaseService.js

const { paiseToRupee } = require("@/utils/money");
const {
  FirestoreDateHelper: FD,
  DATE_FORMAT,
} = require("@/helpers/dateHelper");
const {
  validateAndPrepareFilters,
  requiresLedgerData,
  filterItem,
} = require("@/helpers/reportHelper");

const { fetchGRNReportData } = require("@/repositories/procurementReportRepo");

/**
 * Purchase Dashboard Service
 * ---------------------------
 * Returns tenant-specific purchase summary.
 */
exports.getSummary = async (tenantId, payload) => {
  // initialize
  const result = {
    // metric data
    total: 0,
    baseAmount: 0,
    discount: 0,
    charge: 0,
    tax: 0,
    grn: 0,
    foc: 0,

    // metrics of unique items
    vendors: 0,
    uniqueItems: 0,
    locations: 0,
    hsnCompliant: 0,
    returnPercent: 0,

    // graph data
    topVendors: [],
    categoryPurchaseDistribution: [],
    subcategoryPurchaseDistribution: [],
    highValueItems: [],
    locationPurchaseAnalysis: [],
    dailyPurchaseTrends: [],
  };
  const topVendors = new Map();
  const categoryPurchaseDistribution = new Map();
  const subcategoryPurchaseDistribution = new Map();
  const highValueItems = new Map();
  const dailyPurchaseTrends = new Map();
  const locationPurchaseAnalysis = new Map();

  // format grn level data
  const formatGrn = (grn) => {
    result.grn += 1;

    // calculate metrics
    result.total += grn.totalAmount || 0;
    result.baseAmount += grn.grossAmount || 0;
    result.discount += grn.totalDiscount || 0;
    result.tax += grn.totalTaxAmount || 0;
    result.charge += grn.totalChargeAmount || 0;
    result.foc += grn.totalFocAmount || 0;

    const timestamp = FD.toJSDate(grn.grnDate).getTime();
    getGraphData(dailyPurchaseTrends, timestamp, grn.grnDate, grn.totalAmount); // daily aggregation
    getGraphData(topVendors, grn.vendorId, grn.vendorName, grn.totalAmount); // vendor aggregation
    getGraphData(
      locationPurchaseAnalysis,
      grn.location.id,
      grn.location.name,
      grn.totalAmount
    );

    grn.items?.forEach((item) => {
      getGraphData(
        categoryPurchaseDistribution,
        item.categoryId,
        item.categoryName,
        item.totalAmount
      ); // category aggregation
      getGraphData(
        subcategoryPurchaseDistribution,
        item.subcategoryId,
        item.subcategoryName,
        item.totalAmount
      ); // sub category aggregation
      getGraphData(
        highValueItems,
        item.itemId,
        item.itemName,
        item.totalAmount
      ); // item aggregation
    });
  };

  // format while item filter present
  const formatLedgerItem = (grn) => {
    const timestamp = FD.toJSDate(grn.grnDate).getTime();
    const items = grn.items?.filter((item) =>
      filterItem(payload.filters, item)
    );
    if (!items.length) {
      return;
    }
    // data.totalAmount - data.totalFocAmount;
    result.grn += 1;
    items.forEach((item) => {
      result.total += item.totalAmount || 0 - item.totalFocAmount || 0;
      result.baseAmount += item.grossAmount || 0;
      result.discount += item.totalDiscount || 0;
      result.tax += item.totalTaxAmount || 0;
      result.charge += item.totalChargeAmount || 0;
      result.foc += item.totalFocAmount || 0;

      getGraphData(
        dailyPurchaseTrends,
        timestamp,
        grn.grnDate,
        item.totalAmount
      ); // daily aggregation
      getGraphData(topVendors, grn.vendorId, grn.vendorName, item.totalAmount); // vendor aggregation
      formatGraphData(
        locationPurchaseAnalysis,
        grn.location.id,
        grn.location.name,
        item.totalAmount
      );

      getGraphData(
        categoryPurchaseDistribution,
        item.categoryId,
        item.categoryName,
        item.totalAmount
      ); // category aggregation
      getGraphData(
        subcategoryPurchaseDistribution,
        item.subcategoryId,
        item.subcategoryName,
        item.totalAmount
      ); // sub category aggregation
      getGraphData(
        highValueItems,
        item.itemId,
        item.itemName,
        item.totalAmount
      ); // item aggregation
    });
  };

  const fromLedger = requiresLedgerData(payload.filters);
  const _filters = validateAndPrepareFilters(tenantId, payload.filters);

  // fetch & form data
  const snapshot = await fetchGRNReportData(tenantId, _filters);
  for (const doc of snapshot.docs) {
    const grn = doc.data();
    if (fromLedger) {
      formatLedgerItem(grn);
      continue;
    }

    formatGrn(grn);
  }

  // convert paiseToRupee
  const formatAmt = (v) => paiseToRupee(v || 0);
  result.total = formatAmt(result.total);
  result.baseAmount = formatAmt(result.baseAmount);
  result.discount = formatAmt(result.discount);
  result.charge = formatAmt(result.charge);
  result.tax = formatAmt(result.tax);
  result.foc = formatAmt(result.foc);

  // get unique count
  const getUnique = (map) => Array.from(map.values()).length;
  result.vendors = getUnique(topVendors);
  result.uniqueItems = getUnique(highValueItems);
  result.locations = getUnique(locationPurchaseAnalysis);
  // result.hsnCompliant = getUnique(grn.hsnCompliant);
  // result.returnPercent += grn.returnPercent;

  // format graph data
  result.topVendors = formatGraphData(topVendors);
  result.categoryPurchaseDistribution = formatGraphData(
    categoryPurchaseDistribution
  );
  result.subcategoryPurchaseDistribution = formatGraphData(
    subcategoryPurchaseDistribution
  );
  result.highValueItems = formatGraphData(highValueItems);
  result.locationPurchaseAnalysis = formatGraphData(locationPurchaseAnalysis);

  // daily trends format date
  result.dailyPurchaseTrends = Array.from(dailyPurchaseTrends.values())
    .sort((a, b) => a.id - b.id)
    .map((v) => ({
      id: v.id,
      title: FD.toJSDate(v.title).getDate(),
      label: FD.toFormattedDate(v.title, DATE_FORMAT.DATE_ONLY),
      count: v.count,
      amount: formatAmt(v.amount),
    }));

  return result;
};

const getGraphData = (map, id, title, value) => {
  const entry = map.get(id) || {
    id: id || "unknown",
    title: title || "Unknown",
    label: null,
    count: 0,
    amount: 0,
  };
  entry.count += 1;
  entry.amount += value || 0;
  map.set(id, entry);
  return map;
};

const formatGraphData = (map) => {
  return Array.from(map.values())
    .sort((a, b) => b.amount - a.amount)
    .map((v) => ({
      id: v.id,
      title: v.title,
      label: v.label,
      count: v.count,
      amount: paiseToRupee(v.amount || 0),
    }));
};
