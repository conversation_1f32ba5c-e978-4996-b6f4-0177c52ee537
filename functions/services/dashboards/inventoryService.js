// services/dashboards/inventoryService.js
const {
  fetchTenantsReportData
} = require("@/repositories/stockMovementReportRepo");

const { validateAndPrepareFilters } = require("@/helpers/reportHelper");

const { paiseToRupee } = require("@/utils/money");

/**
 * Inventory Dashboard Service
 * ---------------------------
 * Returns tenant-specific inventory summary.
 */

exports.getSummary = async (tenantId, payload) => {
  // filters = req.body, tenantId = from route params

  // initialize
  const result = {
    // metric data

    closingStock: 0,
    openingStock: 0,
    purchasesVendor: 0,
    ibtIn: 0,
    ibtOut: 0,
    indentConsumed: 0,
    spoilageLoss: 0,
    adjustments: 0,
    systemClosing: 0,
    physicalClosing: 0,
    closingAdjustment: 0,
    variance: 0,

    //qty
    openingQty: 0,
    purchaseQty: 0,
    ibtInQty: 0,
    ibtOutQty: 0,
    closingQty: 0,
    adjustmentsQty: 0,
    spoilageQty: 0,

    // graph data
    financialOverview: [],
    rupeeValueTracking: [],
    varianceAction: [],
    riskIntelligence: [],
    topInvestmentItems: [],
    categoryPortfolio: []
  };

  const financialOverview = new Map();

  const rupeeValueTracking = new Map();
  const varianceActionMap = new Map();
  const riskIntelligenceMap = new Map();
  const topInvestmentItemsMap = new Map();
  const categoryPortfolioMap = new Map();

  const formatAmt = (v) => paiseToRupee(v || 0);

  // validation
  const formatInventory = (data) => {
    console.log(data, "data");

    const finalClosing = data.finalClosing?.totalValue || 0;
    const opening = data.opening?.totalValue || 0;
    const purchase = data.purchase?.totalValue || 0;
    const transferIn = data.transferIn?.totalValue || 0;
    const transferOut = data.transferOut?.totalValue || 0;
    const consumption = data.consumption?.totalValue || 0;
    const spoilage = data.spoilage?.totalValue || 0;
    const adjustment = data.adjustment?.totalValue || 0;
    const systemClosing = data.systemClosing?.totalValue || 0;
    const physicalClosing = data.physicalClosing?.totalValue || 0;
    const closingAdj = data.closingAdjustment?.totalValue || 0;

    result.closingStock += finalClosing;
    result.openingStock += opening;
    result.purchasesVendor += purchase;
    result.ibtIn += transferIn;
    result.ibtOut += transferOut;
    result.indentConsumed += consumption;
    result.spoilageLoss += spoilage;
    result.adjustments += adjustment;
    result.systemClosing += systemClosing;
    result.physicalClosing += physicalClosing;
    result.closingAdjustment += closingAdj;

    result.variance += systemClosing - physicalClosing;

    result.openingQty += data.opening?.qty || 0;
    result.purchaseQty += data.purchase?.qty || 0;
    result.ibtInQty += data.transferIn?.qty || 0;
    result.ibtOutQty += data.transferOut?.qty || 0;
    result.closingQty += data.finalClosing?.qty || 0;
    result.adjustmentsQty += data.adjustment?.qty || 0;
    result.spoilageQty += data.spoilage?.qty || 0;

    result.financialOverview = [
      {
        title: "Opening Stock",
        count: result.openingQty || 0,
        amount: formatAmt(result.openingStock)
      },
      {
        title: "Purchases",
        count: result.purchaseQty || 0,
        amount: formatAmt(result.purchasesVendor)
      },
      {
        title: "Transfer In",
        count: result.ibtInQty || 0,
        amount: formatAmt(result.ibtIn)
      },
      {
        title: "Transfer Out",
        count: result.ibtOutQty || 0,
        amount: formatAmt(result.ibtOut)
      },
      {
        title: "Closing Stock",
        count: result.closingQty || 0,
        amount: formatAmt(result.closingStock)
      },
      {
        title: "Spoilage",
        count: result.spoilageQty || 0,
        amount: formatAmt(result.spoilageLoss)
      },
      {
        title: "Adjustments",
        count: result.adjustmentsQty || 0,
        amount: formatAmt(result.adjustments)
      }
    ];

    accumulateRupeeFlow(rupeeValueTracking, data);
    accumulateVarianceAction(varianceActionMap, data);
    accumulateRiskIntelligence(riskIntelligenceMap, data);
    accumulateTopInvestmentItems(topInvestmentItemsMap, data);
    accumulateCategoryPortfolio(categoryPortfolioMap, data);
  };

  const _filters = validateAndPrepareFilters(tenantId, payload.filters);
  // fetch & form data
  const data = await fetchTenantsReportData(tenantId, _filters);
  for (const d of data) {
    formatInventory(d);
  }

  result.closingStock = formatAmt(result.closingStock);
  result.openingStock = formatAmt(result.openingStock);
  result.purchasesVendor = formatAmt(result.purchasesVendor);
  result.ibtIn = formatAmt(result.ibtIn);
  result.ibtOut = formatAmt(result.ibtOut);
  result.indentConsumed = formatAmt(result.indentConsumed);
  result.spoilageLoss = formatAmt(result.spoilageLoss);
  result.adjustments = formatAmt(result.adjustments);
  result.systemClosing = formatAmt(result.systemClosing);
  result.physicalClosing = formatAmt(result.physicalClosing);
  result.closingAdjustment = formatAmt(result.closingAdjustment);
  result.variance = formatAmt(result.variance);

  result.rupeeValueTracking = formatGraphData(rupeeValueTracking);

  result.varianceAction = Array.from(varianceActionMap.values())
    .sort((a, b) => Math.abs(b.amount) - Math.abs(a.amount))
    .slice(0, 10);

  result.riskIntelligence = Array.from(riskIntelligenceMap.values())
    .sort((a, b) => b.agingDays - a.agingDays)
    .slice(0, 10);

  result.topInvestmentItems = formatGraphData(topInvestmentItemsMap);

  result.categoryPortfolio = formatGraphData(categoryPortfolioMap);

  return result;
};

const accumulateRupeeFlow = (map, data) => {
  const id = data.categoryId || "unknown";
  const title = data.categoryName || "Unknown";

  const entry = map.get(id) || {
    id,
    title,
    inflow: { count: 0, amount: 0 },
    outflow: { count: 0, amount: 0 }
  };

  // inflow
  const inflowAmount =
    (data.purchase?.totalValue || 0) + (data.transferIn?.totalValue || 0);

  const inflowCount = (data.purchase?.qty || 0) + (data.transferIn?.qty || 0);

  // outflow
  const outflowAmount =
    (data.consumption?.totalValue || 0) +
    (data.transferOut?.totalValue || 0) +
    (data.spoilage?.totalValue || 0);

  const outflowCount =
    (data.consumption?.qty || 0) +
    (data.transferOut?.qty || 0) +
    (data.spoilage?.qty || 0);

  entry.inflow.amount += inflowAmount;
  entry.inflow.count += inflowCount;

  entry.outflow.amount += outflowAmount;
  entry.outflow.count += outflowCount;

  map.set(id, entry);
};

const accumulateVarianceAction = (map, data) => {
  const id = data.subcategoryId;
  const title = data.subcategoryName;

  const systemQty = data.systemClosing?.qty || 0;
  const physicalQty = data.physicalClosing?.qty || 0;
  const varianceQty = physicalQty - systemQty;

  const systemAmt = data.systemClosing?.totalValue || 0;
  const physicalAmt = data.physicalClosing?.totalValue || 0;
  const varianceAmt = physicalAmt - systemAmt;

  if (!varianceQty && !varianceAmt) return;

  map.set(id, {
    title,
    system: systemQty,
    physical: physicalQty,
    variance: varianceQty,
    amount: paiseToRupee(varianceAmt)
  });
};

const formatGraphData = (map) => {
  return Array.from(map.values())
    .sort((a, b) => b.inflow.amount - a.inflow.amount)
    .map((v) => ({
      title: v.title,
      inflow: {
        count: v.inflow.count,
        amount: paiseToRupee(v.inflow.amount || 0)
      },
      outflow: {
        count: v.outflow.count,
        amount: paiseToRupee(v.outflow.amount || 0)
      }
    }));
};

const accumulateRiskIntelligence = (map, data) => {
  if (!data.createdAt) return;

  const agingDays = Math.floor(
    (Date.now() - data.createdAt.toMillis()) / (1000 * 60 * 60 * 24)
  );

  let flag = null;
  if (agingDays >= 45) flag = "critical";
  else if (agingDays >= 20) flag = "watch";
  else return;

  const amount = data.finalClosing?.totalValue || 0;
  const qty = data.finalClosing?.qty || 0;

  if (!amount && !qty) return;

  map.set(data.inventoryItemId, {
    title: data.inventoryItemName,
    flag,
    agingDays,
    qty,
    amount: paiseToRupee(amount)
  });
};

const accumulateTopInvestmentItems = (map, data) => {
  const id = data.inventoryItemId;
  const title = data.inventoryItemName;

  const entry = map.get(id) || {
    title,
    inflow: { count: 0, amount: 0 },
    outflow: { count: 0, amount: 0 }
  };

  entry.inflow.count += (data.purchase?.qty || 0) + (data.transferIn?.qty || 0);
  entry.inflow.amount +=
    (data.purchase?.totalValue || 0) + (data.transferIn?.totalValue || 0);

  entry.outflow.count +=
    (data.consumption?.qty || 0) +
    (data.transferOut?.qty || 0) +
    (data.spoilage?.qty || 0);
  entry.outflow.amount +=
    (data.consumption?.totalValue || 0) +
    (data.transferOut?.totalValue || 0) +
    (data.spoilage?.totalValue || 0);

  map.set(id, entry);
};

const accumulateCategoryPortfolio = (map, data) => {
  const id = data.categoryId;
  const title = data.categoryName;

  const entry = map.get(id) || {
    title,
    inflow: { count: 0, amount: 0 },
    outflow: { count: 0, amount: 0 }
  };

  entry.inflow.count += (data.purchase?.qty || 0) + (data.transferIn?.qty || 0);
  entry.inflow.amount +=
    (data.purchase?.totalValue || 0) + (data.transferIn?.totalValue || 0);

  entry.outflow.count +=
    (data.consumption?.qty || 0) +
    (data.transferOut?.qty || 0) +
    (data.spoilage?.qty || 0);
  entry.outflow.amount +=
    (data.consumption?.totalValue || 0) +
    (data.transferOut?.totalValue || 0) +
    (data.spoilage?.totalValue || 0);

  map.set(id, entry);
};
