const {
    updateLocationStatus
} = require("@/repositories/locationRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Location operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a location
 * No validation needed for activation
 * @param {string} vendorId
 * @returns {object} activeStatus
 */
async function activateLocation(tenantId, locationId) {
    // Update status to active
    await updateLocationStatus(tenantId, locationId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} locationId
 * @returns {object} activeStatus
 */
async function deactivateLocation(tenantId, locationId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("locations", tenantId, locationId);

    // Update status to inactive
    await updateLocationStatus(tenantId, locationId, false);
    return { activeStatus: false };
}

module.exports = {
    activateLocation,
    deactivateLocation
};
