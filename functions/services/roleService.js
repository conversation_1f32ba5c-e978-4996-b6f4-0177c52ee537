const {
    updateRoleStatus
} = require("@/repositories/roleRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Vendor operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a vendor
 * No validation needed for activation
 * @param {string} roleId
 * @returns {object} activeStatus
 */
async function activateRole(tenantId, roleId) {
    // Update status to active
    await updateRoleStatus(tenantId, roleId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} roleId
 * @returns {object} activeStatus
 */
async function deactivateRole(tenantId, roleId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("roles", tenantId, roleId);

    // Update status to inactive
    await updateRoleStatus(tenantId, roleId, false);
    return { activeStatus: false };
}

module.exports = {
    activateRole,
    deactivateRole
};
