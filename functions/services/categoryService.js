const {
    updateCategoryStatus
} = require("@/repositories/categoryRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Category operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a Category
 * No validation needed for activation
 * @param {string} categoryId
 * @returns {object} activeStatus
 */
async function activateCategory(tenantId, categoryId) {
    // Update status to active
    await updateCategoryStatus(tenantId, categoryId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} categoryId
 * @returns {object} activeStatus
 */
async function deactivateCategory(tenantId, categoryId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("categories", tenantId, categoryId);

    // Update status to inactive
    await updateCategoryStatus(tenantId, categoryId, false);
    return { activeStatus: false };
}

module.exports = {
    activateCategory,
    deactivateCategory
};
