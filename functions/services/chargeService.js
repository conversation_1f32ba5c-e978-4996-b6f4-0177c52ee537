const chargeSchema = require("@/schema/chargeSchema");
const chargeRepo = require("@/repositories/chargeRepo");
const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.CHARGES);

const { checkConstraints } = require("./validationHelper"); // optional helper

/**
 * Create a new charge
 */
async function createCharge(tenantId, data) {
  // Validate input
  const { error, value } = chargeSchema.validate(data, { abortEarly: false });
  if (error) {
    throw new Error(error.details.map((d) => d.message).join(", "));
  }

  const { name } = value;

  const nameNormalized = name.replace(/\s+/g, "").toLowerCase();

  // 🔍 Check for existing charge
  const existingCharge = await db
    .where("tenantId", "==", tenantId)
    .where("nameNormalized", "==", nameNormalized)
    .limit(1)
    .get();

  if (!existingCharge.empty) {
    throw new Error(`Charge '${name}' already exists`);
  }

  // ✅ Add tenantId and nameNormalized
  value.tenantId = tenantId;
  value.nameNormalized = nameNormalized;

  // Ensure type is always "charge"
  value.type = "charge";

  // Ensure valueAmt defaults to 0 if not provided
  if (value.valueAmt === undefined) {
    value.valueAmt = 0;
  }

  // 💾 Create in repo
  return await chargeRepo.createCharge(value);
}

/**
 * Update an existing charge
 */
async function updateCharge(tenantId, chargeId, data) {
  // Validate data using Joi
  const { error, value } = chargeSchema.validate(data, { abortEarly: false });
  if (error) throw new Error(error.details.map((d) => d.message).join(", "));

  const { name } = value;

  const nameNormalized = name.replace(/\s+/g, "").toLowerCase();

  // 🔍 Check for duplicate charge name (exclude current chargeId)
  const existingCharge = await db
    .where("tenantId", "==", tenantId)
    .where("nameNormalized", "==", nameNormalized)
    .limit(1)
    .get();

  if (!existingCharge.empty && existingCharge.docs[0].id !== chargeId) {
    throw new Error(`Charge '${name}' already exists`);
  }

  // ✅ Add tenantId and nameNormalized
  value.tenantId = tenantId;
  value.nameNormalized = nameNormalized;

  // Ensure type is always "tax"
  value.type = "charge";

  // 💾 Update in repo
  return await chargeRepo.updateCharge(chargeId, value);
}

/**
 * Get charge by ID (tenant validated)
 */
async function getChargeById(tenantId, chargeId) {
  const charge = await chargeRepo.getChargeById(tenantId, chargeId);
  if (!charge) throw new Error("Charge not found");
  return charge;
}

/**
 * Get all charges for a tenant
 */
async function getCharges(tenantId) {
  return await chargeRepo.getCharges(tenantId);
}

/**
 * Activate a charge
 * No extra validation needed
 */
async function activateCharge(tenantId, chargeId) {
  await chargeRepo.updateChargeStatus(tenantId, chargeId, true);
  return { activeStatus: true };
}

/**
 * Deactivate a charge
 * Runs validation before deactivation if needed
 */
async function deactivateCharge(tenantId, chargeId) {
  await checkConstraints("charges", tenantId, chargeId);

  await chargeRepo.updateChargeStatus(tenantId, chargeId, false);
  return { activeStatus: false };
}

module.exports = {
  createCharge,
  updateCharge,
  getChargeById,
  getCharges,
  activateCharge,
  deactivateCharge,
};
