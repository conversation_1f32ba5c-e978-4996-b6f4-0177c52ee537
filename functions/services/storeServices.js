const {
    updateStoreStatus
} = require("@/repositories/storeRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Store operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a store
 * No validation needed for activation
 * @param {string} storeId
 * @returns {object} activeStatus
 */
async function activateStore(tenantId, storeId) {
    // Update status to active
    await updateStoreStatus(tenantId, storeId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a store
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} storeId
 * @returns {object} activeStatus
 */
async function deactivateStore(tenantId, storeId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("stores", tenantId, storeId);

    // Update status to inactive
    await updateStoreStatus(tenantId, storeId, false);
    return { activeStatus: false };
}

module.exports = {
    activateStore,
    deactivateStore
};
