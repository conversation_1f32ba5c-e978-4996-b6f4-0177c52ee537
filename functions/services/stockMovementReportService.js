const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const {
  validateAndPrepareFilters,
  constructColumns,
  validateIdentityFilters,
} = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

const {
  fetchTransferReportData,
  fetchTenantsReportData,
  fetchReportGroupsData,
} = require("@/repositories/stockMovementReportRepo");
const { paiseToRupee, truncateNumber } = require("@/utils/money");

/**
 * NewProcurementReport
 * --------------------
 * Identifies collection, determines aggregateType,
 * fetches snapshot immediately on construction,
 * and exposes result scaffolding + context.
 */
class NewProcurementReport {
  #headers = [];
  constructor(tenantId, payload, reportType, identity) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;

    const { filters = {}, columns = [] } = payload || {};

    let _filters = validateAndPrepareFilters(tenantId, filters);
    // ✅ identity-aware filtering
    if (identity) {
      _filters = validateIdentityFilters(identity, _filters);
    }

    // Expose everything upfront
    this._options = {
      _filters,
      columns,
      reportType,
      aggregateType: reportInfo.aggregateType,
    };

    // Create default result skeleton
    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {},
      payload,
      _meta: null,
    };

    // fetch immediately (sync-like)
    // store the promise — controller will await it
    const { fetchFn } = this.#identify(this._options._filters, reportInfo.id);
    this._snapPromise = fetchFn(tenantId, this._options._filters);
  }

  /**
   * Resolve Firestore snapshot
   */
  async snap() {
    this._snap = await this._snapPromise;
    return this._snap;
  }

  generate(resultType) {
    this.result.headers = constructColumns(
      this.#headers,
      this._options.columns,
      this.result._meta,
    );
    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result);
      default:
        return this.result;
    }
  }

  /**
   * Identify collection and fetch function.
   * @private
   */
  #identify(filters, reportType) {
    switch (reportType) {
      case "item-wise-stock-movements":
      case "physical-closing":
      case "system-closing":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters),
        };

      case "flr-report":
        return {
          fetchFn: () => Promise.resolve([]),
        };

      case "store-variance":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, {
              defaultWorkArea: true,
            }),
        };

      case "inventory-consumption":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, {
              defaultWorkArea: false,
            }),
        };

      case "bar-variance":
        return {
          fetchFn: (tenantId, filters) =>
            fetchTenantsReportData(tenantId, filters, {
              defaultWorkArea: false,
              barWorkArea: true,
            }),
        };

      case "cost-of-issue-vs-revenue":
        return {
          fetchFn: fetchReportGroupsData,
        };

      default:
        return {
          fetchFn: fetchTransferReportData,
        };
    }
  }
}

/**
 * Transfer List Report
 * ---------------------
 */
const getTransferListReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.TRANSFER_LIST,
    identity,
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    const result = {
      transferNo: data.transferNumber,
      fromLocation: data.issuer?.locationName,
      fromWorkArea: data.issuer?.name,
      toLocation: data.requester?.locationName,
      toWorkArea: data.requester?.name,
      createdAt: FD.toFormattedDate(data.requestedBy?.time),
      createdDate: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.DATE_ONLY,
      ),
      createdTime: FD.toFormattedDate(
        data.requestedBy?.time,
        DATE_FORMAT.TIME_ONLY,
      ),
      createdBy: data.requestedBy?.name,
      dispatchStatus: data.dispatchStatus,
      receiveStatus: data.receiveStatus,
    };

    report.result.data.push(result);
    report.result.totalRow.totalValue += data.totalValue;
  });

  return report.generate(resultType);
};

/**
 * Dispatch Transfer Report
 * ---------------------
 */
const getDispatchTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DISPATCH_TRANSFER,
    identity,
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    if (!data.timeLine) return;

    data.timeLine.forEach((timeline) => {
      const result = {
        transferNo: data.transferNumber,
        dispatchNo: timeline.dispatchNo,
        dispatchedBy: timeline.dispatchedBy?.name,
        dispatchedDate: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.DATE_ONLY,
        ),
        dispatchedTime: FD.toFormattedDate(
          timeline.dispatchedBy?.time,
          DATE_FORMAT.TIME_ONLY,
        ),
        // dispatchedAt: FD.toFormattedDate(timeline.dispatchedBy?.time),
        status: timeline.status,
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

/**
 * Detailed Transfer Report
 * ---------------------
 */
const getDetailedTransferReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DETAILED_TRANSFER,
    identity,
  );
  const snapshot = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalDispatchCost: 0,
    totalReceiveCost: 0,
  };

  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const totalDispatchCost = paiseToRupee(item.totalValueDispatch) || 0;
      // const totalReceiveCost = paiseToRupee(item.totalValueReceive) || 0;
      const cost =
        item?.dispatchedQuantity > 0
          ? truncateNumber(totalDispatchCost / item.dispatchedQuantity)
          : item?.unitCost || 0;

      const totalReceiveCost = truncateNumber(cost * item.receivedQuantity);

      const reasons = (data.timeLine || []).flatMap((s) =>
        (s.items || [])
          .filter((i) => i.itemId === item.itemId && i.reason)
          .map((i) => i.reason.trim()),
      );

      const shortageReason = [...new Set(reasons)].join(", ") || "-";

      const result = {
        transferNo: data.transferNumber,
        fromLocation: data.issuer?.locationName,
        fromWorkArea: data.issuer?.name,
        toLocation: data.requester?.locationName,
        toWorkArea: data.requester?.name,
        createdAt: FD.toFormattedDate(data.requestedBy?.time),
        createdDate: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.DATE_ONLY,
        ),
        createdTime: FD.toFormattedDate(
          data.requestedBy?.time,
          DATE_FORMAT.TIME_ONLY,
        ),
        createdBy: data.requestedBy?.name,
        itemName: item.itemName,
        itemCode: item.itemCode,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        unitCost: cost,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        receivedQuantity: item.receivedQuantity || 0,
        shortageQuantity: item.shortageQuantity || 0,
        totalDispatchCost,
        totalReceiveCost,
        shortageReason,
      };
      report.result.data.push(result);

      report.result.totalRow.totalDispatchCost += totalDispatchCost;
      report.result.totalRow.totalReceiveCost += totalReceiveCost;

      report.result.totalRow.totalDispatchCost = truncateNumber(
        report.result.totalRow.totalDispatchCost,
      );
      report.result.totalRow.totalReceiveCost = truncateNumber(
        report.result.totalRow.totalReceiveCost,
      );
    });
    report.result.totalRow.totalValue += data.totalValue;
  });
  return report.generate(resultType);
};

// cost-of-issue-vs-revenue
const getCostOfIssueVsRevenueReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.COST_OF_ISSUE_VS_REVENUE,
    identity,
  );

  const { groups: groupsData, othersCost: otherCategoryCost } =
    await report.snap();

  report.result.data = [];
  report.result.expand = true;

  // Initialize totalRow as an array to hold multiple footer rows
  report.result.totalRow = [
    {
      group: "OVERALL",
      sales: 0,
      costOfIssue: 0,
      costingPercentage: 0,
    },
  ];

  groupsData.forEach((data) => {
    const groupName = data.name?.toUpperCase();
    const groupSales = data?.netSales ? paiseToRupee(data.netSales) : 0;
    let groupCost = 0;

    const subItems = data.category.map((cat) => {
      const cost = paiseToRupee(cat.cost) || 0;
      groupCost += cost;

      return {
        group: cat.name,
        sales: "-",
        costOfIssue: cost,
        costingPercentage: groupSales
          ? truncateNumber((cost / groupSales) * 100)
          : 0,
      };
    });

    const groupCostingPercentage = groupSales
      ? truncateNumber((groupCost / groupSales) * 100)
      : 0;

    report.result.totalRow[0].sales += groupSales;
    report.result.totalRow[0].costOfIssue += groupCost;

    report.result.data.push({
      id: data.id,
      group: groupName,
      sales: groupSales,
      costOfIssue: groupCost,
      costingPercentage: groupCostingPercentage,
      subItems,
    });
  });

  // Add "Others" row
  const othersCost = paiseToRupee(otherCategoryCost);
  report.result.totalRow.push({
    group: "Others",
    sales: "-",
    costOfIssue: othersCost,
    costingPercentage: "-",
    isOthers: true,
  });

  // Add "Total Cost of Issue" row
  const totalCostOfIssue = report.result.totalRow[0].costOfIssue + othersCost;
  report.result.totalRow.push({
    group: "Total Cost of Issue",
    sales: "-",
    costOfIssue: totalCostOfIssue,
    costingPercentage: "-",
    isTotalCostOfIssue: true,
  });

  return report.generate(resultType);
};

// item-wise stock movement
const getItemWiseStockMovementsReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.ITEM_WISE_STOCK_MOVEMENTS,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    purchaseAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    consumptionAmount: 0,
    preparationAmount: 0,
    prepareConsumptionAmount: 0,
    closingAmount: 0,
    varianceAmount: 0,
    expectedAmount: 0,
  };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const purchaseAmount = data?.purchase?.totalValue
      ? paiseToRupee(data.purchase.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const prepareConsumptionAmount = data?.prepareConsumption?.totalValue
      ? paiseToRupee(data.prepareConsumption.totalValue)
      : 0;
    const preparationAmount = data?.preparation?.totalValue
      ? paiseToRupee(data.preparation.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const varianceAmount = data?.closingAdjustment?.totalValue
      ? paiseToRupee(data?.closingAdjustment?.totalValue)
      : 0;
    const expectedAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      openingQty: data?.opening?.qty || 0,
      openingAmount,
      purchaseQty: data?.purchase?.qty || 0,
      purchaseAmount,
      transferInQty: data?.transferIn?.qty || 0,
      transferInAmount,
      transferOutQty: data?.transferOut?.qty || 0,
      transferOutAmount,
      spoilageQty: data?.spoilage?.qty || 0,
      spoilageAmount,
      prepareConsumptionQty: data?.prepareConsumption?.qty || 0,
      prepareConsumptionAmount,
      preparationQty: data?.preparation?.qty || 0,
      preparationAmount,
      adjustmentQty: data?.adjustment?.qty || 0,
      adjustmentAmount,
      consumptionQty: data?.consumption?.qty || 0,
      consumptionAmount,
      expectedQty: data?.systemClosing?.qty || 0,
      expectedAmount,
      closingQty: data?.physicalClosing?.qty || 0,
      closingAmount,
      varianceQty: data?.closingAdjustment?.qty || 0,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.purchaseAmount += purchaseAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.prepareConsumptionAmount += prepareConsumptionAmount;
    report.result.totalRow.preparationAmount += preparationAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.varianceAmount += varianceAmount;
    report.result.totalRow.expectedAmount += expectedAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });

  return report.generate(resultType);
};

const getPhysicalClosingReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.PHYSICAL_CLOSING,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalQuantity: 0,
  };

  allDocs.forEach((data) => {
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      quantity: data?.physicalClosing?.qty || 0,
      rate: truncateNumber(closingAmount / data?.physicalClosing?.qty, 2),
      totalValue: closingAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.totalValue += closingAmount || 0;
    report.result.totalRow.totalQuantity += data?.physicalClosing?.qty || 0;
  });

  return report.generate(resultType);
};

const getSystemClosingReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SYSTEM_CLOSING,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    totalQuantity: 0,
  };

  allDocs.forEach((data) => {
    const closingAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    const result = {
      itemName: data.inventoryItemName,
      itemCode: data.inventoryItemCode,
      businessDate: data.businessDate,
      location: data.locationName,
      workArea: data.inventoryLocationName,
      hsnCode: data?.hsnCode || "-",
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      pkg: data?.pkgName || "-",
      unitCost: data?.unitCost || 0,
      quantity: data?.systemClosing?.qty || 0,
      rate: truncateNumber(closingAmount / data?.systemClosing?.qty, 2),
      totalValue: closingAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.totalValue += closingAmount || 0;
    report.result.totalRow.totalQuantity += data?.systemClosing?.qty || 0;
  });

  return report.generate(resultType);
};

// Short supply Report
// requested vs issued
const getShortSupplyReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SHORT_SUPPLY,
    identity,
  );
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };
  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const result = {
        transferNo: data.transferNumber,
        itemName: item.itemName,
        itemCode: item.itemCode,
        pkg: item?.pkg.id === "default" ? item.purchaseUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        shortageQuantity:
          item.requestedQuantity - (item.dispatchedQuantity || 0),
      };
      report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

// store variance
const getStoreVarianceReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.STORE_VARIANCE,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    purchaseAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    closingAmount: 0,
    varianceAmount: 0,
    expectedAmount: 0,
  };

  const aggregateMap = new Map();

  allDocs.forEach((data) => {
    const key = `${data.inventoryLocationId}_${data.inventoryItemId}_${data.pkgId}`;

    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const purchaseAmount = data?.purchase?.totalValue
      ? paiseToRupee(data.purchase.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const varianceAmount = data?.closingAdjustment?.totalValue
      ? paiseToRupee(data.closingAdjustment.totalValue)
      : 0;
    const expectedAmount = data?.systemClosing?.totalValue
      ? paiseToRupee(data.systemClosing.totalValue)
      : 0;

    if (!aggregateMap.has(key)) {
      aggregateMap.set(key, {
        location: data.locationName,
        workArea: data.inventoryLocationName,
        categoryName: data?.categoryName || "-",
        subCategoryName: data?.subcategoryName || "-",
        hsnCode: data?.hsnCode || "-",
        itemCode: data.inventoryItemCode,
        itemName: data.inventoryItemName,
        pkg: data?.pkgName || "-",

        openingQty: 0,
        purchaseQty: 0,
        transferInQty: 0,
        transferOutQty: 0,
        spoilageQty: 0,
        adjustmentQty: 0,
        expectedQty: 0,
        closingQty: 0,
        varianceQty: 0,

        openingAmount: 0,
        purchaseAmount: 0,
        transferInAmount: 0,
        transferOutAmount: 0,
        spoilageAmount: 0,
        adjustmentAmount: 0,
        expectedAmount: 0,
        closingAmount: 0,
        varianceAmount: 0,
      });
    }

    const row = aggregateMap.get(key);

    row.openingQty += data?.opening?.qty || 0;
    row.purchaseQty += data?.purchase?.qty || 0;
    row.transferInQty += data?.transferIn?.qty || 0;
    row.transferOutQty += data?.transferOut?.qty || 0;
    row.spoilageQty += data?.spoilage?.qty || 0;
    row.adjustmentQty += data?.adjustment?.qty || 0;
    row.expectedQty += data?.systemClosing?.qty || 0;
    row.closingQty += data?.physicalClosing?.qty || 0;
    row.varianceQty += data?.closingAdjustment?.qty || 0;

    row.openingAmount += openingAmount;
    row.purchaseAmount += purchaseAmount;
    row.transferInAmount += transferInAmount;
    row.transferOutAmount += transferOutAmount;
    row.spoilageAmount += spoilageAmount;
    row.adjustmentAmount += adjustmentAmount;
    row.expectedAmount += expectedAmount;
    row.closingAmount += closingAmount;
    row.varianceAmount += varianceAmount;

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.purchaseAmount += purchaseAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.varianceAmount += varianceAmount;
    report.result.totalRow.expectedAmount += expectedAmount;
  });

  report.result.data = Array.from(aggregateMap.values());

  return report.generate(resultType);
};

// bar variance
const getBarVarianceReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.BAR_VARIANCE,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    preparationAmount: 0,
    totalStockAmount: 0,
    closingAmount: 0,
    actualCost: 0,
    consumptionAmount: 0,
    varianceAmount: 0,
  };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const preparationAmount = data?.preparation?.totalValue
      ? paiseToRupee(data.preparation.totalValue)
      : 0;

    // @todo
    // 1.Preparation Qty
    // 2.Total Available Stock Qty:
    // inv-item (Opening Qty + Transfer In Qty + Adjustment Qty) - (Transfer Out Qty + Spoilage Qty + consumption Preparation Qty)
    // made-item (Opening Qty + Transfer In Qty + Adjustment Qty + consumption Preparation Qty) - (Transfer Out Qty + Spoilage Qty)

    const openingQty = data?.opening?.qty || 0;
    const transferInQty = data?.transferIn?.qty || 0;
    const transferOutQty = data?.transferOut?.qty || 0;
    const spoilageQty = data?.spoilage?.qty || 0;
    const adjustmentQty = data?.adjustment?.qty || 0;
    const preparationQty = data?.preparation?.qty || 0;

    // Total Available Stock Qty
    let totalStock = 0;

    if (data.itemType === "made") {
      totalStock =
        openingQty +
        transferInQty +
        adjustmentQty +
        preparationQty -
        (transferOutQty + spoilageQty);
    } else {
      totalStock =
        openingQty +
        transferInQty +
        adjustmentQty -
        (transferOutQty + spoilageQty + preparationQty);
    }

    const closingQty = data?.physicalClosing?.qty || 0;
    const actualConsumption = totalStock - closingQty;
    const consumptionQty = data?.consumption?.qty || 0;

    // Minimal assumption: stock value before closing
    const totalStockAmount =
      openingAmount +
      transferInAmount +
      adjustmentAmount +
      preparationAmount -
      (transferOutAmount + spoilageAmount);

    const actualCost = totalStockAmount - closingAmount;
    const varianceAmount = actualCost - consumptionAmount;

    const result = {
      location: data.locationName,
      workArea: data.inventoryLocationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.countingUOM || "-",

      openingQty,
      transferInQty,
      transferOutQty,
      spoilageQty,
      adjustmentQty,
      preparationQty,
      totalStock,
      closingQty,
      actualConsumption,
      consumptionQty,
      varianceQty: actualConsumption - consumptionQty,

      openingAmount,
      transferInAmount,
      transferOutAmount,
      spoilageAmount,
      adjustmentAmount,
      preparationAmount,
      totalStockAmount,
      closingAmount,
      actualCost,
      consumptionAmount,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.preparationAmount += preparationAmount;
    report.result.totalRow.totalStockAmount += totalStockAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.actualCost += actualCost;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.varianceAmount += varianceAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

// flr report
const getFLRReport = async (tenantId, payload, resultType, identity) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.FLR,
    identity,
  );

  // Store Closing → default work area
  const storeData = await fetchTenantsReportData(
    tenantId,
    report._options._filters,
    { defaultWorkArea: true, barCategory: true },
  );

  // Work Area Closing → bar work areas
  const workAreaData = await fetchTenantsReportData(
    tenantId,
    report._options._filters,
    { barWorkArea: true, barCategory: true },
  );

  const storeDataMap = new Map();
  storeData.forEach((item) => {
    const key = `${item.locationId}_${item.inventoryItemId}_${item.pkgId}`;
    storeDataMap.set(key, item);
  });

  const workAreaDataMap = new Map();
  workAreaData.forEach((item) => {
    const key = `${item.locationId}_${item.inventoryItemId}_${item.pkgId}`;
    const storeItem = storeDataMap.get(key);

    // skip default store work area
    if (storeItem && item.inventoryLocationId === storeItem.inventoryLocationId)
      return;

    const existing = workAreaDataMap.get(key) || { recipeQty: 0 };
    existing.recipeQty += item?.physicalClosing?.recipeQty || 0;

    workAreaDataMap.set(key, existing);
  });

  const allKeys = new Set([...storeDataMap.keys(), ...workAreaDataMap.keys()]);

  report.result.totalRow = { totalValue: 0 };

  allKeys.forEach((key) => {
    const storeItem = storeDataMap.get(key);
    const workAreaAgg = workAreaDataMap.get(key);
    const data = storeItem;

    const pkgUom = storeItem?.physicalClosing?.uom || "";
    const storeQty = calculateBaseQty(
      storeItem?.physicalClosing?.recipeQty || 0,
      pkgUom,
    );
    const workAreaQty = calculateBaseQty(workAreaAgg?.recipeQty || 0, pkgUom);
    const pkgQty = storeItem?.physicalClosing?.qty
      ? storeQty / storeItem.physicalClosing.qty
      : 1;

    const store = calculateFullOpenBottles(pkgQty, storeQty);
    const workArea = calculateFullOpenBottles(pkgQty, workAreaQty);

    const totalQty =
      store.fullBottle * pkgQty +
      store.openBottle +
      workArea.fullBottle * pkgQty +
      workArea.openBottle;

    const total = calculateFullOpenBottles(pkgQty, totalQty);

    const result = {
      location: data.locationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.pkgName || "-",

      storeClosingPkg: store.fullBottle,
      storeClosingOpen: truncateNumber(store.openBottle),
      purchaseQty: data?.purchase?.qty || 0,

      workAreaClosingPkg: workArea.fullBottle,
      workAreaClosingOpen: truncateNumber(workArea.openBottle),

      totalQtyPkg: total.fullBottle,
      totalQtyOpen: truncateNumber(total.openBottle),
    };

    report.result.data.push(result);

    report.result.totalRow.totalValue +=
      (storeItem?.physicalClosing?.totalValue || 0) +
      (workAreaAgg?.totalValue || 0);
  });

  return report.generate(resultType);
};

function calculateBaseQty(qty = 0, uom = "") {
  const unit = uom.toLowerCase();

  switch (unit) {
    case "l":
      return qty * 1000;
    case "ml":
      return qty;

    case "kg":
      return qty * 1000;
    case "g":
      return qty;

    case "nos":
      return qty;

    default:
      return qty;
  }
}

function calculateFullOpenBottles(pkgQty, totalQty) {
  let fullBottle = Math.floor(totalQty / pkgQty);
  let openBottle = totalQty % pkgQty;

  if (openBottle >= pkgQty) {
    const extra = Math.floor(openBottle / pkgQty);
    fullBottle += extra;
    openBottle = openBottle % pkgQty;
  }

  return { fullBottle, openBottle };
}

// inventory consumption
const getInventoryConsumptionReport = async (
  tenantId,
  payload,
  resultType,
  identity,
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.INVENTORY_CONSUMPTION,
    identity,
  );

  const allDocs = await report.snap();

  report.result.totalRow = {
    totalValue: 0,
    openingAmount: 0,
    transferInAmount: 0,
    transferOutAmount: 0,
    spoilageAmount: 0,
    adjustmentAmount: 0,
    preparationAmount: 0,
    totalStockAmount: 0,
    closingAmount: 0,
    actualCost: 0,
    consumptionAmount: 0,
    varianceAmount: 0,
  };

  allDocs.forEach((data) => {
    const openingAmount = data?.opening?.totalValue
      ? paiseToRupee(data.opening.totalValue)
      : 0;
    const transferInAmount = data?.transferIn?.totalValue
      ? paiseToRupee(data.transferIn.totalValue)
      : 0;
    const transferOutAmount = data?.transferOut?.totalValue
      ? paiseToRupee(data.transferOut.totalValue)
      : 0;
    const spoilageAmount = data?.spoilage?.totalValue
      ? paiseToRupee(data.spoilage.totalValue)
      : 0;
    const adjustmentAmount = data?.adjustment?.totalValue
      ? paiseToRupee(data.adjustment.totalValue)
      : 0;
    const consumptionAmount = data?.consumption?.totalValue
      ? paiseToRupee(data.consumption.totalValue)
      : 0;
    const closingAmount = data?.physicalClosing?.totalValue
      ? paiseToRupee(data.physicalClosing.totalValue)
      : 0;
    const preparationAmount = data?.preparation?.totalValue
      ? paiseToRupee(data.preparation.totalValue)
      : 0;

    // @todo
    // 1.Preparation Qty
    // 2.Total Available Stock Qty:
    // inv-item (Opening Qty + Transfer In Qty + Adjustment Qty) - (Transfer Out Qty + Spoilage Qty + consumption Preparation Qty)
    // made-item (Opening Qty + Transfer In Qty + Adjustment Qty + consumption Preparation Qty) - (Transfer Out Qty + Spoilage Qty)

    const openingQty = data?.opening?.qty || 0;
    const transferInQty = data?.transferIn?.qty || 0;
    const transferOutQty = data?.transferOut?.qty || 0;
    const spoilageQty = data?.spoilage?.qty || 0;
    const adjustmentQty = data?.adjustment?.qty || 0;
    const preparationQty = data?.preparation?.qty || 0;

    // Total Available Stock Qty
    let totalStock = 0;

    if (data.itemType === "made") {
      totalStock =
        openingQty +
        transferInQty +
        adjustmentQty +
        preparationQty -
        (transferOutQty + spoilageQty);
    } else {
      totalStock =
        openingQty +
        transferInQty +
        adjustmentQty -
        (transferOutQty + spoilageQty + preparationQty);
    }

    const closingQty = data?.physicalClosing?.qty || 0;
    const actualConsumption = totalStock - closingQty;
    const consumptionQty = data?.consumption?.qty || 0;

    // Minimal assumption: stock value before closing
    const totalStockAmount =
      openingAmount +
      transferInAmount +
      adjustmentAmount +
      preparationAmount -
      (transferOutAmount + spoilageAmount);

    const actualCost = totalStockAmount - closingAmount;
    const varianceAmount = actualCost - consumptionAmount;

    const result = {
      location: data.locationName,
      workArea: data.inventoryLocationName,
      categoryName: data?.categoryName || "-",
      subCategoryName: data?.subcategoryName || "-",
      hsnCode: data?.hsnCode || "-",
      itemCode: data.inventoryItemCode,
      itemName: data.inventoryItemName,
      pkg: data?.countingUOM || "-",

      openingQty,
      transferInQty,
      transferOutQty,
      spoilageQty,
      adjustmentQty,
      preparationQty,
      totalStock,
      closingQty,
      actualConsumption,
      consumptionQty,
      varianceQty: actualConsumption - consumptionQty,

      openingAmount,
      transferInAmount,
      transferOutAmount,
      spoilageAmount,
      adjustmentAmount,
      preparationAmount,
      totalStockAmount,
      closingAmount,
      actualCost,
      consumptionAmount,
      varianceAmount,
    };

    report.result.data.push(result);

    report.result.totalRow.openingAmount += openingAmount;
    report.result.totalRow.transferInAmount += transferInAmount;
    report.result.totalRow.transferOutAmount += transferOutAmount;
    report.result.totalRow.spoilageAmount += spoilageAmount;
    report.result.totalRow.adjustmentAmount += adjustmentAmount;
    report.result.totalRow.preparationAmount += preparationAmount;
    report.result.totalRow.totalStockAmount += totalStockAmount;
    report.result.totalRow.closingAmount += closingAmount;
    report.result.totalRow.actualCost += actualCost;
    report.result.totalRow.consumptionAmount += consumptionAmount;
    report.result.totalRow.varianceAmount += varianceAmount;

    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};

module.exports = {
  getTransferListReport,
  getDispatchTransferReport,
  getDetailedTransferReport,
  getCostOfIssueVsRevenueReport,
  getItemWiseStockMovementsReport,
  getShortSupplyReport,
  getPhysicalClosingReport,
  getSystemClosingReport,
  getStoreVarianceReport,
  getInventoryConsumptionReport,
  getBarVarianceReport,
  getFLRReport,
};
