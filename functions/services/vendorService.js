const {
    updateVendorStatus
} = require("@/repositories/vendorRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Vendor operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a vendor
 * No validation needed for activation
 * @param {string} vendorId
 * @returns {object} activeStatus
 */
async function activateVendor(tenantId, vendorId) {
    // Update status to active
    await updateVendorStatus(tenantId, vendorId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} vendorId
 * @returns {object} activeStatus
 */
async function deactivateVendor(tenantId, vendorId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("vendors", tenantId, vendorId);

    // Update status to inactive
    await updateVendorStatus(tenantId, vendorId, false);
    return { activeStatus: false };
}

module.exports = {
    activateVendor,
    deactivateVendor
};
