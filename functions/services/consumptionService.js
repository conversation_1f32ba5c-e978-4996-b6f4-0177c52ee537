// services/consumptionService.js
const {
  prepareMenuRecipeSplitup,
  debitStockByRecipe,
} = require("@/services/menuRecipeService");
const schema = require("@/schema/consumptionSchema");
const { handleValidation } = require("@/utils/validation");
const {
  createConsumption,
  getConsumptionData,
  updateConsumptionRecordsInFirestore,
  getConsumptionDataById,
  updateConsumptionRecord,
} = require("@/repositories/consumptionRepo");
const { paiseToRupee } = require("@/utils/money");

const {
  getMenuItemById,
  getRecordsByFloorId,
  getModifierById,
} = require("@/repositories/receipeRepo");
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const {
  getInventoryItemStocks,
  calculateAggregateQty,
} = require("@/repositories/stockRepo");


/**
 * Main function to create consumption data from sales details
 * Processes menu items and their modifiers to calculate inventory consumption
 */
const createConsumptionData = async (tenantId, saleDetails) => {
  const floorId = saleDetails.floor_no;
  const consumptionDetails = [];

  // Fetch all active work areas for the given floor once
  const availableWorkAreas = await getRecordsByFloorId(tenantId, floorId);

  // Preprocess: Normalize items and modifiers into a flat structure
  const normalizedItems = preprocessSaleItems(saleDetails.items);

  // Process each normalized entry (menu items + modifiers)
  for (const normalizedItem of normalizedItems) {
    const consumptionObj = await processNormalizedItem(
      tenantId,
      saleDetails,
      normalizedItem,
      availableWorkAreas
    );
    consumptionDetails.push(consumptionObj);
  }

  // Validate and create consumption records
  for (const item of consumptionDetails) {
    if (item.workAreaId !== null) {
      let requiredWA = availableWorkAreas.find(
        (wa) => wa.id === item.workAreaId
      );
      let requiredFloor = requiredWA.floorIds.find(
        (floor) => floor.id === item.floorNo
      );
      item["floorName"] = requiredFloor.name;
    } else {
      item["floorName"] = "";
    }
    const validatedData = handleValidation(item, schema);
    if (validatedData) {
      await createConsumption(validatedData);
      await calculateConsumptionCosts(tenantId);
    }
  }

  return saleDetails;
};

/**
 * Preprocesses sale items to create a normalized flat structure
 * Expands modifiers into separate entries alongside menu items
 * Each item in the items array is processed separately (even duplicates)
 */
const preprocessSaleItems = (items) => {
  const normalizedItems = [];

  for (const item of items) {
    // Add the base menu item
    normalizedItems.push({
      type: "menu",
      menu_item_id: item.menu_item_id,
      menu_item_name: item.menu_item_name,
      plu_code: item.plu_code,
      serving_size_id: item.serving_size_id,
      serving_size_name: item.serving_size_name,
      quantity: item.quantity,
      total_amount: item.total_amount,
      ledger_category: item.ledger_category,
      department: item.department,
      cuisines: item.cuisines,
      categories: item.categories,
      kot_sent_date: item.kot_sent_date,
      kot_sent_by: item.kot_sent_by,
      status: item.status,
    });

    // Check if modifiers exist
    const modifierRecipe = item.modifier_receipe;
    if (!modifierRecipe) {
      continue;
    }

    // Process mandatory modifier groups
    if (modifierRecipe.mandatory_modifier_groups) {
      const mandatoryModifiers = extractModifiers(
        modifierRecipe.mandatory_modifier_groups,
        "mandatory",
        item
      );
      normalizedItems.push(...mandatoryModifiers);
    }

    // Process optional modifier groups
    if (modifierRecipe.optional_modifier_groups) {
      const optionalModifiers = extractModifiers(
        modifierRecipe.optional_modifier_groups,
        "optional",
        item
      );
      normalizedItems.push(...optionalModifiers);
    }
  }

  return normalizedItems;
};

/**
 * Extracts modifiers from modifier groups
 * Creates normalized modifier entries with parent item's quantity
 * @param {Array} modifierGroups - Array of modifier groups (mandatory or optional)
 * @param {String} groupType - 'mandatory' or 'optional'
 * @param {Object} parentItem - Parent menu item
 * @returns {Array} Modifier entries
 */
const extractModifiers = (modifierGroups, groupType, parentItem) => {
  const modifiers = [];

  for (const modifierGroup of modifierGroups) {
    const modifiersList = modifierGroup.modifiers || [];

    for (const modifier of modifiersList) {
      modifiers.push({
        type: "modifier",
        modifierGroup: groupType,
        // Parent menu item metadata
        menu_item_id: parentItem.menu_item_id,
        menu_item_name: parentItem.menu_item_name,
        plu_code: parentItem.plu_code,
        // Modifier-specific data
        modifier_id: modifier.id,
        modifier_name: modifier.name,
        modifier_amount: modifier.amount,
        quantity: parentItem.quantity, // ✅ Inherit parent item's quantity
        serving_size_id: modifier.serving_size_id,
        serving_size_name: parentItem.serving_size_name,
        modifier_group_id: modifierGroup.modifier_group_id,
        tax_amount: modifier.tax_amount || 0,
        kot_sent_date: parentItem.kot_sent_date,
        kot_sent_by: parentItem.kot_sent_by,
        status: parentItem.status,
      });
    }
  }

  return modifiers;
};

/**
 * Processes a normalized item (menu or modifier)
 * Single unified flow with conditional recipe fetching
 * @param {String} tenantId - Tenant identifier
 * @param {Object} saleDetails - Sale details object
 * @param {Object} normalizedItem - Normalized item (menu or modifier)
 * @param {Array} availableWorkAreas - Available work areas for the floor
 * @returns {Object} Consumption object
 */
const processNormalizedItem = async (
  tenantId,
  saleDetails,
  normalizedItem,
  availableWorkAreas
) => {
  // Fetch recipe based on type
  const recipe =
    normalizedItem.type === "menu"
      ? await getMenuItemById(tenantId, normalizedItem.menu_item_id)
      : await getModifierById(tenantId, normalizedItem.modifier_id);

  // For modifiers, we also need the parent menu item's recipe to get its PLU code and name
  let parentRecipe = null;
  if (normalizedItem.type === "modifier") {
    parentRecipe = await getMenuItemById(tenantId, normalizedItem.menu_item_id);
  }

  // Initialize consumption object with common fields
  const consumptionObj = {
    saleReferenceId: saleDetails.sale_reference_id,
    tenantId: tenantId,
    accountId: saleDetails.account_id,
    storeId: saleDetails.store_id,
    ticketNo: saleDetails.ticket_no,
    tableNo: saleDetails.table_no,
    floorNo: saleDetails.floor_no,
    type: normalizedItem.type,
    soldQuantity: normalizedItem.quantity,
    servingSizeId: normalizedItem.serving_size_id,
    servingSizeName: normalizedItem.serving_size_name || "", // Will be populated from recipe
    menuItemId: normalizedItem.menu_item_id || "",
    posItemCode:
      normalizedItem.type === "modifier" && parentRecipe
        ? parentRecipe.itemCode || "" // ✅ Use parent's PLU code for modifiers
        : normalizedItem.plu_code || "", // Use from payload for menu items (will be overwritten)
    posMenuItemName:
      normalizedItem.type === "modifier" && parentRecipe
        ? parentRecipe.itemName || normalizedItem.menu_item_name // ✅ Use parent's name for modifiers
        : normalizedItem.menu_item_name || "", // Use from payload for menu items (will be overwritten)
  };

  // Add type-specific fields
  if (normalizedItem.type === "menu") {
    consumptionObj.posItemTotalAmount = paiseToRupee(
      normalizedItem.total_amount
    );
  } else {
    // Modifier type
    consumptionObj.modifierName = normalizedItem.modifier_name;
    consumptionObj.modifierId = normalizedItem.modifier_id;
    consumptionObj.modifierGroup = normalizedItem.modifierGroup;
    consumptionObj.modifierGroupId = normalizedItem.modifier_group_id;
    consumptionObj.posItemTotalAmount = paiseToRupee(
      normalizedItem.modifier_amount
    );
  }

  // Process recipe details and calculate consumption
  await processRecipeDetails(
    tenantId,
    consumptionObj,
    recipe,
    availableWorkAreas,
    normalizedItem.serving_size_id,
    normalizedItem.quantity
  );

  return consumptionObj;
};

/**
 * Common function to process recipe details
 * Works for both menu items and modifiers (same response structure)
 * Calculates inventory consumption based on recipe type
 * @param {String} tenantId - Tenant identifier
 * @param {Object} obj - Consumption object being built
 * @param {Object} recipe - Recipe details from getMenuItemById or getModifierById
 * @param {Array} availableWorkAreas - Available work areas
 * @param {Number} servingSizeId - Serving size ID (from payload)
 * @param {Number} soldQuantity - Quantity sold (inherits parent item quantity for modifiers)
 */
const processRecipeDetails = async (
  tenantId,
  obj,
  recipe,
  availableWorkAreas,
  servingSizeId,
  soldQuantity
) => {
  // Check if recipe exists and is linked
  if (!recipe || recipe.linkingStatus !== true) {
    obj.workArea = null;
    obj.workAreaId = null;
    obj.location = null;
    obj.locationId = null;
    obj.invItemName = null;
    obj.invItemCode = null;
    obj.invItems = [];
    obj.status = "error";
    obj.statusSummary = recipe
      ? "Item not linked to inventory"
      : `${obj.type === "menu" ? "Menu item" : "Modifier"} not found in system`;
    return;
  }

  // Store itemCode (PLU code) from recipe ONLY for menu items
  // For modifiers, posItemCode already has parent's PLU code from processNormalizedItem
  if (obj.type === "menu") {
    obj.posItemCode = recipe.itemCode || ""; // ✅ Only update for menu items
  }

  // Store itemName from recipe ONLY for menu items
  // For modifiers, posMenuItemName already has parent's name from processNormalizedItem
  if (obj.type === "menu") {
    obj.posMenuItemName = recipe.itemName || ""; // ✅ Only update for menu items
  }

  // Find the work area matching the recipe's tag
  const requiredWorkArea = availableWorkAreas.find(
    (wa) => wa.tagId === recipe.tags.id
  );

  if (!requiredWorkArea) {
    obj.workArea = null;
    obj.workAreaId = null;
    obj.location = null;
    obj.locationId = null;
    obj.invItemName = recipe.selectedItemDetail.itemName;
    obj.invItemCode = recipe.selectedItemDetail.itemCode;
    obj.invItems = [];
    obj.status = "error";
    obj.statusSummary = "Work area not found for recipe tag";
    return;
  }

  // Assign work area and location details
  obj.workArea = requiredWorkArea.name;
  obj.workAreaId = requiredWorkArea.id;
  obj.location = requiredWorkArea.locationName;
  obj.locationId = requiredWorkArea.locationId;
  obj.invItemName = recipe.selectedItemDetail.itemName;
  obj.invItemCode = recipe.selectedItemDetail.itemCode;
  obj.invItems = [];

  // Find the serving size quantity by ID (convert to string for comparison)
  const servingSizeIdStr = String(servingSizeId);
  const reductionQty = recipe.servingLevels.find(
    (level) => String(level.servingSizeId) === servingSizeIdStr
  );

  if (!reductionQty) {
    obj.status = "error";
    obj.statusSummary = "Serving size not found in recipe";
    return;
  }

  // Store the serving size name from the matched recipe serving level
  obj.servingSizeName = reductionQty.servingSizeName;

  // Process based on item type (bought/made vs recipe)
  const itemType = recipe.selectedItemDetail.itemType;

  if (itemType && ["bought", "made"].includes(itemType)) {
    // Direct inventory item - simple consumption calculation
    obj.invItems.push({
      itemName: recipe.selectedItemDetail.itemName,
      itemCode: recipe.selectedItemDetail.itemCode,
      itemId: recipe.selectedItemDetail.id,
      itemType: itemType,
      recipeUom: recipe.selectedItemDetail.recipeUnit,
      cost: 0,
      requiredQty: reductionQty.qty * soldQuantity,
    });
    obj.status = "pending";
    obj.statusSummary = "Ready for consumption";
  } else {
    // Recipe item - break down into ingredients
    const ingredientDetails = await prepareMenuRecipeSplitup(
      tenantId,
      recipe.selectedItemDetail.id,
      requiredWorkArea.id
    );

    // Calculate conversion ratio: (serving size qty / base recipe qty)
    const conversionRatio = reductionQty.qty / ingredientDetails.quantity;

    // Process each ingredient in the recipe
    for (const ingredient of ingredientDetails.ingredients) {
      obj.invItems.push({
        itemName: ingredient.itemName,
        itemCode: ingredient.itemCode,
        itemId: ingredient.itemId,
        itemType: ingredient.itemType,
        recipeUom: ingredient.unit,
        cost: 0,
        requiredQty: conversionRatio * ingredient.recipeQty * soldQuantity,
      });
    }
    obj.status = "pending";
    obj.statusSummary = "Ready for consumption";
  }
};

//------------------------------------------------------//
/**
 * Main function to process consumption data with aggregation
 * Handles partial failures properly and updates Firestore automatically
 */
const processConsumptionData = async (tenantId) => {
  try {
    console.log("\n" + "=".repeat(70));
    console.log("CONSUMPTION PROCESSING STARTED");
    console.log("=".repeat(70));
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log("=".repeat(70));

    // Fetch consumption data from Firestore
    console.log("\n🔄 Fetching consumption data from Firestore...");
    const consumptionData = await getConsumptionData(tenantId);

    if (!consumptionData || consumptionData.length === 0) {
      console.log("ℹ️  No consumption data to process");
      return {
        success: true,
        message: "No pending consumption data found",
        results: [],
      };
    }

    console.log(
      `✓ Fetched ${consumptionData.length} pending consumption records`
    );

    // Step 1: Build aggregation map
    console.log("\n🔄 Step 1: Building aggregation map...");
    const aggregationMap = buildAggregationMap(consumptionData);
    console.log(
      `✓ Created ${Object.keys(aggregationMap).length} aggregated groups`
    );

    // Step 2: Process each aggregated group (debit stock)
    console.log(
      "\n🔄 Step 2: Processing aggregated groups (debiting stock)..."
    );
    const debitResults = await processAggregatedGroups(aggregationMap);
    console.log(`✓ Processed ${debitResults.length} debit operations`);

    // Step 2.5: Track which records have failures
    const recordFailureMap = buildRecordFailureMap(debitResults);

    // Step 3: Distribute costs back to original records
    console.log("\n🔄 Step 3: Distributing costs to original records...");
    const { updatedRecords, failedRecords } = distributeCosts(
      consumptionData,
      debitResults,
      recordFailureMap
    );
    console.log(`✓ Successfully processed: ${updatedRecords.length} records`);
    console.log(`⚠️  Failed/Skipped: ${failedRecords.length} records`);

    // Step 4: Update Firestore with successfully processed records
    let updateResult = null;
    if (updatedRecords.length > 0) {
      console.log("\n🔄 Step 4: Updating records in Firestore...");
      updateResult = await updateConsumptionRecordsInFirestore(updatedRecords);
    } else {
      console.log(
        "\n⚠️  Step 4: No records to update in Firestore (all failed or none processed)"
      );
    }

    console.log("\n" + "=".repeat(70));
    console.log("PROCESSING SUMMARY");
    console.log("=".repeat(70));
    console.log(`✓ Total records fetched: ${consumptionData.length}`);
    console.log(
      `✓ Unique items aggregated: ${Object.keys(aggregationMap).length}`
    );
    console.log(`✓ Debit operations executed: ${debitResults.length}`);
    console.log(
      `✓ Records updated in Firestore: ${updateResult?.successCount || 0}`
    );
    if (failedRecords.length > 0) {
      console.log(`⚠️  Records with debit errors: ${failedRecords.length}`);
    }
    if (updateResult?.failCount > 0) {
      console.log(
        `⚠️  Records failed to update in Firestore: ${updateResult.failCount}`
      );
    }
    console.log("=".repeat(70));

    return {
      success: true,
      totalRecords: consumptionData.length,
      uniqueItems: Object.keys(aggregationMap).length,
      debitOperations: debitResults.length,
      updatedRecords,
      failedRecords,
      debitResults,
      updateResult,
    };
  } catch (error) {
    console.error("\n❌ Error processing consumption data:", error);
    console.error("Stack trace:", error.stack);
    throw error;
  }
};

/**
 * Build aggregation map grouped by itemId and workAreaId
 */
// const buildAggregationMap = (consumptionData) => {
//   const aggregationMap = {};

//   consumptionData.forEach((record) => {
//     if (!record.invItems || record.invItems.length === 0) {
//       console.warn(`⚠️  Record ${record.id} has no invItems, skipping...`);
//       return;
//     }

//     record.invItems.forEach((item) => {
//       const key = `${item.itemId}_${record.workAreaId}`;

//       if (!aggregationMap[key]) {
//         aggregationMap[key] = {
//           itemId: item.itemId,
//           itemCode: item.itemCode,
//           itemName: item.itemName,
//           workAreaId: record.workAreaId,
//           workArea: record.workArea,
//           location: record.location,
//           locationId: record.locationId,
//           recipeUom: item.recipeUom,
//           itemType: item.itemType,
//           totalRequiredQty: 0,
//           records: [],
//         };
//       }

//       aggregationMap[key].totalRequiredQty += item.requiredQty;

//       aggregationMap[key].records.push({
//         recordId: record.id,
//         recordRef: record,
//         itemContribution: item.requiredQty,
//         invItem: item,
//         ticketNo: record.ticketNo,
//         soldQuantity: record.soldQuantity,
//       });
//     });
//   });

//   console.log("\n📊 Aggregation Summary:");
//   Object.entries(aggregationMap).forEach(([key, data]) => {
//     console.log(`  ${data.itemName} (${data.itemCode})`);
//     console.log(`    ├─ Location: ${data.workArea}`);
//     console.log(`    ├─ Total Qty: ${data.totalRequiredQty} ${data.recipeUom}`);
//     console.log(`    └─ Records: ${data.records.length} tickets`);
//   });

//   return aggregationMap;
// };

/**
 * Process each aggregated group and debit stock using FIFO/FEFO
 */
const processAggregatedGroups = async (aggregationMap) => {
  const debitResults = [];

  for (const [key, aggregatedItem] of Object.entries(aggregationMap)) {
    try {
      const debitPayload = {
        itemId: aggregatedItem.itemId,
        qtyInRecipeUOM: aggregatedItem.totalRequiredQty,
        inventoryLocationId: aggregatedItem.workAreaId,
        itemName: aggregatedItem.itemName,
      };

      console.log(
        `\n🔸 Processing: ${aggregatedItem.itemName} (${aggregatedItem.itemCode})`
      );
      console.log(
        `   Total quantity: ${aggregatedItem.totalRequiredQty} ${aggregatedItem.recipeUom}`
      );
      console.log(`   Work area: ${aggregatedItem.workArea}`);
      console.log(`   From ${aggregatedItem.records.length} tickets`);

      // Call your existing debitStockByRecipe function
      const debitResult = await debitStockByRecipe(debitPayload);

      // Calculate total cost from all batches (handles multi-batch FIFO/FEFO)
      const totalCost = debitResult.debitResults.reduce(
        (sum, dr) => sum + dr.totalCost,
        0
      );

      console.log(`   ✓ Debited successfully`);
      console.log(`   ├─ Batches used: ${debitResult.batchesToDebit.length}`);
      console.log(`   ├─ Total cost: ${totalCost.toFixed(2)}`);
      console.log(
        `   └─ Cost per unit: ${(
          totalCost / aggregatedItem.totalRequiredQty
        ).toFixed(4)} per ${aggregatedItem.recipeUom}`
      );

      debitResults.push({
        aggregationKey: key,
        aggregatedItem,
        debitResult,
        totalCost,
        success: true,
      });
    } catch (error) {
      console.error(
        `   ❌ Error debiting ${aggregatedItem.itemName}:`,
        error.message
      );

      debitResults.push({
        aggregationKey: key,
        aggregatedItem,
        error: error.message,
        success: false,
      });
    }
  }

  return debitResults;
};

/**
 * Build a map of record IDs to their failed items
 * This helps identify which records have partial failures
 */
const buildRecordFailureMap = (debitResults) => {
  const recordFailureMap = new Map(); // recordId -> Array of failed items

  debitResults.forEach(({ aggregatedItem, error, success }) => {
    if (!success) {
      // This item failed, mark all associated records
      aggregatedItem.records.forEach(({ recordId, recordRef }) => {
        if (!recordFailureMap.has(recordId)) {
          recordFailureMap.set(recordId, []);
        }
        recordFailureMap.get(recordId).push({
          itemName: aggregatedItem.itemName,
          itemCode: aggregatedItem.itemCode,
          itemId: aggregatedItem.itemId,
          error: error,
        });
      });
    }
  });

  if (recordFailureMap.size > 0) {
    console.log("\n⚠️  Records with failed items:");
    recordFailureMap.forEach((failures, recordId) => {
      console.log(`  Record ${recordId}:`);
      failures.forEach((f) => {
        console.log(`    ├─ ${f.itemName} (${f.itemCode}): ${f.error}`);
      });
    });
  }

  return recordFailureMap;
};

/**
 * Distribute costs from debit results back to original consumption records
 * Handles partial failures - records with ANY failed item are excluded
 */
const distributeCosts = (consumptionData, debitResults, recordFailureMap) => {
  const updatedRecords = [];
  const failedRecords = [];
  const processedRecordIds = new Set();

  debitResults.forEach(
    ({
      aggregationKey,
      aggregatedItem,
      debitResult,
      totalCost,
      error,
      success,
    }) => {
      if (!success || error) {
        console.log(
          `\n⚠️  Skipping cost distribution for ${aggregatedItem.itemName} due to error`
        );

        // Mark all associated records as failed (only once per record)
        aggregatedItem.records.forEach(({ recordRef }) => {
          if (!processedRecordIds.has(recordRef.id)) {
            processedRecordIds.add(recordRef.id);

            const failures = recordFailureMap.get(recordRef.id) || [];
            const failedItemNames = failures.map((f) => f.itemName).join(", ");

            recordRef.status = "error";
            recordRef.statusSummary = `Debit failed for: ${failedItemNames}`;
            recordRef.failedItems = failures;

            failedRecords.push(recordRef);
          }
        });

        return;
      }

      // Calculate total cost from all debit results (handles multiple batches)
      const totalCostDebited = debitResult.debitResults.reduce(
        (sum, dr) => sum + dr.totalCost,
        0
      );

      const totalQtyDebited = aggregatedItem.totalRequiredQty;
      const costPerUnit = totalCostDebited / totalQtyDebited;

      console.log(`\n💰 Distributing costs for ${aggregatedItem.itemName}:`);
      console.log(`   Total cost debited: ${totalCostDebited.toFixed(2)}`);
      console.log(
        `   Total quantity: ${totalQtyDebited} ${aggregatedItem.recipeUom}`
      );
      console.log(
        `   Cost per unit: ${costPerUnit.toFixed(4)} per ${
          aggregatedItem.recipeUom
        }`
      );
      console.log(
        `   Distributing to ${aggregatedItem.records.length} records:`
      );

      // Distribute cost to each record proportionally
      aggregatedItem.records.forEach((recordInfo, index) => {
        const record = recordInfo.recordRef;

        // Check if this record has ANY failed items
        if (recordFailureMap.has(record.id)) {
          console.log(
            `   ${index + 1}. Ticket #${
              record.ticketNo
            } - ⚠️ SKIPPED (has failed items)`
          );
          return; // Skip this record entirely
        }

        const contribution = recordInfo.itemContribution;
        const allocatedCost = costPerUnit * contribution;

        console.log(
          `   ${index + 1}. Ticket #${record.ticketNo} (${record.id})`
        );
        console.log(
          `      ├─ Quantity: ${contribution} ${aggregatedItem.recipeUom}`
        );
        console.log(`      ├─ Allocated cost: ${allocatedCost.toFixed(2)}`);
        console.log(
          `      └─ Proportion: ${(
            (contribution / totalQtyDebited) *
            100
          ).toFixed(2)}%`
        );

        // Update the invItem with cost information
        const invItemIndex = record.invItems.findIndex(
          (item) => item.itemId === aggregatedItem.itemId
        );

        if (invItemIndex !== -1) {
          record.invItems[invItemIndex].cost = allocatedCost;
        }

        // Only add to updatedRecords once per record
        if (!processedRecordIds.has(record.id)) {
          processedRecordIds.add(record.id);

          // Update record-level information
          record.status = "completed";
          record.statusSummary = "Consumption completed";

          updatedRecords.push(record);
        }
      });
    }
  );

  return { updatedRecords, failedRecords };
};

//------------------------------------------------------//

/**
 * Preview batch costs with partial stock handling
 * Simulates FIFO/FEFO logic and handles all stock scenarios (full, partial, none)
 * Includes tax in cost calculations using taxRate
 */
const previewBatchCostsWithPartialStock = async (aggregationMap) => {
  const costPreviewResults = [];
  for (const [key, aggregatedItem] of Object.entries(aggregationMap)) {
    try {
      console.log(`\n🔸 Previewing: ${aggregatedItem.itemName} (${aggregatedItem.itemCode})`);
      console.log(`   Required quantity: ${aggregatedItem.totalRequiredQty} ${aggregatedItem.recipeUom}`);
      console.log(`   Work area: ${aggregatedItem.workArea}`);
      console.log(`   From ${aggregatedItem.records.length} tickets`);
      
      // Check available stock using aggregateQty
      const [aggregateResult] = await calculateAggregateQty(
        aggregatedItem.workAreaId,
        [{ itemId: aggregatedItem.itemId }]
      );
      
      const availableStock = aggregateResult.inStock;
      console.log(`   Available stock: ${availableStock} ${aggregatedItem.recipeUom}`);
      
      // Get available batches using FIFO/FEFO logic
      const ledgerBatches = await StockLedgerRepo.getAvailableBatches(
        aggregatedItem.itemId,
        aggregatedItem.workAreaId,
        null,
        null
      );

      let costPerUnit;
      let totalCost;
      let batchesToUse = [];
      let stockStatus;

      // Scenario 1: Fully available stock
      if (availableStock >= aggregatedItem.totalRequiredQty) {
        console.log(`   ✓ Stock Status: FULLY AVAILABLE`);
        stockStatus = "full";
        
        // Calculate cost from actual batches used (normal flow)
        let remainingQtyToConsume = aggregatedItem.totalRequiredQty;
        totalCost = 0;

        for (const batch of ledgerBatches) {
          if (remainingQtyToConsume <= 0) break;

          const qtyFromThisBatch = Math.min(
            batch.remainingQtyInRecipeUOM,
            remainingQtyToConsume
          );

          // Calculate tax per unit using taxRate
          const taxRate = batch.taxRate || 0;
          const taxPerUnit = (batch.unitCost * taxRate) / 100;
          const unitCostWithTax = batch.unitCost + taxPerUnit;
          const costFromThisBatch = unitCostWithTax * qtyFromThisBatch;

          batchesToUse.push({
            batchId: batch.id,
            itemCode: batch.itemCode,
            itemName: batch.itemName,
            pkgUOM: batch.pkgUOM,
            unitCost: batch.unitCost,
            taxRate: taxRate,
            taxPerUnit: taxPerUnit,
            unitCostWithTax: unitCostWithTax,
            qtyFromBatch: qtyFromThisBatch,
            remainingQtyInBatch: batch.remainingQtyInRecipeUOM,
            costFromBatch: costFromThisBatch,
            expiryDate: batch.expiryDate
          });

          totalCost += costFromThisBatch;
          remainingQtyToConsume -= qtyFromThisBatch;
        }

        costPerUnit = totalCost / aggregatedItem.totalRequiredQty;
      }
      // Scenario 2: Partially available stock
      else if (availableStock > 0 && availableStock < aggregatedItem.totalRequiredQty) {
        console.log(`   ⚠️  Stock Status: PARTIALLY AVAILABLE`);
        console.log(`   Shortfall: ${aggregatedItem.totalRequiredQty - availableStock} ${aggregatedItem.recipeUom}`);
        stockStatus = "partial";
        
        // Calculate cost per unit from available stock
        let availableCost = 0;
        let remainingQtyToConsume = availableStock;

        for (const batch of ledgerBatches) {
          if (remainingQtyToConsume <= 0) break;

          const qtyFromThisBatch = Math.min(
            batch.remainingQtyInRecipeUOM,
            remainingQtyToConsume
          );

          // Calculate tax per unit using taxRate
          const taxRate = batch.taxRate || 0;
          const taxPerUnit = (batch.unitCost * taxRate) / 100;
          const unitCostWithTax = batch.unitCost + taxPerUnit;
          const costFromThisBatch = unitCostWithTax * qtyFromThisBatch;

          batchesToUse.push({
            batchId: batch.id,
            itemCode: batch.itemCode,
            itemName: batch.itemName,
            pkgUOM: batch.pkgUOM,
            unitCost: batch.unitCost,
            taxRate: taxRate,
            taxPerUnit: taxPerUnit,
            unitCostWithTax: unitCostWithTax,
            qtyFromBatch: qtyFromThisBatch,
            remainingQtyInBatch: batch.remainingQtyInRecipeUOM,
            costFromBatch: costFromThisBatch,
            expiryDate: batch.expiryDate
          });

          availableCost += costFromThisBatch;
          remainingQtyToConsume -= qtyFromThisBatch;
        }

        // Calculate cost per unit from available stock
        costPerUnit = availableCost / availableStock;
        
        // Apply this cost per unit to the FULL required quantity
        totalCost = costPerUnit * aggregatedItem.totalRequiredQty;
        
        console.log(`   Available stock cost: ${availableCost.toFixed(2)} for ${availableStock} ${aggregatedItem.recipeUom}`);
        console.log(`   Cost per unit (incl. tax): ${costPerUnit.toFixed(4)} per ${aggregatedItem.recipeUom}`);
        console.log(`   Full requirement cost: ${totalCost.toFixed(2)} for ${aggregatedItem.totalRequiredQty} ${aggregatedItem.recipeUom}`);
      }
      // Scenario 3: No stock available (100% shortage)
      else {
        console.log(`   ❌ Stock Status: NO STOCK AVAILABLE (100% SHORTAGE)`);
        stockStatus = "none";
        
        // Get latest price reference using StockLedgerRepo helper
        const priceReference = await StockLedgerRepo.getLatestPriceReference(
          aggregatedItem.itemId,
          aggregatedItem.workAreaId,
          null
        );

        if (!priceReference) {
          throw new Error(`No price reference found for item ${aggregatedItem.itemName}`);
        }

        console.log(`   Using price reference from: ${priceReference.source.toUpperCase()}`);
        console.log(`   Price: ${priceReference.unitCost.toFixed(4)} per ${priceReference.recipeUOM}`);
        
        // Use price reference to calculate cost (already includes tax)
        costPerUnit = priceReference.unitCost;
        totalCost = costPerUnit * aggregatedItem.totalRequiredQty;
        
        // Create a virtual batch for reference
        batchesToUse = [{
          batchId: priceReference.id,
          itemCode: priceReference.itemCode || aggregatedItem.itemCode,
          itemName: priceReference.itemName || aggregatedItem.itemName,
          qtyFromBatch: 0, // No actual stock used
          costFromBatch: totalCost,
          source: priceReference.source,
          note: priceReference.source === "fallback" 
            ? `Fallback: ${priceReference.error}` 
            : "Price reference only - no stock available"
        }];

        console.log(`   Estimated cost per unit: ${costPerUnit.toFixed(4)} per ${aggregatedItem.recipeUom}`);
        console.log(`   Total estimated cost: ${totalCost.toFixed(2)} for ${aggregatedItem.totalRequiredQty} ${aggregatedItem.recipeUom}`);
      }

      console.log(`   ✓ Preview completed`);
      console.log(`   ├─ Batches referenced: ${batchesToUse.length}`);
      console.log(`   ├─ Total cost: ${totalCost.toFixed(2)}`);
      console.log(`   └─ Cost per unit: ${costPerUnit.toFixed(4)} per ${aggregatedItem.recipeUom}`);
      
      costPreviewResults.push({
        aggregationKey: key,
        aggregatedItem,
        batchesToUse,
        totalCost,
        costPerUnit,
        availableStock,
        requiredStock: aggregatedItem.totalRequiredQty,
        shortfall: Math.max(0, aggregatedItem.totalRequiredQty - availableStock),
        stockStatus,
        success: true
      });
      
    } catch (error) {
      console.error(`   ❌ Error previewing ${aggregatedItem.itemName}:`, error.message);
      
      costPreviewResults.push({
        aggregationKey: key,
        aggregatedItem,
        error: error.message,
        success: false
      });
    }
  }
  
  return costPreviewResults;
};

/**
 * Calculate consumption costs without debiting stock
 * Previews which batches would be used and calculates costs using FIFO/FEFO
 * Handles full stock, partial stock, and no stock scenarios
 * Updates records with costs but does NOT debit stock or create ledger entries
 */
const calculateConsumptionCosts = async (tenantId) => {
  try {
    console.log("\n" + "=".repeat(70));
    console.log("CONSUMPTION COST CALCULATION STARTED (PREVIEW MODE)");
    console.log("=".repeat(70));
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log("=".repeat(70));

    // Fetch consumption data from Firestore
    console.log("\n🔄 Fetching consumption data from Firestore...");
    const consumptionData = await getConsumptionData(tenantId);
    
    if (!consumptionData || consumptionData.length === 0) {
      console.log("ℹ️  No consumption data to process");
      return { 
        success: true, 
        message: "No pending consumption data found", 
        results: [] 
      };
    }

    console.log(`✓ Fetched ${consumptionData.length} pending consumption records`);

    // Step 1: Build aggregation map
    console.log("\n🔄 Step 1: Building aggregation map...");
    const aggregationMap = buildAggregationMap(consumptionData);
    console.log(`✓ Created ${Object.keys(aggregationMap).length} aggregated groups`);

    // Step 2: Preview batch usage and calculate costs (handles all stock scenarios)
    console.log("\n🔄 Step 2: Previewing batch usage and calculating costs...");
    const costPreviewResults = await previewBatchCostsWithPartialStock(aggregationMap);
    console.log(`✓ Previewed ${costPreviewResults.length} cost calculations`);

    // Step 3: Distribute costs back to original records
    console.log("\n🔄 Step 3: Distributing costs to original records...");
    const updatedRecords = distributeCostsFromPreview(
      consumptionData, 
      costPreviewResults
    );
    console.log(`✓ Calculated costs for ${updatedRecords.length} records`);

    // Step 4: Update Firestore with calculated costs
    let updateResult = null;
    if (updatedRecords.length > 0) {
      console.log("\n🔄 Step 4: Updating records in Firestore with costs...");
      updateResult = await updateConsumptionRecordsInFirestore(updatedRecords);
    } else {
      console.log("\n⚠️  Step 4: No records to update in Firestore");
    }

    console.log("\n" + "=".repeat(70));
    console.log("COST CALCULATION SUMMARY");
    console.log("=".repeat(70));
    console.log(`✓ Total records fetched: ${consumptionData.length}`);
    console.log(`✓ Unique items aggregated: ${Object.keys(aggregationMap).length}`);
    console.log(`✓ Cost calculations performed: ${costPreviewResults.length}`);
    console.log(`✓ Records updated with costs: ${updateResult?.successCount || 0}`);
    
    // Summary by stock status
    const fullStock = costPreviewResults.filter(r => r.stockStatus === "full").length;
    const partialStock = costPreviewResults.filter(r => r.stockStatus === "partial").length;
    const noStock = costPreviewResults.filter(r => r.stockStatus === "none").length;
    
    if (fullStock > 0) console.log(`   ├─ Full stock: ${fullStock}`);
    if (partialStock > 0) console.log(`   ├─ Partial stock: ${partialStock}`);
    if (noStock > 0) console.log(`   └─ No stock (used price reference): ${noStock}`);
    
    console.log(`⚠️  NOTE: Stock NOT debited - this is preview/costing only`);
    console.log("=".repeat(70));

    return {
      success: true,
      totalRecords: consumptionData.length,
      uniqueItems: Object.keys(aggregationMap).length,
      costCalculations: costPreviewResults.length,
      updatedRecords,
      costPreviewResults,
      updateResult,
      stockSummary: {
        fullStock,
        partialStock,
        noStock
      }
    };
    
  } catch (error) {
    console.error("\n❌ Error calculating consumption costs:", error);
    console.error("Stack trace:", error.stack);
    throw error;
  }
};

/**
 * Build aggregation map grouped by itemId and workAreaId
 */
const buildAggregationMap = (consumptionData) => {
  const aggregationMap = {};
  
  consumptionData.forEach((record) => {
    if (!record.invItems || record.invItems.length === 0) {
      console.warn(`⚠️  Record ${record.id} has no invItems, skipping...`);
      return;
    }

    record.invItems.forEach((item) => {
      const key = `${item.itemId}_${record.workAreaId}`;
      
      if (!aggregationMap[key]) {
        aggregationMap[key] = {
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          workAreaId: record.workAreaId,
          workArea: record.workArea,
          location: record.location,
          locationId: record.locationId,
          recipeUom: item.recipeUom,
          itemType: item.itemType,
          totalRequiredQty: 0,
          records: []
        };
      }
      
      aggregationMap[key].totalRequiredQty += item.requiredQty;
      
      aggregationMap[key].records.push({
        recordId: record.id,
        recordRef: record,
        itemContribution: item.requiredQty,
        invItem: item,
        ticketNo: record.ticketNo,
        soldQuantity: record.soldQuantity
      });
    });
  });

  console.log("\n📊 Aggregation Summary:");
  Object.entries(aggregationMap).forEach(([key, data]) => {
    console.log(`  ${data.itemName} (${data.itemCode})`);
    console.log(`    ├─ Location: ${data.workArea}`);
    console.log(`    ├─ Total Qty: ${data.totalRequiredQty} ${data.recipeUom}`);
    console.log(`    └─ Records: ${data.records.length} tickets`);
  });
  
  return aggregationMap;
};

/**
 * Distribute costs from preview results to original consumption records
 */
const distributeCostsFromPreview = (consumptionData, costPreviewResults) => {
  const updatedRecords = [];
  const processedRecordIds = new Set();
  
  costPreviewResults.forEach(({ 
    aggregationKey, 
    aggregatedItem, 
    batchesToUse, 
    totalCost, 
    costPerUnit, 
    availableStock,
    requiredStock,
    shortfall,
    stockStatus,
    error, 
    success 
  }) => {
    if (!success || error) {
      console.log(`\n⚠️  Skipping cost distribution for ${aggregatedItem.itemName} due to error`);
      return;
    }
    
    console.log(`\n💰 Distributing costs for ${aggregatedItem.itemName}:`);
    console.log(`   Stock status: ${stockStatus.toUpperCase()}`);
    console.log(`   Available: ${availableStock} ${aggregatedItem.recipeUom}`);
    console.log(`   Required: ${requiredStock} ${aggregatedItem.recipeUom}`);
    if (shortfall > 0) {
      console.log(`   Shortfall: ${shortfall} ${aggregatedItem.recipeUom}`);
    }
    console.log(`   Total cost: ${totalCost.toFixed(2)}`);
    console.log(`   Cost per unit: ${costPerUnit.toFixed(4)} per ${aggregatedItem.recipeUom}`);
    console.log(`   Distributing to ${aggregatedItem.records.length} records:`);
    
    // Distribute cost to each record proportionally
    aggregatedItem.records.forEach((recordInfo, index) => {
      const record = recordInfo.recordRef;
      const contribution = recordInfo.itemContribution;
      const allocatedCost = costPerUnit * contribution;
      
      console.log(`   ${index + 1}. Ticket #${record.ticketNo} (${record.id})`);
      console.log(`      ├─ Quantity: ${contribution} ${aggregatedItem.recipeUom}`);
      console.log(`      ├─ Allocated cost: ${allocatedCost.toFixed(2)}`);
      console.log(`      └─ Proportion: ${((contribution / aggregatedItem.totalRequiredQty) * 100).toFixed(2)}%`);
      
      // Update the invItem with cost information
      const invItemIndex = record.invItems.findIndex(
        item => item.itemId === aggregatedItem.itemId
      );
      
      if (invItemIndex !== -1) {
        record.invItems[invItemIndex].cost = allocatedCost;
      }
      
      // Only add to updatedRecords once per record
      if (!processedRecordIds.has(record.id)) {
        processedRecordIds.add(record.id);
        
        // Update record-level information
        record.status = "completed";
        
        // Set status summary based on stock status
        if (stockStatus === "full") {
          record.statusSummary = "Consumption costs calculated";
        } else if (stockStatus === "partial") {
          record.statusSummary = `Consumption costs calculated (partial stock: ${availableStock}/${requiredStock} ${aggregatedItem.recipeUom})`;
        } else {
          record.statusSummary = `Consumption costs calculated (no stock - used price reference)`;
        }
        
        // Store batch preview info for audit
        if (!record.batchPreview) {
          record.batchPreview = [];
        }
        record.batchPreview.push({
          itemId: aggregatedItem.itemId,
          itemName: aggregatedItem.itemName,
          itemCode: aggregatedItem.itemCode,
          totalCost: allocatedCost,
          costPerUnit: costPerUnit,
          stockStatus: stockStatus,
          availableStock: availableStock,
          requiredStock: contribution,
          shortfall: shortfall > 0 ? (contribution / aggregatedItem.totalRequiredQty) * shortfall : 0,
          batchesUsed: batchesToUse.map(b => ({
            batchId: b.batchId,
            qtyFromBatch: b.qtyFromBatch,
            costFromBatch: b.costFromBatch,
            source: b.source || "ledger"
          }))
        });
        
        updatedRecords.push(record);
      }
    });
  });
  
  return updatedRecords;
};



/**
 * Rerun consumption for a single error record
 * @param {string} tenantId
 * @param {string} id - consumption document id
 */
const rerunConsumption = async (tenantId, id) => {
  const record = await getConsumptionDataById(tenantId, id);

  if (!record) {
    throw new Error("Consumption record not found");
  }

  // Only error records are allowed
  if (record.status !== "error") {
    return {
      id: record.id,
      status: record.status,
      message: "Only error records can be re-run",
    };
  }

  // Reprocess record
  const updatedRecord = await reprocessConsumptionRecord(
    tenantId,
    record
  );

  // Persist changes
  await updateConsumptionRecord(record.id, updatedRecord);
  await calculateConsumptionCosts(tenantId);

  return {
    id: record.id,
    status: updatedRecord.status,
    statusSummary: updatedRecord.statusSummary,
  };
};

/**
 * Reprocess a stored consumption record
 * Uses same logic as initial processing
 * @param {string} tenantId
 * @param {object} record
 */
const reprocessConsumptionRecord = async (tenantId, record) => {
  // 1️⃣ Reset derived fields
  record.invItems = [];
  record.status = null;
  record.statusSummary = null;

  // 2️⃣ Fetch work areas
  const availableWorkAreas = await getRecordsByFloorId(
    tenantId,
    record.floorNo
  );

  // 3️⃣ Fetch recipe
  const recipe =
    record.type === "menu"
      ? await getMenuItemById(tenantId, record.menuItemId)
      : await getModifierById(tenantId, record.modifierId);

  // 4️⃣ Re-run core logic
  await processRecipeDetails(
    tenantId,
    record,
    recipe,
    availableWorkAreas,
    record.servingSizeId,
    record.soldQuantity
  );

  // 5️⃣ ✅ SET floorName (same logic as initial flow)
  if (record.workAreaId) {
    const requiredWA = availableWorkAreas.find(
      (wa) => wa.id === record.workAreaId
    );

    if (requiredWA && Array.isArray(requiredWA.floorIds)) {
      const requiredFloor = requiredWA.floorIds.find(
        (floor) => floor.id === record.floorNo
      );

      record.floorName = requiredFloor ? requiredFloor.name : "";
    } else {
      record.floorName = "";
    }
  } else {
    record.floorName = "";
  }

  return record;
};



module.exports = {
  createConsumptionData,
  rerunConsumption,
  processConsumptionData,
  calculateConsumptionCosts,
};
