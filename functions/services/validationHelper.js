const validationRepo = require("@/repositories/validationRepo.js");

/**
 * All constraints per module
 * Add or update rules here
 */
const moduleConstraints = {
  stores: [
    {
      name: "linkedWorkAreas",
      fn: validationRepo.getLocationCountWorkAreas,
      errorMessage: "Cannot deactivate: linked to {count} workarea(s)",
    }
  ],
  roles: [
    {
      name: "linkedUsers",
      fn: validationRepo.getRoleCountUsers,
      errorMessage: "Cannot deactivate: linked to {count} user(s)",
    },
  ],
  vendors: [
    {
      name: "linkedInventoryItems",
      fn: validationRepo.getVendorCountInventoryItems,
      errorMessage: "Cannot deactivate: linked to {count} inventory item(s)",
    }
  ],
  categories: [
    {
      name: "linkedInventoryItems",
      fn: validationRepo.getCategoryCountInventoryItems,
      errorMessage: "Cannot deactivate: linked to {count} inventory item(s)",
    },
  ],
  houseUnits: [
    {
      name: "linkedInventoryItems",
      fn: validationRepo.getHouseUnitsCountInventoryItems,
      errorMessage: "Cannot deactivate: linked to {count} inventory item(s)",
    },
    {
      name: "linkedRecipes",
      fn: validationRepo.getHouseUnitsCountRecipes,
      errorMessage: "Cannot deactivate: linked to {count} receipe(s)",
    },
  ],
  tags: [
    {
      name: "linkedInventoryItems",
      fn: validationRepo.getTagCountInventoryItems,
      errorMessage: "Cannot deactivate: linked to {count} inventory item(s)",
    },
    {
      name: "linkedRecipes",
      fn: validationRepo.getTagCountRecipes,
      errorMessage: "Cannot deactivate: linked to {count} recipe(s)",
    },
    {
      name: "linkedWorkAreas",
      fn: validationRepo.getTagCountWorkAreas,
      errorMessage: "Cannot deactivate: linked to {count} workarea(s)",
    }
  ],
  taxes: [
    {
      name: "linkedInventoryItems",
      fn: validationRepo.getTaxCountInventoryItems,
      errorMessage: "Cannot deactivate: linked to {count} inventory item(s)",
    },
  ],
  inventoryItems: [
    {
      name: "linkedRecipes",
      fn: validationRepo.getItemsCountRecipes,
      errorMessage: "Cannot deactivate: linked to {count} recipe(s)",
    }
  ]
};

/**
 * Runs validators sequentially and stops at the first failure.
 * @param {Array<{ fn: Function, args: Array, errorMessage: string }>} validators
 */
async function validate(validators) {
  for (const v of validators) {
    const count = await v.fn(...v.args);
    if (count > 0) {
      // Stop execution and throw first encountered error
      throw new Error(v.errorMessage.replace("{count}", count));
    }
  }
}

/**
 * Generic constraint checker for modules.
 * Checks all business constraints before performing actions
 * Stops at first violation.
 * @param {string} moduleName - e.g., "vendors", "menuItems"
 * @param {string} entityId - ID of the entity to validate
 */
async function checkConstraints(moduleName, tenantId, entityId) {
  const validators = moduleConstraints[moduleName]?.map((v) => ({
    fn: v.fn,
    args: [tenantId, entityId],
    errorMessage: v.errorMessage,
  }));

  if (!validators || validators.length === 0) return; // nothing to validate

  await validate(validators);
}

module.exports = {
  checkConstraints
};
