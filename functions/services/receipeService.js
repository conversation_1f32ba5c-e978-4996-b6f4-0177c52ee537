const {
    updateReceipeStatus
} = require("@/repositories/receipeRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Receipe operations
 * <PERSON>les activate and deactivate logic
 */
/**
 * Activate a Receipe
 * No validation needed for activation
 * @param {string} receipeId
 * @returns {object} activeStatus
 */
async function activateR<PERSON>eipe(tenantId, receipeId) {
    // Update status to active
    await updateReceipeStatus(tenantId, receipeId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a receipe
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} receipeId
 * @returns {object} activeStatus
 */
async function deactivateReceipe(tenantId, receipeId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("receipes", tenantId, receipeId);

    // Update status to inactive
    await updateReceipeStatus(tenantId, receipeId, false);
    return { activeStatus: false };
}

module.exports = {
    activateReceipe,
    deactivateRecei<PERSON>
};
