const {
    updateTagStatus
} = require("@/repositories/tagRepo");
const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for Vendor operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a vendor
 * No validation needed for activation
 * @param {string} vendorId
 * @returns {object} activeStatus
 */
async function activateTag(tenantId, tagId) {
    // Update status to active
    await updateTagStatus(tenantId, tagId, true);
    return { activeStatus: true };
}

/**
 * Deactivate a vendor
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} tagId
 * @returns {object} activeStatus
 */
async function deactivateTag(tenantId, tagId) {
    // Run validations sequentially (e.g., linked menu items/orders)
    await checkConstraints("tags", tenantId, tagId);

    // Update status to inactive
    await updateTagStatus(tenantId, tagId, false);
    return { activeStatus: false };
}

module.exports = {
    activateTag,
    deactivateTag
};
