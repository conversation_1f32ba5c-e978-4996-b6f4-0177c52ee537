const {
  updateProductLedgerStatus,
} = require("@/repositories/productLedgerRepo");
// const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for ProductLedger operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a ProductLedger
 * No validation needed for activation
 * @param {string} productLedgerId
 * @returns {object} activeStatus
 */
async function activateProductLedger(tenantId, productLedgerId) {
  // Update status to active
  await updateProductLedgerStatus(tenantId, productLedgerId, true);
  return { activeStatus: true };
}

/**
 * Deactivate a productLedgerId
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} productLedgerId
 * @returns {object} activeStatus
 */
async function deactivateProductLedger(tenantId, productLedgerId) {
  // Run validations sequentially (e.g., linked menu items/orders)
  //   await checkConstraints("vendors", tenantId, vendorId);

  // Update status to inactive
  await updateProductLedgerStatus(tenantId, productLedgerId, false);
  return { activeStatus: false };
}

module.exports = {
  activateProductLedger,
  deactivateProductLedger,
};
