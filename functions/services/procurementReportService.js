const {
  REPORTS,
  REPORT_INFORMATION,
  AGGREGATE_TYPES
} = require("@/defs/reportDefs");
const {
  validateAndPrepareFilters,
  requiresLedgerData,
  filterItem,
  constructColumns,
  validateIdentityFilters
} = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD
} = require("@/helpers/dateHelper");
const { paiseToRupee, truncateNumber } = require("@/utils/money");

const { fetchGRNReportData } = require("@/repositories/procurementReportRepo");

/**
 * NewProcurementReport
 * --------------------
 * Handles:
 * - GRN vs Item level identification
 * - Formatting
 * - Aggregation
 * - Rendering (JSON / Excel)
 */
class NewProcurementReport {
  #headers = [];
  #aggregateId = null;
  #detailedReport = false;
  #format;
  constructor(tenantId, payload, reportType, identity) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;
    this.#aggregateId = reportInfo.aggregateId;

    const { filters = {}, columns = [], options } = payload || {};

    let _filters = validateAndPrepareFilters(tenantId, filters);
    // ✅ identity-aware filtering
    if (identity) {
      _filters = validateIdentityFilters(identity, _filters);
    }
    // Expose everything upfront
    this._options = {
      _filters,
      columns,
      reportType,
      splitBy: options?.splitBy
    };

    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {
        grossAmount: 0,
        totalDiscount: 0,
        netAmount: 0,
        totalChargeAmount: 0,
        totalTaxAmount: 0,
        totalAmount: 0,
        totalCess: 0,
        totalFocAmount: 0
      },
      payload,
      _meta: {
        // _meta stores dynamic column IDs only (labels resolved later)
        taxes: {},
        charges: {}
      },
      expand: false
    };

    // Identify GRN-level vs Item-level processing
    this.#detailedReport = this.#identify(
      this._options._filters,
      reportInfo.id
    );

    // Choose formatter upfront
    this.#format = this.#detailedReport
      ? this.#formDetailedGRN
      : this.#formGRNSummary;
  }

  /**
   * Fetch and format raw data
   */
  async get() {
    const snapshot = await fetchGRNReportData(
      this.result.tenantId,
      this._options._filters
    );

    // Pure transformation: docs → formatted rows
    this.result.data = snapshot.docs.flatMap((doc) =>
      this.#format(doc.data())
    );

    return this.result.data;
  }

  /**
   * Convert paise values to rupees
   */
  #paiseToRupee = (v) => ({
    grossAmount: paiseToRupee(v.grossAmount),
    totalDiscount: paiseToRupee(v.totalDiscount),
    netAmount: paiseToRupee(v.netAmount),
    totalChargeAmount: paiseToRupee(v.totalChargeAmount),
    totalTaxAmount: paiseToRupee(v.totalTaxAmount),
    totalAmount: paiseToRupee(v.totalAmount),
    unitCost: v.unitCost ? paiseToRupee(v.unitCost) : null,
    totalCess: paiseToRupee(v.totalCess),
    totalFocAmount: paiseToRupee(v.totalFocAmount),
  });

  /**
   * Final formatting:
   * - Convert amounts
   * - Accumulate totals
   */
  #formatData() {
    let totalRow = {
      grossAmount: 0,
      totalDiscount: 0,
      netAmount: 0,
      totalChargeAmount: 0,
      totalTaxAmount: 0,
      totalAmount: 0,
      totalCess: 0,
      totalFocAmount: 0
    };

    const _meta = {
      taxes: Object.keys(this.result._meta.taxes),
      charges: Object.keys(this.result._meta.charges)
    };

    const data = this.result.data.map((v) => {
      const row = { ...v, ...this.#paiseToRupee(v) };

      row.subItems = row.subItems?.map((subItem) => ({
        ...subItem,
        ...this.#paiseToRupee(subItem),
      }));

      // Accumulate totals (paise-level)
      totalRow.grossAmount += v.grossAmount;
      totalRow.totalDiscount += v.totalDiscount;
      totalRow.netAmount += v.netAmount;
      totalRow.totalChargeAmount += v.totalChargeAmount;
      totalRow.totalTaxAmount += v.totalTaxAmount;
      totalRow.totalAmount += v.totalAmount;
      totalRow.totalCess += v.totalCess;
      totalRow.totalFocAmount += v.totalFocAmount;

      Object.keys(_meta).forEach((m) => {
        _meta[m].forEach((metaId) => {
          const k = `${m}_${metaId}`;
          totalRow[k] = truncateNumber((totalRow[k] || 0) + (v[k] || 0));
        });
      });

      return row;
    });

    return {
      data,
      totalRow: { ...totalRow, ...this.#paiseToRupee(totalRow) }
    };
  }

  /**
   * Generate final output
   */
  generate(resultType) {
    const aggregateId = this.#aggregateId;
    const headers = this.#headers;
    const options = this._options;
    const result = this.result;

    // Step 1: Primary aggregation
    if (aggregateId) {
      const aggregateConfig = AGGREGATE_TYPES[aggregateId];
      if (!aggregateConfig)
        throw new Error(`Invalid aggregate type: ${aggregateId}`);
      this.#aggregate(aggregateConfig);
    }

    // Step 2: Split-by aggregation (second pass)
    let splitByColumn = null;
    if (options.splitBy) {
      result.expand = true;

      const aggregateConfig = AGGREGATE_TYPES[options.splitBy];
      if (!aggregateConfig)
        throw new Error(`Invalid split-by type: ${options.splitBy}`);

      splitByColumn = aggregateConfig.label;
      this.#aggregate(aggregateConfig, true);
    }

    const { data, totalRow } = this.#formatData();

    result.data = data;
    result.totalRow = totalRow;

    // Build headers last (needs _meta + split info)
    result.headers = constructColumns(
      headers,
      options.columns,
      result._meta,
      splitByColumn,
      resultType
    );

    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result);
      default:
        return this.result;
    }
  }

  #addTaxes(taxes, res) {
    taxes.forEach((tax) => {
      const taxKey = `taxes_${tax.id}`;
      this.result._meta.taxes[tax.id] = tax.name;
      res[taxKey] = (res[taxKey] || 0) + tax.valueAmt;
    });
  }

  #getAmt(data) {
    let res = {
      grossAmount: data.grossAmount,
      totalDiscount: data.totalDiscount,
      netAmount: data.netAmount,
      totalChargeAmount: data.totalChargeAmount,
      totalTaxAmount: data.totalTaxAmount,
      totalAmount: data.totalAmount,
      totalCess: data.totalCess,
      totalFocAmount: data.totalFocAmount
    };

    this.#addTaxes(data.taxes, res);

    data.charges?.forEach((charge) => {
      const chargeKey = `charges_${charge.id}`;
      this.result._meta.charges[charge.id] = charge.name;
      res[chargeKey] = (res[chargeKey] || 0) + charge.valueAmt;
    });

    data.items.forEach((item) => {
      this.#addTaxes(item.taxes, res);
    });

    return res;
  }

  #getItemAmt(data) {
    let res = {
      grossAmount: data.grossAmount,
      totalDiscount: data.totalDiscount,
      totalCess: data.totalCess,
      totalTaxAmount: data.totalTaxAmount,
      totalChargeAmount: data.totalChargeAmount,
      totalAmount: data.totalAmount - data.totalFocAmount,
      totalFocAmount: data.totalFocAmount,
      netAmount: data.netAmount,
      taxes: data.taxes
    };

    this.#addTaxes(data.taxes, res);
    return res;
  }

  /**
   * Base GRN fields (no amounts)
   */
  #formGRNBase(grn) {
    return {
      grnNumber: grn.grnNumber,
      poNumber: grn.poNumber,
      invoiceNumber: grn.invoiceNumber,
      invoiceDate: FD.toFormattedDate(grn.invoiceDate, DATE_FORMAT.DATE_ONLY),
      vendorName: grn.vendorName,
      locationId: grn.location?.id,
      locationName: grn.location?.name,
      inventoryLocationId: grn.inventoryLocation?.id,
      inventoryLocationName: grn.inventoryLocation?.name,
      date: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY), // change date grn/invoice/created
      grnDate: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY),
      createdAt: FD.toFormattedDate(grn.createdAt),
      createdDate: FD.toFormattedDate(grn.createdAt, DATE_FORMAT.DATE_ONLY),
      createdTime: FD.toFormattedDate(grn.createdAt, DATE_FORMAT.TIME_ONLY),
      createdByName: grn.createdBy?.name || grn.receivedByName
    };
  }

  /**
   * GRN summary row (with amounts)
   */
  #formGRNSummary(grn) {
    return [
      {
        ...this.#formGRNBase(grn),
        ...this.#getAmt(grn),
      },
    ];
  }

  #filter(item) {
    return filterItem(this._options._filters, item);
  }

  /**
   * Item-level expansion
   */
  #formDetailedGRN(grn) {
    const results = [];
    const grnBase = this.#formGRNBase(grn);

    grn.items?.forEach((item) => {
      if (!this.#filter(item)) return;

      results.push({
        ...grnBase,
        itemId: item.itemId,
        itemName: item.itemName,
        itemCode: item.itemCode,
        hsnCode: item.hsnCode || "-",
        categoryId: item.categoryId,
        categoryName: item.categoryName || item.categoryId,
        subCategoryId: item.subcategoryId,
        subCategoryName: item.subcategoryName || item.subcategoryId,
        pkg: item.pkg?.id === "default" ? item.purchaseUOM : item.pkg?.name,
        pkgId: item.pkg?.id,
        qty: item.receivedQty,
        unitCost: item.unitCost,
        ...this.#getItemAmt(item),
      });
    });

    return results;
  }

  /**
   * Aggregation engine
   * - Supports GRN and Item level
   * - Computes Weighted Average Cost (WAC)
   */
  #aggregate({ id, label, columns }, detailed = false) {
    const map = new Map();
    const input = this.result.data;
    this.result.data = [];

    const extractFields = (data, columns = []) =>
      columns.reduce((acc, c) => ((acc[c] = data[c]), acc), {});

    const getKey = (data, id) =>
      Array.isArray(id)
        ? id.map((k) => data[k]).join("|")
        : data[id];

    const _meta = {
      taxes: Object.keys(this.result._meta.taxes),
      charges: Object.keys(this.result._meta.charges)
    };

    input.forEach((data) => {
      const key = getKey(data, id);

      const existing =
        map.get(key) ||
        {
          id: key,
          [label]: data[label],
          ...extractFields(data, columns),
          grossAmount: 0,
          totalDiscount: 0,
          netAmount: 0,
          totalChargeAmount: 0,
          totalTaxAmount: 0,
          totalAmount: 0,
          totalFocAmount: 0,
          totalCess: 0,

          // WAC fields
          qty: 0,
          unitCost: 0, // wac
          
          // internal value for wac calc
          wacValue: 0,
          entries: 0,

          // sub entries
          subItems: [],
        };

      existing.grossAmount += data.grossAmount;
      existing.totalDiscount += data.totalDiscount;
      existing.netAmount += data.netAmount;
      existing.totalChargeAmount += data.totalChargeAmount;
      existing.totalTaxAmount += data.totalTaxAmount;
      existing.totalFocAmount += data.totalFocAmount;
      existing.totalCess += data.totalCess;
      existing.totalAmount += data.totalAmount;
      existing.entries += 1;

      Object.keys(_meta).forEach((m) => {
        _meta[m].forEach((metaId) => {
          const k = `${m}_${metaId}`;
          existing[k] = truncateNumber((existing[k] || 0) + (data[k] || 0));
        });
      });

      // Weighted Average Cost
      existing.qty += data.qty || 0;
      existing.wacValue += data.unitCost || 0;
      existing.unitCost = existing.qty
        ? truncateNumber(existing.wacValue / existing.entries)
        : 0;

      if (detailed) {
        existing.subItems.push(data);
      }

      map.set(key, existing);
    });

    this.result.data = Array.from(map.values());
  }

  /**
   * Decide GRN vs Item level processing
   */
  #identify(filters, reportType) {
    const ledgerReports = [
      REPORTS.DETAILED_GRN,
      REPORTS.CATEGORY_WISE_GRN,
      REPORTS.SUB_CATEGORY_WISE_GRN,
      REPORTS.ITEM_WISE_GRN
    ];
    return ledgerReports.includes(reportType) || requiresLedgerData(filters);
  }
}

/**
 * GRN Summary Report
 * ------------------
 * Summarizes GRN details like invoice, vendor, total value, etc.
 */
const getGRNReport = async (tenantId, payload, resultType, identity) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Detailed GRN Report
 * -------------------
 * Provides line-item level GRN data with quantities and values.
 */
const getDetailedGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DETAILED_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Location Purchase Report
 * ------------------------
 * Summarizes purchase values per location.
 */
const getLocationWiseGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.LOCATION_WISE_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Vendor Purchase Report
 * ----------------------
 * Aggregates purchases grouped by vendor.
 */
const getVendorWiseGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.VENDOR_WISE_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Category Purchase Report
 * ------------------------
 * Shows total purchase amount per category.
 */
const getCategoryWiseGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.CATEGORY_WISE_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * SubCategory Purchase Report
 * ------------------------
 * Shows total purchase amount per sub-category.
 */
const getSubCategoryWiseGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SUB_CATEGORY_WISE_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Item Purchase Report
 * --------------------
 * Item-wise total purchases and quantities.
 */
const getItemWiseGRNReport = async (
  tenantId,
  payload,
  resultType,
  identity
) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.ITEM_WISE_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Daily Purchase Report
 * ---------------------
 * Date-wise total purchase summary.
 */
const getDailyGRNReport = async (tenantId, payload, resultType, identity) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DAILY_GRN,
    identity
  );
  await report.get();
  return report.generate(resultType);
};

module.exports = {
  getGRNReport,
  getDetailedGRNReport,
  getLocationWiseGRNReport,
  getVendorWiseGRNReport,
  getCategoryWiseGRNReport,
  getSubCategoryWiseGRNReport,
  getItemWiseGRNReport,
  getDailyGRNReport
};
