const admin = require("firebase-admin");
const db = admin.firestore();

const {
  fetchClosingItems,
  createClosing,
  fetchClosingData,
  getById,
  prepareClosingItems,
} = require("@/repositories/closingRepo");
const { getNextClosingId } = require("./counterService");
const schema = require("@/schema/closingSchema");
const { handleValidation } = require("@/utils/validation");
const {
  FirestoreDateHelper: FD,
  TIME_OPTION,
} = require("@/helpers/dateHelper");
const {
  getInventoryItemStocks,
  getBusinessDate,
  getStockMovementItems,
} = require("@/repositories/stockRepo");
const { LedgerTypes, StockLedgerReasonCode } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("./stockService");
const {
  applyLedgersToDailyStock,
  applyPhysicalClosing,
  recalculateStockFromDate,
} = require("./stockMovementsService");

const getClosingItemsRequest = async (tenantId, locationId) => {
  const closingItems = await fetchClosingItems(tenantId, locationId);
  return closingItems;
};

const getClosingById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      closingDate: FD.toFormattedDate(data.closingDate),
      closedBy: {
        ...data.closedBy,
        time: FD.toFormattedDate(data.closedBy.time),
      },
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const getClosingDataRequest = async (filters) => {
  const closingData = await fetchClosingData(filters);

  const result = closingData.map((doc) => ({
    closingNumber: doc.closingNumber,
    id: doc.id,
    closingDate: FD.toFormattedDate(doc.closingDate),
    locationName: doc.locationName,
    createdDate: FD.toFormattedDate(doc.closedBy.time),
    workAreaName: doc.workAreaName,
    closedBy: doc.closedBy.userName,
    stockCorrection: doc.stockCorrection,
  }));

  return result;
};

const createClosingRequest = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const closingNumber = await getNextClosingId(validatedData.tenantId);

    const ledgers = [];
    const today = FD.now(TIME_OPTION.START);
    const businessDateToday = getBusinessDate(today);
    const eventDate = FD.toFirestore(
      validatedData.closingDate,
      TIME_OPTION.START,
    );
    const businessDateEventDate = getBusinessDate(eventDate);

    const createdClosing = await db.runTransaction(async (trans) => {
      validatedData.items = await prepareClosingItems(
        validatedData.tenantId,
        validatedData.workAreaId,
        validatedData.items,
      );

      if (validatedData.stockCorrection) {
        const items =
          businessDateEventDate === businessDateToday
            ? await getInventoryItemStocks(
                validatedData.workAreaId,
                validatedData.items,
              )
            : await getStockMovementItems(
                validatedData.tenantId,
                validatedData.locationId,
                validatedData.workAreaId,
                businessDateEventDate,
                validatedData.items,
              );

        // CREDIT
        for (const item of items.filter(
          (i) => i.closingQtyInRecipeUOM > i.recipeQtyInStock,
        )) {
          const qty = item.convertedClosingQuantity - item.inStock;
          const qtyInRecipeUOM =
            item.closingQtyInRecipeUOM - item.recipeQtyInStock;

          const uom =
            item.pkg && item.pkg.id !== "default"
              ? item.pkg.name
              : item.purchaseUOM;

          const ledger = await creditStock(
            {
              ledgerType: LedgerTypes.ADJUSTMENT,
              reasonCode: StockLedgerReasonCode.PHYSICAL_STOCK_OVERRIDE,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              qtyInRecipeUOM,
              pkgUOM: uom,
              countingUOM: item.countingUOM || null,
              itemType: item.itemType || null,
              recipeUOM: item.recipeUOM || "",
              conversionFactor: item.conversionFactor || 1,
              unitCost: item.unitCost,
              totalCost: item.unitCost * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
              eventDate: validatedData.closingDate,
            },
            trans,
          );

          ledgers.push({
            ...ledger,
            qty: item.convertedClosingQuantity,
            recipeQty: item.closingQtyInRecipeUOM,
            totalCost: item.convertedClosingQuantity * item.unitCost,
          });
        }

        // DEBIT
        for (const item of items.filter(
          (i) => i.closingQtyInRecipeUOM < i.recipeQtyInStock,
        )) {
          const qty = item.inStock - item.convertedClosingQuantity;
          const qtyInRecipeUOM =
            item.recipeQtyInStock - item.closingQtyInRecipeUOM;

          const uom =
            item.pkg && item.pkg.id !== "default"
              ? item.pkg.name
              : item.purchaseUOM;

          const ledger = await debitStock(
            {
              ledgerType: LedgerTypes.ADJUSTMENT,
              reasonCode: StockLedgerReasonCode.PHYSICAL_STOCK_OVERRIDE,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              qtyInRecipeUOM,
              pkgUOM: uom,
              countingUOM: item.countingUOM || null,
              itemType: item.itemType || null,
              unitCost: item.unitCost,
              totalCost: item.unitCost * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
              eventDate: validatedData.closingDate,
            },
            trans,
          );

          ledgers.push({
            ...ledger,
            qty: item.convertedClosingQuantity,
            recipeQty: item.closingQtyInRecipeUOM,
            totalCost: item.convertedClosingQuantity * item.unitCost,
          });
        }
      }

      return createClosing({ ...validatedData, closingNumber }, trans);
    });

    /* --------------------------------------------------
       BUILD AFFECTED ITEMS (ALWAYS FROM CLOSING ITEMS)
    -------------------------------------------------- */

    const ledgerMap = new Map(
      ledgers.map((l) => [`${l.itemId}_${l.pkg?.id}`, l]),
    );

    const affectedItems = validatedData.items.map((i) => {
      const key = `${i.itemId}_${i.pkg?.id}`;
      const ledger = ledgerMap.get(key);

      return ledger
        ? {
            itemId: i.itemId,
            itemCode: i.itemCode,
            itemName: i.itemName,
            qty: ledger.qty,
            qtyInRecipeUOM: ledger.recipeQty,
            uom: ledger.pkgUOM,
            countingUOM: ledger.countingUOM || null,
            itemType: ledger.itemType || null,
            unitCost: ledger.unitCost,
            totalCost: ledger.totalCost,
            categoryId: ledger.categoryId,
            subcategoryId: ledger.subcategoryId,
            categoryName: ledger.categoryName,
            subcategoryName: ledger.subcategoryName,
            pkg: {
              id: ledger.pkg?.id,
              name: ledger.pkg?.name,
            },
          }
        : {
            itemId: i.itemId,
            itemCode: i.itemCode,
            itemName: i.itemName,
            qty: i.closingQuantity,
            qtyInRecipeUOM: i.closingQtyInRecipeUOM,
            uom: i.pkg?.name,
            countingUOM: i.countingUOM || null,
            itemType: i.itemType || null,
            unitCost: i.unitCost,
            totalCost: i.unitCost * i.closingQuantity,
            categoryId: i.categoryId,
            subcategoryId: i.subcategoryId,
            categoryName: i.categoryName,
            subcategoryName: i.subcategoryName,
            pkg: {
              id: i.pkg?.id,
              name: i.pkg?.name,
            },
          };
    });

    const infoData = {
      tenantId: validatedData.tenantId,
      locationId: validatedData.locationId,
      locationName: validatedData.locationName,
      inventoryLocationId: validatedData.workAreaId,
      inventoryLocationName: validatedData.workAreaName,
      eventDate,
    };

    // ✅ ALWAYS CALLED
    await applyPhysicalClosing(
      { ...infoData, items: affectedItems },
      validatedData.stockCorrection,
    );

    // ✅ ALWAYS CALLED
    await recalculateStockFromDate({
      ...infoData,
      startDate: eventDate,
      items: affectedItems.map((i) => `${i.itemId}_${i.pkg.id}`),
    });

    return createdClosing;
  } catch (error) {
    console.error("Error in createClosingRequest:", error);
    throw new Error(error.message);
  }
};

module.exports = {
  getClosingItemsRequest,
  createClosingRequest,
  getClosingDataRequest,
  getClosingById,
};
