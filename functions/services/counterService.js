const { COUNTER_TYPES } = require("@/defs/counterDefs");
const { getNextNumber } = require("@/repositories/counterRepo");

/**
 * Get next formatted ID for tenant + counter type
 * @param {string} tenantId - Tenant identifier
 * @param {import("@/defs/counterDefs").CounterType} counterType - One of COUNTER_TYPES
 * @returns {Promise<string>} Formatted ID (e.g., "P0001")
 */
const getNextId = async (tenantId, counterType) => {
  if (
    !counterType ||
    typeof counterType.key !== "string" ||
    typeof counterType.prefix !== "string"
  ) {
    throw new Error(`Invalid counter type: ${JSON.stringify(counterType)}`);
  }

  const updatedDoc = await getNextNumber(
    tenantId,
    counterType.key,
    counterType.prefix
  );

  if (!updatedDoc || typeof updatedDoc.nextNumber !== "number") {
    throw new Error(
      `Invalid counter document returned for tenant ${tenantId}, type ${counterType.key}`
    );
  }

  // Format number to 4 digits with leading zeros
  const formattedNumber = String(updatedDoc.nextNumber).padStart(4, "0");

  return `${updatedDoc.prefix}${formattedNumber}`;
};

module.exports = {
  /** @returns {Promise<string>} e.g., "PO0001" */
  getNextPurchaseOrderId: (tenantId) =>
    getNextId(tenantId, COUNTER_TYPES.PURCHASE_ORDER),

  /** @returns {Promise<string>} e.g., "PR0001" */
  getNextPurchaseRequestId: (tenantId) =>
    getNextId(tenantId, COUNTER_TYPES.PURCHASE_REQUEST),

  /** @returns {Promise<string>} e.g., "GRN0001" */
  getNextGrnId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.GRN),

  /** @returns {Promise<string>} e.g., "V0001" */
  getNextVendorId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.VENDOR),

  /** @returns {Promise<string>} e.g., "I0001" */
  getNextInventoryItemId: (tenantId) =>
    getNextId(tenantId, COUNTER_TYPES.INVENTORY_ITEM),

  /** @returns {Promise<string>} e.g., "R0001" */
  getNextRecipeId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.RECIPE),

  /** @returns {Promise<string>} e.g., "T0001" */
  getNextTransferId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.TRANSFER),

  /** @returns {Promise<string>} e.g., "DIS0001" */
  getNextDispatchId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.DISPATCH),

   /** @returns {Promise<string>} e.g., "PKG0001" */
  getNextPackageId: (tenantId) =>
    getNextId(tenantId, COUNTER_TYPES.PACKAGE),

  /** @returns {Promise<string>} e.g., "C0001" */
  getNextClosingId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.CLOSING),

  /** @returns {Promise<string>} e.g., "CT0001" */
  getNextContractId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.CONTRACT),

  /** @returns {Promise<string>} e.g., "SPI0001" */
  getNextSpoilageId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.SPOILAGE),

  /** @returns {Promise<string>} e.g., "ADJ0001" */
  getNextAdjustmentId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.ADJUSTMENT),

  /** @returns {Promise<string>} e.g., "MR0001" */
  getNextMenuRecipeId: (tenantId) => getNextId(tenantId, COUNTER_TYPES.MENU_RECIPE),
};
