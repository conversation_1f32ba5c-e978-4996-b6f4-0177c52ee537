const { updateReportGroupStatus } = require("@/repositories/reportGroupRepo");
// const { checkConstraints } = require("./validationHelper");

/**
 * Service layer for ReportGroup operations
 * Handles activate and deactivate logic
 */
/**
 * Activate a ReportGroup
 * No validation needed for activation
 * @param {string} reportGroupId
 * @returns {object} activeStatus
 */
async function activateReportGroup(tenantId, reportGroupId) {
  // Update status to active
  await updateReportGroupStatus(tenantId, reportGroupId, true);
  return { activeStatus: true };
}

/**
 * Deactivate a reportGroupId
 * Runs validation before deactivation
 * Stops at first validation error
 * @param {string} reportGroupId
 * @returns {object} activeStatus
 */
async function deactivateReportGroup(tenantId, reportGroupId) {
  // Run validations sequentially (e.g., linked menu items/orders)
  //   await checkConstraints("vendors", tenantId, vendorId);

  // Update status to inactive
  await updateReportGroupStatus(tenantId, reportGroupId, false);
  return { activeStatus: false };
}

module.exports = {
  activateReportGroup,
  deactivateReportGroup
};
