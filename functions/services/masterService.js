// src/services/masterService.js
const masterRepo = require("@/repositories/masterRepo");

async function getAllMasters(tenantId) {
  const [
    locations,
    inventoryLocations,
    { categories, subCategories },
    vendors,
    tags,
    taxes,
    charges,
    houseUnits,
    productLedgers,
    recipes,
  ] = await Promise.all([
    masterRepo.getLocations(tenantId),
    masterRepo.getInventoryLocations(tenantId),
    masterRepo.getCategoriesAndSubCategories(tenantId),
    masterRepo.getVendors(tenantId),
    masterRepo.getTags(tenantId),
    masterRepo.getTaxes(tenantId),
    masterRepo.getCharges(tenantId),
    masterRepo.getHouseUnits(tenantId),
    masterRepo.getProductLedgers(tenantId),
    masterRepo.getRecipes(tenantId),
  ]);

  return {
    locations,
    inventoryLocations,
    categories,
    subCategories,
    vendors,
    tags,
    taxes,
    charges,
    houseUnits,
    productLedgers,
    recipes,
  };
}

async function getInventoryMasters(tenantId) {
  return await masterRepo.getInventoryItems(tenantId);
}

module.exports = {
  getAllMasters,
  getInventoryMasters,
};
