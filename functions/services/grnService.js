// services/grnService.js

const {
  listGRNs,
  getGRNById,
  getGRNByNumber,
  updateAttachments,
  deleteAttachment,
  updateGRNDocument,
  applyStockActions,
} = require("@/repositories/grnRepo");
const {
  getLedgersByGRNId,
  findGrnItemPricesBetween,
  findLastGrnItemPrice,
} = require("@/repositories/stockLedgerRepo");
const { ResultTypes } = require("@/defs/resultTypeDefs");
const { paiseToRupee, rupeeToPaise } = require("@/utils/money");
const {
  TIME_OPTION,
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const {
  formatAmountToPaise,
  formatAmountToRupee,
} = require("@/helpers/amountFormat.js");

const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();

/**
 * Builds a detailed GRN document from a given GRN document.
 * Fetches ledger entries linked to the given GRN and transforms them into an array of items.
 * @param {object} grnDoc - GRN document to build the detailed document from.
 * @returns {Promise<object>} - Detailed GRN document with items or null if grnDoc is null.
 */
async function buildGRNDetail(grnDoc) {
  if (!grnDoc) return null;

  // Fetch ledger entries linked to this GRN
  const ledgers = await getLedgersByGRNId(grnDoc.grnId, grnDoc.tenantId);

  const items = ledgers.map((l) => {
    const data = {
      itemId: l.itemId,
      itemCode: l.itemCode,
      itemName: l.itemName,
      pkg: l.pkg,
      remarks: l.remarks,
      orderedQty: l.orderedQty,
      receivedQty: l.qty,
      uom: l.pkg?.id !== "default" ? l.pkg.name : l.countingUOM,
      unitCost: paiseToRupee(l.unitCost),
      totalAmount: paiseToRupee(l.totalCost),
      totalTaxAmount: paiseToRupee(l.taxAmount),
      totalDiscount: paiseToRupee(l.discount),
      taxRate: l.taxRate,
      foc: l.foc,
      totalCess: paiseToRupee(l.cess),
    };

    data.netAmount = data.unitCost * data.receivedQty - data.totalDiscount;

    return data;
  });

  return {
    ...grnDoc,
    items,
  };
}

/**
 * Retrieves a single GRN document by its ID.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {string} grnId - Unique identifier of the GRN document to retrieve.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
async function getGrnByID(grnId, tenantId) {
  const grnDoc = await getGRNById(grnId, tenantId);
  return grnDoc;
  // return await buildGRNDetail(grnDoc);
}

/**
 * Retrieves a single GRN document by its number.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {string} grnNumber - Unique GRN number of the GRN document to retrieve.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
async function getGrnByNumber(tenantId, grnNumber) {
  const grnDoc = await getGRNByNumber(tenantId, grnNumber);
  return await buildGRNDetail(grnDoc);
}

/**
 * Lists GRN summaries for the given tenantId, with optional filters for locationId, startDate, and endDate.
 * - Returns a list of GRN summaries in the given resultType (either "json" or "excel").
 * - If resultType is "excel", sets the Content-Disposition and Content-Type headers to return the GRN summaries as an Excel file.
 * @param {object} filters - Filters to apply when listing GRNs. Supports locationId, startDate, and endDate.
 * @param {string} resultType - Type of result to return. Either "json" or "excel".
 * @returns {Promise<object|Buffer>} - List of GRN summaries or Excel file containing the GRN summaries.
 */
async function listGrnSummaries(filters, resultType) {
  const grns = await listGRNs(filters);

  const result = grns.map((grn) => ({
    ...grn,
    grnDate: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY),
    invoiceDate: FD.toFormattedDate(grn.invoiceDate, DATE_FORMAT.DATE_ONLY),
    totalAmount: paiseToRupee(grn.totalAmount),
    grossAmount: paiseToRupee(grn.grossAmount),
    netAmount: paiseToRupee(grn.netAmount),
    createdAt: FD.toFormattedDate(grn.createdAt),
    roundoff: paiseToRupee(grn.roundoff),
  }));

  if (resultType === ResultTypes.EXCEL.toLowerCase()) {
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet("GRNs");

    sheet.columns = [
      { header: "GRN ID", key: "grnId" },
      { header: "GRN Number", key: "grnNumber" },
      { header: "Location", key: "locationName" },
      { header: "Vendor", key: "vendorName" },
      { header: "Total Value", key: "totalValue" },
      { header: "Created At", key: "createdAt" },
    ];

    result.forEach((grn) => sheet.addRow(grn));

    return await workbook.xlsx.writeBuffer();
  }

  return result;
}

async function fetchGrnItemPricesByPeriod({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) {
  return findGrnItemPricesBetween({
    tenantId,
    itemId,
    inventoryLocationId,
    pkgId,
  });
}

async function fetchLastGrnItemPrice({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) {
  return findLastGrnItemPrice({
    tenantId,
    itemId,
    inventoryLocationId,
    pkgId,
  });
}

const updateGrnAttachments = async (id, attachments) => {
  try {
    const updatedAttachments = await updateAttachments(id, attachments);
    return {
      success: true,
      message: `Updated successfully`,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

const deleteGrnAttachment = async (id, filePath) => {
  try {
    if (!filePath) throw new Error("File path required");

    const updatedAttachments = await deleteAttachment(id, filePath);

    return {
      success: true,
      message: `File deleted successfully`,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

const diffGrn = (oldGrn, newGrn) => {
  const changes = [];

  // Compare simple non-stock fields
  const simpleFields = [
    "grnDate",
    "invoiceNumber",
    "invoiceDate",
    // "remarks",
    // "attachments",
  ];

  for (const f of simpleFields) {
    if (
      Object.prototype.hasOwnProperty.call(newGrn, f) &&
      JSON.stringify(oldGrn[f]) !== JSON.stringify(newGrn[f])
    ) {
      changes.push({
        type: "FIELD_CHANGE",
        field: f,
        oldValue: oldGrn[f],
        newValue: newGrn[f],
      });
    }
  }

  // Key function using ordinal
  const keyFor = (item) => item.ordinal;
  const oldMap = new Map((oldGrn.items || []).map((it) => [keyFor(it), it]));
  const newMap = new Map((newGrn.items || []).map((it) => [keyFor(it), it]));

  // Detect removed or changed rows
  for (const [key, oldItem] of oldMap) {
    if (!newMap.has(key)) {
      changes.push({
        type: "ITEM_REMOVED",
        field: `items.${key}`,
        oldValue: oldItem,
        newValue: null,
      });
      continue;
    }

    const newItem = newMap.get(key);
    const editableItemFields = [
      "receivedQty",
      "unitCost",
      "pkg",
      "remarks",
      "totalDiscount",
      "totalTaxAmount",
      "totalCess",
      "totalFocAmount",
      "totalAmount",
      "taxes",
      "taxRate",
    ];

    for (const f of editableItemFields) {
      if (JSON.stringify(oldItem[f]) !== JSON.stringify(newItem[f])) {
        changes.push({
          type: "ITEM_FIELD_CHANGED",
          field: `items.${key}.${f}`,
          key,
          oldValue: oldItem[f],
          newValue: newItem[f],
          itemId: oldItem.itemId,
          pkgId: oldItem.pkg?.id,
        });
      }
    }
  }

  // Detect new rows
  for (const [key, newItem] of newMap) {
    if (!oldMap.has(key)) {
      changes.push({
        type: "ITEM_ADDED",
        field: `items.${key}`,
        oldValue: null,
        newValue: newItem,
      });
    }
  }

  return changes;
};

const getStockActions = (diffs, oldGrn, newGrn) => {
  const actions = [];

  const getItem = (list, key) => list.find((it) => it.ordinal === key);

  for (const d of diffs) {
    // A. New row = always stock IN
    if (d.type === "ITEM_ADDED") {
      actions.push({
        action: "STOCK_IN",
        item: d.newValue,
        qty: d.newValue.receivedQty,
        reason: "ITEM_ADDED",
      });
      continue;
    }

    // B. Removed row = always stock OUT
    if (d.type === "ITEM_REMOVED") {
      actions.push({
        action: "STOCK_OUT",
        item: d.oldValue,
        qty: d.oldValue.receivedQty,
        reason: "ITEM_REMOVED",
      });
      continue;
    }

    // C. Changed fields
    if (d.type === "ITEM_FIELD_CHANGED") {
      const key = d.key;
      const oldItem = getItem(oldGrn.items, key);
      const newItem = getItem(newGrn.items, key);

      // C1. Quantity changed
      if (d.field.endsWith(".receivedQty")) {
        const diff = (d.newValue || 0) - (d.oldValue || 0);

        if (diff > 0)
          actions.push({
            action: "STOCK_IN",
            item: newItem,
            qty: diff,
            reason: "QTY_INCREASE",
          });

        if (diff < 0)
          actions.push({
            action: "STOCK_OUT",
            item: oldItem,
            qty: Math.abs(diff),
            reason: "QTY_DECREASE",
          });
      }

      // C2. Package changed
      if (d.field.endsWith(".pkg")) {
        actions.push({
          action: "STOCK_OUT",
          item: oldItem,
          qty: oldItem.receivedQty,
          reason: "PKG_CHANGE_OUT",
        });

        actions.push({
          action: "STOCK_IN",
          item: newItem,
          qty: newItem.receivedQty,
          reason: "PKG_CHANGE_IN",
        });
      }

      // C3. Other financial fields changed
      if (
        [
          ".unitCost",
          ".totalAmount",
          ".totalDiscount",
          ".totalTaxAmount",
          ".totalCess",
          ".totalFocAmount",
          ".taxes",
          ".taxRate",
        ].some((suffix) => d.field.endsWith(suffix))
      ) {
        actions.push({
          action: "LEDGER_NOTE",
          item: newItem,
          field: d.field,
          oldValue: d.oldValue,
          newValue: d.newValue,
          qty: newItem.receivedQty,
          reason: "COST_CHANGE",
        });
      }
    }
  }
  return actions;
};

const validateChanges = (changes) => {
  const locked = ["location", "inventoryLocation"];

  for (const c of changes) {
    for (const lf of locked) {
      if (c.field === lf || c.field.startsWith(lf + ".")) {
        throw new Error(`Cannot modify locked GRN field: ${lf}`);
      }
    }
  }
};

const updateGRN = async ({ id, grn, editedById, editedByName }) => {
  try {
    const oldGrn = await getGRNById(id, grn.tenantId, null, false);

    // Step 1: Normalize incoming items
    const items = grn.items.map((item) => ({
      ordinal: item.ordinal,
      itemName: item.itemName,
      itemId: item.itemId,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      pkg: item.pkg,
      orderedQty: item.orderedQty,
      receivedQty: item.receivedQty,
      pkgUOM: item.pkgUOM || null,
      purchaseUOM: item.purchaseUOM,
      remarks: item.remarks || null,
      hsnCode: item.hsnCode,
      taxRate: item.taxRate,
      stockable: item.stockable,
      unitCost: rupeeToPaise(item.unitCost),
      foc: item.foc || false,
      ...formatAmountToPaise(item),
    }));
    const newGrn = {
      ...grn,
      items,
      updatedAt: FD.now(),
      editedById,
      editedByName,
      grnDate: FD.toFirestore(grn.grnDate, TIME_OPTION.START),
      invoiceDate: FD.toFirestore(grn.invoiceDate, TIME_OPTION.START),
      ...formatAmountToPaise(grn),
    };

    // Step 2: Diff old vs new
    const diffs = diffGrn(oldGrn, newGrn);

    // Step 3: Validate diff does not modify locked fields
    validateChanges(diffs);

    // Step 4: Determine stock actions
    const stockActions = getStockActions(diffs, oldGrn, newGrn);

    // Step 5: Perform all operations in one atomic transaction
    return db.runTransaction(async (t) => {
      await applyStockActions(stockActions, oldGrn, newGrn, t);
      await updateGRNDocument(id, oldGrn, newGrn, diffs, t);

      return {
        message: "GRN updated successfully",
        diffs,
        stockActions,
      };
    });

    // await updateGRNDocument(id, newGrn);
  } catch (err) {
    throw new Error(err.message);
  }
};

module.exports = {
  getGrnByID,
  getGrnByNumber,
  listGrnSummaries,
  fetchGrnItemPricesByPeriod,
  updateGrnAttachments,
  deleteGrnAttachment,
  fetchLastGrnItemPrice,
  updateGRN,
};
