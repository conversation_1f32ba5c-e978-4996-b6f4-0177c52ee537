const TEMP_TALLY_APP_ID = "3f6c1c7e-8e4a-4c5a-9c2a-1f7b6e3a9d21";

const validateAppId = (req, res, next) => {
  const appId = req.headers["app-id"];

  if (!appId) {
    return res.status(401).json({
      message: "Missing app-id header",
    });
  }

  if (appId !== TEMP_TALLY_APP_ID) {
    return res.status(401).json({
      message: "Invalid app-id",
    });
  }

  next();
};

module.exports = { validateAppId };
