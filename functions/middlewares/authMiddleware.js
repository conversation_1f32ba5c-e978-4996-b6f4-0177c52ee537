// middlewares/authMiddleware.js

/**
 * Firebase Authentication Middleware
 *
 * Provides:
 * 1. validateToken - verifies Firebase ID token, attaches identity to req.identity
 * 2. validateTenant - ensures the tenant in the route matches one of the tenant(s) in identity
 */

const jwt = require("jsonwebtoken");

const { getPrivileges } = require("./roleCache.js");

/**
 * Middleware 1: Validate Firebase ID Token and attach identity
 */
const validateToken = async (req, res, next) => {
  const authHeader = req.headers["authorization"];

  if (!authHeader) {
    return res.status(401).json({
      message: "No token, authorization denied",
    });
  }

  const token = authHeader.startsWith("Bearer ")
    ? authHeader.split(" ")[1]
    : authHeader;

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Attach identity — assumes `tenants` is in custom claims
    req.identity = {
      userName: decoded.userName,
      uid: decoded.userId,
      email: decoded.email,
      tenants: decoded.tenants || [], // array of tenants
      claimsIssuedAt: decoded.claimsIssuedAt,
    };

    next();
  } catch (err) {
    console.error("Token verification failed:", err.message);
    return res.status(401).json({
      message: "Invalid or expired token",
    });
  }
};

/**
 * Middleware 2: Validate tenant from route is included in identity.tenants
 */
const validateTenant = (req, res, next) => {
  const routeTenant = req.params.tenantId; // adjust if coming from query or body
  const identityTenants = req.identity?.tenants;

  if (!Array.isArray(identityTenants) || identityTenants.length === 0) {
    return res.status(403).json({
      message: "No tenants assigned to identity",
    });
  }

  const tenant = identityTenants.find((t) => t.id === routeTenant) || null;
  if (!tenant) {
    return res.status(403).json({
      message: "Tenant mismatch, access denied",
    });
  }

  const {
    isAdmin,
    allLocations,
    locationIds = [],
    allInventoryLocations,
    inventoryLocationIds = [],
  } = tenant.userDetails;

  req.identity = {
    ...req.identity,
    userId: tenant.userId,
    userName: tenant.userName,
    isAdmin,
    locations: !isAdmin && !allLocations ? locationIds : [],
    inventoryLocations:
      !isAdmin && !allInventoryLocations ? inventoryLocationIds : [],
    workAreaMap: tenant.workAreaMap,
  };

  next();
};

/**
 * Middleware: Validate that the user's tenant role includes the required privilege
 */

const checkPrivilege = (requiredPrivilege) => {
  return async (req, res, next) => {
    const tenantId = req.params.tenantId; // adjust if tenantId comes from query or body
    const identityTenants = req.identity?.tenants;
    const claimsIssuedAt = req.identity?.claimsIssuedAt; // clearer name

    if (!Array.isArray(identityTenants) || identityTenants.length === 0) {
      return res.status(403).json({
        message: "No tenants assigned to identity",
      });
    }

    // Find tenant entry in identity
    const tenantEntry = identityTenants.find((t) => t.id === tenantId);
    if (!tenantEntry) {
      return res.status(403).json({
        message: "Tenant mismatch, access denied",
      });
    }

    // check user is admin, no privileges required
    if (tenantEntry.isAdmin) {
      return next();
    }

    const { roleId } = tenantEntry;
    if (!roleId) {
      return res.status(403).json({
        message: "No role assigned for tenant",
      });
    }

    try {
      // Fetch privileges (cached + fallback to DB)
      const privileges = await getPrivileges(roleId, claimsIssuedAt);

      const hasPrivilege =
        Array.isArray(privileges) && privileges.includes(requiredPrivilege);

      if (!hasPrivilege) {
        return res.status(403).json({
          message: `Access denied. Missing privilege: ${requiredPrivilege}`,
        });
      }

      next();
    } catch (err) {
      console.error("Privilege validation error:", err);
      return res.status(500).json({
        message: "Failed to validate privileges",
      });
    }
  };
};

module.exports = {
  validateToken,
  validateTenant,
  checkPrivilege,
};
