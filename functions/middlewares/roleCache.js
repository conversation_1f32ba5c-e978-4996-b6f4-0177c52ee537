const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

/**
 * @typedef {Object} RoleCacheEntry
 * @property {string[]} privileges - List of privilege codes for the role
 * @property {number} lastUpdated - Timestamp (ms) of role's last update in Firestore
 * @property {number} cacheTime - Timestamp (ms) when this entry was cached
 */

/** 
 * In-memory cache for role privileges.
 * Key: roleId
 * Value: RoleCacheEntry
 */
const roleCache = {};

/**
 * Tracks inflight Firestore fetches per roleId to prevent redundant requests.
 * Key: roleId
 * Value: Promise<string[]> - resolves to privileges array
 */
const inflightFetches = {};

/** Cache and cleanup configuration */
const CACHE_TTL_MS = 60 * 60 * 1000; // 1 hour: time after which cache entry is considered stale
const CLEANUP_INTERVAL_MS = 30 * 60 * 1000; // 30 minutes: how often to cleanup expired cache entries

/**
 * Periodic cleanup of expired role cache entries.
 * This prevents unbounded memory growth in long-running Cloud Function instances.
 */
setInterval(() => {
    const now = Date.now();
    for (const roleId in roleCache) {
        if (now - roleCache[roleId].cacheTime > CACHE_TTL_MS) {
            delete roleCache[roleId];
        }
    }
    console.log("Role cache cleanup done");
}, CLEANUP_INTERVAL_MS);

/**
 * Fetch privileges for a role, using cache with Firestore fallback.
 *
 * @param {string} roleId - Firestore role document ID
 * @param {number} claimsIssuedAt - Timestamp (ms) from JWT indicating when identity was issued
 * @returns {Promise<string[]>} - Array of privilege codes
 */
async function getPrivileges(roleId, claimsIssuedAt) {
    const now = Date.now();
    const cached = roleCache[roleId];

    // If cache exists and is up-to-date for this token, return cached privileges
    if (cached && cached.lastUpdated <= claimsIssuedAt) {
        return cached.privileges;
    }

    // If a fetch is already in progress for this roleId, wait for it
    if (inflightFetches[roleId]) {
        return inflightFetches[roleId];
    }

    // Firestore fetch
    inflightFetches[roleId] = (async () => {
        const doc = await db.collection(COLLECTIONS.ROLE).doc(roleId).get();
        if (!doc.exists) throw new Error(`Role ${roleId} not found`);

        const data = doc.data();
        const privileges = data.privileges || [];
        const lastUpdated = data.lastUpdated ? data.lastUpdated.toMillis() : now;

        // Update in-memory cache
        roleCache[roleId] = { privileges, lastUpdated, cacheTime: now };

        // Remove inflight marker
        delete inflightFetches[roleId];

        return privileges;
    })();

    return inflightFetches[roleId];
}

module.exports = {
    getPrivileges
};
