const { validateModuleData } = require("@/data/validator.js");
const { getError } = require("@/helpers/errorHandler");

const vendorData = [
  { name: "ABC Ltd", contactNo: "12345", cinNo: "L12345AB1234ABC123" },
  { name: "XYZ Ltd", contactNo: "9876543210", cinNo: "L12345AB1234ABC123456" }
];

const categoryData = [
  { name: "Electronics", status: "Active" },
  { name: "", status: "Inactive" }
];

const existingVendors = ["XYZ Ltd"];
const existingCategories = ["Electronics"];

function alreadyPresentMessage(value, column) {
  return getError("COMMON.ALREADY_PRESENT", { field: value, column }).message;
}

const vendorChecks = {
  name: (value, _row, column) => existingVendors.includes(value)
    ? [alreadyPresentMessage(value, column)]
    : [],
};

const categoryChecks = {
  name: (value, _row, column) => existingCategories.includes(value)
    ? [alreadyPresentMessage(value, column)]
    : [],
};

const modulesToValidate = [
  { name: "vendor", data: vendorData, checks: vendorChecks },
  { name: "category", data: categoryData, checks: categoryChecks }
];

modulesToValidate.forEach(({ name, data, checks }) => {
  const result = validateModuleData(name, data, checks);
  console.log(`\nValidation Results for ${name.toUpperCase()}:`);
  console.log(JSON.stringify(result, null, 2));
});
