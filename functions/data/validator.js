const validationRules = require("@/data/validationRules.json");
const { getError } = require("@/helpers/errorHandler");

const compiledValidationRules = Object.keys(validationRules).reduce((compiled, moduleName) => {
  compiled[moduleName] = {};
  const moduleRules = validationRules[moduleName] || {};

  for (const fieldName in moduleRules) {
    const rule = moduleRules[fieldName];
    compiled[moduleName][fieldName] = {
      ...rule,
      regex: new RegExp(rule.pattern)
    };
  }

  return compiled;
}, {});

function validateField(module, key, value, row) {
  const rule = compiledValidationRules[module]?.[key];
  const errors = [];

  if (rule && rule.regex && !rule.regex.test(value == null ? "" : String(value))) {
    const { message } = getError(rule.errorCode, { ...row, column: key });
    errors.push(message);
  }

  return errors;
}

function validateRow(module, row, rowIndex, customChecks) {
  const errorMap = { row: rowIndex + 1 };

  for (const key in row) {
    const value = row[key];
    let fieldErrors = validateField(module, key, value, row);

    if (customChecks && typeof customChecks[key] === "function") {
      const extraErrors = customChecks[key](value, row, key);
      fieldErrors.push(...extraErrors);
    }

    if (fieldErrors.length) {
      errorMap[key] = fieldErrors;
    }
  }

  return Object.keys(errorMap).length > 1 ? errorMap : null;
}

function validateModuleData(module, data, customChecks = {}) {
  return data
    .map((row, index) => validateRow(module, row, index, customChecks))
    .filter(Boolean);
}

module.exports = { validateModuleData };
