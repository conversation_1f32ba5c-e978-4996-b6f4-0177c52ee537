const express = require("express");
const router = express.Router({ mergeParams: true });
const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const adjustmentController = require("@/controllers/adjustmentController");

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.ADJUSTMENT),
  adjustmentController.getAdjustmentById
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.ADJUSTMENT),
  adjustmentController.getAdjustments
);
router.post(
  "/create",
  checkPrivilege(PRIV_CODES.ADJ_EDIT),
  adjustmentController.insertAdjustment
);
router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.ADJUSTMENT),
  adjustmentController.updateAdjustment
);

module.exports = router;
