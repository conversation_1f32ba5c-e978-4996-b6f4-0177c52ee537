const express = require("express");
const router = express.Router({ mergeParams: true });

const chargeController = require("@/controllers/chargeController");

// Create a new charge
// POST /tenants/:tenantId/charges
router.post("/", chargeController.createCharge);

// Get all charges (List)
// GET /tenants/:tenantId/charges
router.get("/", chargeController.getCharges);

// Update an existing charge
// PUT /tenants/:tenantId/charges/:id
router.put("/:id", chargeController.updateCharge);

// Get charge by ID
// GET /tenants/:tenantId/charges/:id
router.get("/:id", chargeController.getChargeById);

// Activate a charge
// PUT /tenants/:tenantId/charges/:id/activate
router.put("/:id/activate", chargeController.activateCharge);

// Deactivate a charge
// PUT /tenants/:tenantId/charges/:id/deactivate
router.put("/:id/deactivate", chargeController.deactivateCharge);

module.exports = router;

