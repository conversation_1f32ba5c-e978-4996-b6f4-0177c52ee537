const express = require("express");
const router = express.Router({ mergeParams: true });
const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const menuRecipeController = require("@/controllers/menuRecipeController");

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.PREPARATION),
  menuRecipeController.getMenuRecipeById
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.PREPARATION),
  menuRecipeController.getMenuRecipes
);
router.post(
  "/create",
  checkPrivilege(PRIV_CODES.PRE_EDIT),
  menuRecipeController.insertMenuRecipe
);

router.post(
  "/:id/split",
  checkPrivilege(PRIV_CODES.PRE_EDIT),
  menuRecipeController.getMenuRecipeSplit
);

router.post(
  "/sales-reduction",
  menuRecipeController.salesReduction
);

router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.PREPARATION),
  menuRecipeController.updateMenuRecipe
);

module.exports = router;
