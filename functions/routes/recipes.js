const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const receipeController = require("@/controllers/receipeController");

router.get("/:id", checkPrivilege(PRIV_CODES.PC_VIEW), receipeController.getReceipeById);
router.get("/", checkPrivilege(PRIV_CODES.PC_VIEW), receipeController.getReceipes);
router.post("/", checkPrivilege(PRIV_CODES.PC_EDIT), receipeController.insertReceipe);
router.put("/:id", checkPrivilege(PRIV_CODES.PC_EDIT), receipeController.updateReceipe);

router.put("/:id/activate", checkPrivilege(PRIV_CODES.PC_EDIT), receipeController.activateReceipe);
router.put("/:id/deactivate", checkPrivilege(PRIV_CODES.PC_EDIT), receipeController.deactivateReceipe);

module.exports = router;
