const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const storeController = require("@/controllers/storeController");

router.put(
  "/:id/activate",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.activateStore,
);
router.put(
  "/:id/deactivate",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.deactivateStore,
);

router.get(
  "/:id/floors",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.getFloorsByStoreId,
);
router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.updateStore,
);

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.getStoreById,
);
router.get("/", checkPrivilege(PRIV_CODES.SET_LOC), storeController.getStores);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.SET_LOC),
  storeController.insertStores,
);

module.exports = router;
