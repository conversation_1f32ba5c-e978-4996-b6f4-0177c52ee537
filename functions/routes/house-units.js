const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const houseUnitController = require("@/controllers/houseUnitController");

router.post(
  "/get-house-units",
  checkPrivilege(PRIV_CODES.PC_VIEW),
  houseUnitController.getHouseUnits
);
router.post("/related", houseUnitController.getRelatedHouseUnits);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  houseUnitController.insertHouseUnit
);
router.get(
  "/",
  checkPrivilege(PRIV_CODES.PC_VIEW),
  houseUnitController.getAllHouseUnits
);
router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.PC_VIEW),
  houseUnitController.getHouseUnitById
);

router.put(
  "/:id/activate",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  houseUnitController.activateHouseunit
);
router.put(
  "/:id/deactivate",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  houseUnitController.deactivateHouseunit
);

router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  houseUnitController.updateHouseUnit
);
router.delete(
  "/:id",
  checkPrivilege(PRIV_CODES.PC_EDIT),
  houseUnitController.deleteHouseunit
);

module.exports = router;
