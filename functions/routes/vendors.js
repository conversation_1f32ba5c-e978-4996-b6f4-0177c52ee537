const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const vendorController = require("@/controllers/vendorController");

router.get("/:id", checkPrivilege(PRIV_CODES.PC_VIEW), vendorController.getVendorById);
router.get("/", checkPrivilege(PRIV_CODES.PC_VIEW), vendorController.getVendors);
router.post("/", checkPrivilege(PRIV_CODES.PC_EDIT), vendorController.insertVendor);

router.put("/:id/activate", checkPrivilege(PRIV_CODES.PC_EDIT), vendorController.activateVendor);
router.put("/:id/deactivate", checkPrivilege(PRIV_CODES.PC_EDIT), vendorController.deactivateVendor);

router.put("/:id", checkPrivilege(PRIV_CODES.PC_EDIT), vendorController.updateVendor);

module.exports = router;
