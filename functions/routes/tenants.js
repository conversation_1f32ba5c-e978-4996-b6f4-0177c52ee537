const express = require("express");
const app = express();
const cors = require("cors");
app.use(cors({ origin: true }));

const tenantController = require("@/controllers/tenantController");
const accountController = require("@/controllers/accountController");
const storeController = require("@/controllers/storeController");
const menuItemController = require("@/controllers/menuItemController");
const modifierController = require("@/controllers/modifierController");

app.get("/", tenantController.getTenants);

app.post("/:id/linkAccounts", accountController.linkAccount);
app.post("/", tenantController.insertTenants);

app.put("/:id/activate", tenantController.updateStatusActive);
app.put("/:id/inactivate", tenantController.updateStatusInactive);

app.put("/:id", tenantController.updateTenant);

// from pos sync
app.post("/:id/stores", storeController.insertStores);
app.post("/:id/menu-items", menuItemController.insertMenuItems);
app.post("/:id/modifiers", modifierController.insertModifiers);

module.exports = app;
