const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const userController = require("@/controllers/userController");

router.get("/:id", checkPrivilege(PRIV_CODES.SET_USER), userController.getUserById);
router.get("/", checkPrivilege(PRIV_CODES.SET_USER), userController.getUsers);
router.post("/", checkPrivilege(PRIV_CODES.SET_USER), userController.insertUser);
router.put("/:id", checkPrivilege(PRIV_CODES.SET_USER), userController.updateUser);
router.put("/:id/activateStatus", checkPrivilege(PRIV_CODES.SET_USER), userController.updateUserActiveStatus);

module.exports = router;
