// routes/dashboards.js
const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const dashboardController = require("@/controllers/dashboardController.js");

// -------------------
// Dashboard Routes
// -------------------

// Inventory Dashboard
router.post("/inventory", checkPrivilege(PRIV_CODES.DASH_INV), dashboardController.getInventory);

// Purchase Dashboard
router.post("/purchases", checkPrivilege(PRIV_CODES.DASH_PUR), dashboardController.getPurchases);

// COGS Dashboard
router.post("/cogs", checkPrivilege(PRIV_CODES.DASH_COGS), dashboardController.getCogs);

module.exports = router;
