const express = require("express");
const router = express.Router({ mergeParams: true });
const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const closingController = require("@/controllers/closingController");

router.post(
  "/items",
  checkPrivilege(PRIV_CODES.CLOSING),
  closingController.getClosingItems
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.CLOSING),
  closingController.getClosingData
);
router.post(
  "/create",
  checkPrivilege(PRIV_CODES.CLO_EDIT),
  closingController.createClosing
);
router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.CLOSING),
  closingController.getClosingById
);

module.exports = router;
