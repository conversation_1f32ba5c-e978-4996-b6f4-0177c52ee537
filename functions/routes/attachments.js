const express = require("express");
const router = express.Router({ mergeParams: true }); 

const { PRIV_CODES } = require("@/defs/privilegeDefs");
const { checkPrivilege } = require('@/middlewares/authMiddleware');
const ATTACHMENT_TYPES  = require('@/defs/attachmentTypesDefs');

const { generateUploadUrls, generateReadUrl } = require('@/controllers/attachmentsController');

// Middleware factory
const validate = (type) => {
  return (req, res, next) => {
    // Strict type check
    if (!type || !Object.values(ATTACHMENT_TYPES).includes(type)) {
      return res.status(400).json({ error: 'Invalid attachment type' });
    }

    // Privilege mapping
    const privilegeMap = {
      [ATTACHMENT_TYPES.GRN]: PRIV_CODES.PUR_GRN,
      [ATTACHMENT_TYPES.CONTRACT]: PRIV_CODES.CONTRACT,
    };

    // Run privilege check dynamically
    checkPrivilege(privilegeMap[type])(req, res, next);
  };
};

/**
 * POST /attachments/url
 * Body: { type: 'grn' | 'contract', files: [...] }
 */
router.post('/url', (req, res) => {
  const { type } = req.body;
  validate(type)(req, res, () => generateUploadUrls(req, res));
});


/**
 * GET /attachments/read-url
 * Query: ?type=grn&key=file.pdf
 */
router.get('/read-url', (req, res) => {
  const { type } = req.query;
  validate(type)(req, res, () => generateReadUrl(req, res));
});

module.exports = router;
