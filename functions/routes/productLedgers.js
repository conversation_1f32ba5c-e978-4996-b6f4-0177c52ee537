const express = require("express");
const router = express.Router({ mergeParams: true });

const productLedgerController = require("@/controllers/productLedgerController");

router.post("/", productLedgerController.createProductLedger);
router.get("/", productLedgerController.getProductLedgers);
router.get("/:id", productLedgerController.getProductLedgerById);
router.put("/:id", productLedgerController.updateProductLedger);

router.put("/:id/activate", productLedgerController.activateProductLedger);
router.put("/:id/deactivate", productLedgerController.deactivateProductLedger);

module.exports = router;
