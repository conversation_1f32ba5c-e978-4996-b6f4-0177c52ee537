const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const categoryController = require("@/controllers/categoryController");

router.get("/:id", checkPrivilege(PRIV_CODES.PC_VIEW), categoryController.getCategory);
router.get("/", checkPrivilege(PRIV_CODES.PC_VIEW), categoryController.getCategories);
router.post("/:id/subcategories", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.createSubCategory);
router.post("/", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.insertCategory);

router.put("/:id/activate", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.activateCategory);
router.put("/:id/deactivate", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.deactivateCategory);

router.put("/:id", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.updateCategory);

router.delete("/:id", checkPrivilege(PRIV_CODES.PC_EDIT), categoryController.deleteCategory);

module.exports = router;
