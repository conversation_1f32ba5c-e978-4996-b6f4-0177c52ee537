const express = require("express");
const router = express.Router({ mergeParams: true });

const reportGroupController = require("@/controllers/reportGroupController");

router.post("/", reportGroupController.createReportGroup);
router.get("/", reportGroupController.getReportGroups);
router.get("/:id", reportGroupController.getReportGroupById);
router.put("/:id", reportGroupController.updateReportGroup);

router.put("/:id/activate", reportGroupController.activateReportGroup);
router.put("/:id/deactivate", reportGroupController.deactivateReportGroup);

module.exports = router;
