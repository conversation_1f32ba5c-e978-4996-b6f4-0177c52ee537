const express = require("express");
const router = express.Router({ mergeParams: true });

const taxController = require("@/controllers/taxController");

// Create a new tax
// POST /
router.post("/", taxController.createTax);

router.get("/", taxController.getActiveTaxes);

// Update an existing tax
// PUT /:id
router.put("/:id", taxController.updateTax);

// Get tax by ID
// GET /:id
router.get("/:id", taxController.getTaxById);

// Activate a tax
// PUT /:id/activate
router.put("/:id/activate", taxController.activateTax);

// Inactivate a tax
// PUT /:id/inactivate
router.put("/:id/deactivate", taxController.deactivateTax);

module.exports = router;
