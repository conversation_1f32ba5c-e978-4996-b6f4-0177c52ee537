const express = require("express");
const router = express.Router({ mergeParams: true });

const tagController = require("@/controllers/tagController");

router.post("/", tagController.createTag);
router.get("/", tagController.getTags);
router.get("/:id", tagController.getTagById);
router.put("/:id", tagController.updateTag);

router.put("/:id/activate", tagController.activateTag);
router.put("/:id/deactivate", tagController.deactivateTag);

module.exports = router;
