const express = require("express");
const router = express.Router({ mergeParams: true });
const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const transferController = require("@/controllers/transferController");

router.post(
  "/",
  checkPrivilege(PRIV_CODES.TRANSFER),
  transferController.getTransfers
);
router.post(
  "/create",
  checkPrivilege(PRIV_CODES.TRA_EDIT),
  transferController.createTransfer
);
router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.TRANSFER),
  transferController.getTransferById
);
router.put(
  "/:id/dispatch",
  checkPrivilege(PRIV_CODES.TRA_DISPATCH),
  transferController.dispatchTransfer
);
router.put(
  "/:id/receive",
  checkPrivilege(PRIV_CODES.TRA_RECEIVE),
  transferController.receiveTransfer
);
router.put("/:id/close", transferController.closeTransfer);

router.get(
  "/:id/pdf",
  //   checkPrivilege(PRIV_CODES.PUR_PO),
  transferController.exportToPDF
);

router.get(
  "/:id/xlsx",
  //   checkPrivilege(PRIV_CODES.PUR_PO),
  transferController.exportToXLSX
);

module.exports = router;
