const express = require("express");
const router = express.Router({ mergeParams: true });

const menuItemController = require("@/controllers/menuItemController");

router.get("/", menuItemController.getMenuItems);
router.post("/", menuItemController.insertMenuItems);
router.get("/sync", menuItemController.syncMenuItemFromBO);
router.get("/:id", menuItemController.getMenuItemById);
router.put("/:id", menuItemController.updateMenuItem);

module.exports = router;
