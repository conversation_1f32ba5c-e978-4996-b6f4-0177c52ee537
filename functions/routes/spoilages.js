const express = require("express");
const router = express.Router({ mergeParams: true });
const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const spoilageController = require("@/controllers/spoilageController");

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.SPOILAGE),
  spoilageController.getSpoilageById
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.SPOILAGE),
  spoilageController.getSpoilages
);
router.post(
  "/create",
  checkPrivilege(PRIV_CODES.SPO_EDIT),
  spoilageController.insertSpoilage
);
router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.SPOILAGE),
  spoilageController.updateSpoilage
);

module.exports = router;
