/**
 * Procurement Reports Routes
 * --------------------------
 * Routes for tenant-level procurement reports such as GRN, vendor, and purchase summaries.
 */

const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const procurementReportController = require("@/controllers/procurementReportsController");

// All routes assume :tenantId is provided in the parent route
// Example: /:tenantId/reports/procurements/...

// GRN Summary Report
router.post(
    "/grn-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getGRNReport
);

// Detailed GRN Report
router.post(
    "/grn-detailed-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getDetailedGRNReport
);

// Location Purchase Report
router.post(
    "/grn-location-wise-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getLocationWiseGRNReport
);

// Vendor Purchase Report
router.post(
    "/grn-vendor-wise-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getVendorWiseGRNReport
);

// Category Purchase Report
router.post(
    "/grn-category-wise-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getCategoryWiseGRNReport
);

// Category Purchase Report
router.post(
    "/grn-sub-category-wise-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getSubCategoryWiseGRNReport
);

// Item Purchase Report
router.post(
    "/grn-item-wise-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getItemWiseGRNReport
);

// Daily Purchase Report
router.post(
    "/grn-daily-report",
    checkPrivilege(PRIV_CODES.REP_GRN),
    procurementReportController.getDailyGRNReport
);

module.exports = router;
