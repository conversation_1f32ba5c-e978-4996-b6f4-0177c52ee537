const express = require("express");
const router = express.Router({ mergeParams: true });

const contractController = require("@/controllers/contractController");

router.get("/:id", contractController.getContractById);
router.get("/", contractController.getContracts);
router.post("/", contractController.insertContract);

router.put("/:id", contractController.updateContract);
router.put("/:id/close", contractController.closeContract);

router.put('/:id/attachments', contractController.updateContractAttachments);
router.delete("/:id/attachments", contractController.deleteContractAttachment);

module.exports = router;
