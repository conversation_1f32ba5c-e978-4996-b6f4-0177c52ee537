const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const roleController = require("@/controllers/roleController");

router.get("/get-privileges", checkPrivilege(PRIV_CODES.SET_USER), roleController.getPrivileges);

router.post("/", checkPrivilege(PRIV_CODES.SET_USER), roleController.createRole);
router.get("/", checkPrivilege(PRIV_CODES.SET_USER), roleController.getRoles);
router.get("/get-by-id/:id", checkPrivilege(PRIV_CODES.SET_USER), roleController.getRoleById);
router.put("/:id", checkPrivilege(PRIV_CODES.SET_USER), roleController.updateRole);
router.delete("/:id", checkPrivilege(PRIV_CODES.SET_USER), roleController.deleteRole);

router.put("/:id/activate", checkPrivilege(PRIV_CODES.SET_USER), roleController.activateRole);
router.put("/:id/deactivate", checkPrivilege(PRIV_CODES.SET_USER), roleController.deactivateRole);

module.exports = router;
