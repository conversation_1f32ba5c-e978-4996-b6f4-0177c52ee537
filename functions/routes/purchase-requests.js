const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const purchaseRequestController = require("@/controllers/purchaseRequestController");

router.post(
  "/create",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.createPurchaseRequest
);
router.post(
  "/:id/approve",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.approvePurchaseRequest
);
router.post(
  "/:id/reject",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.rejectPurchaseRequest
);
router.post(
  "/:id/close",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.closePurchaseRequest
);
router.post(
  "/:id/convert-to-purchase-order",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.convertToPurchaseOrder
);

router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.updatePurchaseRequest
);
router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.getPurchaseRequestById
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.getPurchaseRequests
);

router.get(
  "/:id/pdf",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.exportToPDF
);

router.get(
  "/:id/xlsx",
  checkPrivilege(PRIV_CODES.PUR_PR),
  purchaseRequestController.exportToXLSX
);

module.exports = router;
