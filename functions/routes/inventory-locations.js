const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const inventoryLocationController = require("@/controllers/inventoryLocationController");

router.get("/:id", checkPrivilege(PRIV_CODES.PC_VIEW), inventoryLocationController.getInventoryLocationById);
router.get("/", checkPrivilege(PRIV_CODES.PC_VIEW), inventoryLocationController.getInventoryLocations);
router.get("/store-locations/:storeId", checkPrivilege(PRIV_CODES.PC_VIEW), inventoryLocationController.getStoreLocations);
router.post("/", checkPrivilege(PRIV_CODES.PC_EDIT), inventoryLocationController.insertInventoryLocation);
router.put("/:id", checkPrivilege(PRIV_CODES.PC_EDIT), inventoryLocationController.updateInventoryLocation);

router.put("/:id/activate", checkPrivilege(PRIV_CODES.PC_EDIT), inventoryLocationController.activateLocation);
router.put("/:id/deactivate", checkPrivilege(PRIV_CODES.PC_EDIT), inventoryLocationController.deactivateLocation);

router.put("/store-locations/:storeId/approval-config", checkPrivilege(PRIV_CODES.PC_EDIT),inventoryLocationController.updateApprovalConfig)

module.exports = router;
