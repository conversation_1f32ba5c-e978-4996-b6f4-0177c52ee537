NODE_ENV=staging

# Firebase Admin (service account)
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=inventory-staging-8f59d
FIREBASE_KEY_ID=693e37733d2cb71b7652ed73cc971c0426932db5
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=106484945132585862229
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40inventory-staging-8f59d.iam.gserviceaccount.com
FIREBASE_UNIVERSE_DOMAIN=googleapis.com

AUTH_SERVER_URL="https://asia-south1-staging-sign-hub.cloudfunctions.net/app"

# Firebase Client Config
FIREBASE_API_KEY=AIzaSyD1z8ErS9TeACjhqHKIYwsshpJDAu0o5U4
FIREBASE_AUTH_DOMAIN=inventory-staging-8f59d.firebaseapp.com
FIREBASE_PROJECT_ID_CONFIG=inventory-staging-8f59d
FIREBASE_STORAGE_BUCKET=inventory-staging-8f59d.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:bac68229dcf2766fc48f86
FIREBASE_MEASUREMENT_ID=G-99GC3M885P

JWT_SECRET=a56f888e807c51fe

# BO Authorization
BO_URI="https://staging-bo-api.digitory.com/api/v1/inventory"
BO_APP_ID= "staging-inventory"
BO_APP_CODE="4ce972bc-1d8b-4bb2-a155-67bf04d60241"

#web application URI
WEB_APP_URI="https://staging-inventory.digitory.com"
CLIENT_ID="inventory"