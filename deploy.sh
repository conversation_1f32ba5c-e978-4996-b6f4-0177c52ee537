#!/usr/bin/env bash
set -e

if [ -z "$1" ]; then
  echo "Usage: ./deploy.sh [staging|prod]"
  exit 1
fi

ENV=$1
FUNCTION_DIR="functions"   # folder where your cloud fn lives

case "$ENV" in
  staging)
    GCP_PROJECT="inventory-staging-8f59d"
    ENV_FILE=".env.staging"
    NPM_CMD="npm run deploy:staging"
    ;;
  prod)
    GCP_PROJECT="inventory-production-3b6ba"
    ENV_FILE=".env.prod"
    NPM_CMD="npm run deploy:prod"
    ;;
  *)
    echo "Invalid env: $ENV (use staging or prod)"
    exit 1
    ;;
esac

echo "Entering $FUNCTION_DIR..."
cd "$FUNCTION_DIR" || exit 1

echo "Switching GCP project to $GCP_PROJECT"
#gcloud config set project "$GCP_PROJECT"

if [ ! -f "$ENV_FILE" ]; then
  echo "Missing $ENV_FILE"
  exit 1
fi


echo "Preparing env file..."
rm -f .env.production
cp "$ENV_FILE" .env.production

echo "Deploying to $ENV..."
$NPM_CMD

# echo "Returning to root..."
cd -

echo "Deployment finished."
