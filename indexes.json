{"indexes": [{"collectionGroup": "adjustments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "adjustmentNumber", "order": "DESCENDING"}, {"fieldPath": "requestedBy.time", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "adjustments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "adjustments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "category", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "category", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "charges", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "closing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "workAreaId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "closing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "closedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "closing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "closingNumber", "order": "DESCENDING"}, {"fieldPath": "closedBy.time", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "closing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "closing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "stockCorrection", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "endDate", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "endDate", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "attachments", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vendorId", "order": "ASCENDING"}, {"fieldPath": "poId", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "houseUnits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "houseUnits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "toUnit", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "houseUnits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "importExportLogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "startedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "menuRecipe", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "productLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseOrders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseOrders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseOrders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseOrders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "purchaseRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "receipes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "receipes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "spoilages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "spoilages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "spoilages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "spoilageNumber", "order": "DESCENDING"}, {"fieldPath": "requestedBy.time", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "grnMeta.id", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "grnMeta.id", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "transactionType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "remainingQty", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "remainingQty", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "remainingQty", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "transactionType", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenant.id", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "tag", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "taxes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "from.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "from.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "to.id", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "issuer.id", "order": "ASCENDING"}, {"fieldPath": "requester.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "issuer.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "requester.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "dispatchStatus", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "receiveStatus", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "to.id", "order": "ASCENDING"}, {"fieldPath": "requestedBy.time", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "vendors", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}, {"collectionGroup": "vendors", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}], "density": "SPARSE_ALL"}], "fieldOverrides": []}